package orders

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	orders "github.com/terang-ai/backend-service/controller/orders"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	OrderCtrl := orders.NewOrderController(db, redis)

	OrderBaseGroup := r.Group("/v1")
	{
		OrderBaseGroup.GET("/orders", OrderCtrl.GetAllOrders)
		OrderBaseGroup.POST("/orders", OrderCtrl.InsertOrder)
	}

	ordersGroup := r.Group("/v1/orders/:id")
	{
		// orders routes
		ordersGroup.GET("", OrderCtrl.GetOneOrder)
		ordersGroup.PUT("", OrderCtrl.UpdateOrder)
		ordersGroup.DELETE("", OrderCtrl.DeleteOrder)
	}

}
