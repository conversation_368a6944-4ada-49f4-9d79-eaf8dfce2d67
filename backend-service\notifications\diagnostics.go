package notifications

import (
	"context"
	"fmt"
	"os"
	"time"
)

// DiagnosticResult contains the results of configuration and connection tests
type DiagnosticResult struct {
	ConfigSource   string // "env" or "file"
	Domain         string
	HasAPI<PERSON>ey      bool
	HasSender      bool
	HasRecipients  bool
	ConnectionTest error
}

func (n *MigrationNotifier) RunDiagnostics() (*DiagnosticResult, error) {
	result := &DiagnosticResult{}

	// Get current configuration
	config, err := GetMailgunConfig()
	if err != nil {
		return nil, fmt.Errorf("configuration error: %v", err)
	}

	// Determine config source and validate
	if config.Mailgun.Domain == os.Getenv("MAILGUN_DOMAIN") {
		result.ConfigSource = "env"
	} else {
		result.ConfigSource = "file"
	}

	// Record configuration status
	result.Domain = config.Mailgun.Domain
	result.HasAPIKey = config.Mailgun.APIKey != ""
	result.HasSender = config.Mailgun.Sender != ""
	result.HasRecipients = len(config.Mailgun.Recipients) > 0

	// Test Mailgun connection using the existing notifier's client
	// instead of creating a new one
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	_, err = n.mg.GetDomain(ctx, config.Mailgun.Domain)
	result.ConnectionTest = err

	return result, nil
}

// Validate checks if the notifier is properly configured
func (n *MigrationNotifier) Validate() error {
	if n.mg == nil {
		return fmt.Errorf("mailgun client not initialized")
	}
	if n.sender == "" {
		return fmt.Errorf("sender email not configured")
	}
	if len(n.recipients) == 0 {
		return fmt.Errorf("no recipients configured")
	}
	return nil
}
