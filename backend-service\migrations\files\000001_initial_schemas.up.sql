-- USER
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_tier') THEN
        CREATE TYPE user_tier AS ENUM (
            'REGULAR',
            'VERIFIED',
            'ADVANCED',
            'PRO',
            'EXPERT',
            'ELITE',
            'PREMIUM',
            'MASTER',
            'VIP',
            'GOLD',
            'PLATINUM',
            'D<PERSON>MOND',
            'ULTIMATE',
            'LEGEND'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM (
            'user',
            'admin',
            'auditor',
            'reader',
            'moderator',
            'superadmin',
            'contributor',
            'editor',
            'guest',
            'developer',
            'support'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'timezone_offset') THEN
        CREATE TYPE timezone_offset AS ENUM (
            '+00:00',  -- UTC
            '+01:00',  -- Central European Time
            '+02:00',  -- Eastern European Time
            '+03:00',  -- Moscow Time
            '+04:00',  -- Gulf Standard Time
            '+05:00',  -- Pakistan Standard Time
            '+06:00',  -- Bangladesh Standard Time
            '+07:00',  -- Asia/Jakarta
            '+08:00',  -- China Standard Time
            '+09:00',  -- Japan Standard Time
            '+10:00',  -- Australian Eastern Standard Time
            '+11:00',  -- Solomon Islands Time
            '+12:00',  -- New Zealand Standard Time
            '-01:00',  -- Azores Time
            '-02:00',  -- South Georgia Time
            '-03:00',  -- Argentina Time
            '-04:00',  -- Atlantic Time
            '-05:00',  -- Eastern Standard Time
            '-06:00',  -- Central Standard Time
            '-07:00',  -- Mountain Standard Time
            '-08:00',  -- Pacific Standard Time
            '-09:00',  -- Alaska Standard Time
            '-10:00',  -- Hawaii-Aleutian Standard Time
            '-11:00',  -- Samoa Time
            '-12:00'   -- Baker Island Time
        );
    END IF;
END $$;

-- ALTER TABLE public.users 
--     ALTER COLUMN timezone DROP DEFAULT,
--     ALTER COLUMN timezone TYPE public.timezone_offset USING timezone::public.timezone_offset,
--     ALTER COLUMN timezone SET DEFAULT '+00:00',
--     ALTER COLUMN timezone SET NOT NULL;


CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    username VARCHAR(255) UNIQUE, -- Allow NULL for SSO users
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NULL,    -- Allow NULL for SSO users
    first_name VARCHAR(255) NULL,
    last_name VARCHAR(255) NULL,
    picture VARCHAR(1000) NULL,
    banner VARCHAR(1000) NULL,
    last_login TIMESTAMPTZ NULL,
    role user_role DEFAULT 'user',
    tier user_tier DEFAULT 'REGULAR',
    personal_website VARCHAR(255) NULL,
    phone_number VARCHAR(20) NULL,
    address VARCHAR(500) NULL,
    date_of_birth DATE NULL,
    country VARCHAR(255) NULL,
    bio TEXT NULL,
    preferences JSONB NULL,
    social_links JSONB NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_banned BOOLEAN DEFAULT FALSE,
    timezone timezone_offset DEFAULT '+00:00',
    consent_marketing BOOLEAN DEFAULT TRUE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255) NULL,
    last_modified_by VARCHAR(26) NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_logins (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    login_timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NULL,  -- Supports both IPv4 and IPv6
    user_agent TEXT NULL,
    location VARCHAR(255) NULL,  -- This can be a city, country, or any other relevant location info
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS user_profile_analytics (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    views INT DEFAULT 0,
    last_viewed TIMESTAMPTZ NULL,
    CONSTRAINT fk_user_analytics FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS user_relationships (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    follower_id VARCHAR(26) NOT NULL,
    followee_id VARCHAR(26) NOT NULL,
    followed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_follower FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_followee FOREIGN KEY (followee_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE (follower_id, followee_id)
);

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_token_status') THEN
        CREATE TYPE user_token_status AS ENUM (
            'AVAILABLE',
            'EXPIRED'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS user_verification_requests
(
    id         BIGSERIAL NOT NULL PRIMARY KEY,
    email      VARCHAR(255) NOT NULL,
    token      VARCHAR(255) NOT NULL,
    status     user_token_status DEFAULT 'AVAILABLE',
    expires    TIMESTAMPTZ NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL '10 minutes'),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_verification_requests FOREIGN KEY (email) REFERENCES users(email) ON DELETE CASCADE
);

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_email_username ON users (email, username);
CREATE INDEX IF NOT EXISTS idx_email ON users (email);

-- AVAILABLE EXAMS
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visibility_status') THEN
        CREATE TYPE visibility_status AS ENUM (
            'PUBLIC',
            'DRAFT'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS available_exams (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subname VARCHAR(255) NOT NULL,
    description BYTEA NOT NULL,
    baseline_price NUMERIC(18, 2),
    visibility visibility_status DEFAULT 'DRAFT',
    duration INTERVAL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR(26) NOT NULL,
    CONSTRAINT fk_user_available_exams FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Available Exams table indexes
CREATE INDEX IF NOT EXISTS idx_available_exams_baseline_price_name ON available_exams (baseline_price, name, created_at);
CREATE INDEX IF NOT EXISTS idx_available_exams_user_id ON available_exams (user_id);

CREATE TABLE IF NOT EXISTS exam_question_answer_hints (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    exam_id VARCHAR(26) NOT NULL,
    data JSONB NULL,
    passing_grade JSONB NULL,
    CONSTRAINT fk_exam_available_exams FOREIGN KEY (exam_id) REFERENCES available_exams(id) ON DELETE CASCADE
);

-- Exam Question Answer Hints table indexes
CREATE INDEX IF NOT EXISTS idx_exam_qah_exam_id ON exam_question_answer_hints (exam_id);

-- MEDIA

CREATE TABLE IF NOT EXISTS media (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    url VARCHAR(1000) NOT NULL,
    available_exam_id VARCHAR(26) NOT NULL,
    size_in_bytes INT NULL,
    mime_type VARCHAR(100) NULL,
    width INT NULL,
    height INT NULL,
    uploaded_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_available_exam_media FOREIGN KEY (available_exam_id) REFERENCES available_exams(id) ON DELETE CASCADE
);

-- Media table indexes
CREATE INDEX IF NOT EXISTS idx_media_id ON media(id);

-- TOS
CREATE TABLE IF NOT EXISTS terms_of_services (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    content BYTEA NOT NULL,
    user_id VARCHAR(26) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_tos FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS exam_terms_of_services (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    exam_id VARCHAR(26) NOT NULL,
    terms_of_service_id VARCHAR(26) NOT NULL, -- unique 1 exam has 1 tos
    FOREIGN KEY (exam_id) REFERENCES available_exams(id),
    FOREIGN KEY (terms_of_service_id) REFERENCES terms_of_services(id) ON DELETE CASCADE
);

-- CATEGORY
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    image_url VARCHAR(1000) NULL,
    size_in_bytes INT NULL,
    mime_type VARCHAR(100) NULL,
    width INT NULL,
    height INT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Categories table indexes
CREATE INDEX IF NOT EXISTS idx_categories_id ON categories(id);

CREATE TABLE IF NOT EXISTS exam_categories (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    exam_id VARCHAR(26) NOT NULL,
    category_id VARCHAR(26) NOT NULL,
    FOREIGN KEY (exam_id) REFERENCES available_exams(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Exam Categories table indexes
CREATE INDEX IF NOT EXISTS idx_exam_categories_exam_id ON exam_categories (exam_id);
CREATE INDEX IF NOT EXISTS idx_exam_categories_category_id ON exam_categories (category_id);

-- TAGS
CREATE TABLE IF NOT EXISTS tags (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS exam_tags (
    exam_id VARCHAR(26) NOT NULL,
    tag_id VARCHAR(26) NOT NULL,
    PRIMARY KEY (exam_id, tag_id),
    FOREIGN KEY (exam_id) REFERENCES available_exams(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- Exam Tags table indexes
CREATE INDEX IF NOT EXISTS idx_exam_tags_exam_id ON exam_tags (exam_id);
CREATE INDEX IF NOT EXISTS idx_exam_tags_tag_id ON exam_tags (tag_id);

-- ORDERS AND PAYMENT
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'order_status') THEN
        CREATE TYPE order_status AS ENUM (
            'PENDING', -- Order is placed but not yet paid for.
            'PAYMENT_PROCESSING',  -- Added for payment processing stage
            'PAYMENT_RECEIVED', -- Received by system
            'IN_PROGRESS',  -- Artist working on the order
            'REVISION_REQUESTED',  -- Client requested revisions
            'REVISIONS_COMPLETED',  -- Artist completed revisions
            'COMPLETED',
            'CANCELLED'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    client_id VARCHAR(26) NOT NULL,
    exam_id VARCHAR(26) NOT NULL,
    quantity INTEGER NOT NULL,
    status order_status NOT NULL,  -- Using the created enum type
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES available_exams(id) ON DELETE CASCADE
);

-- PAYMENTS
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
        CREATE TYPE invoice_status AS ENUM (
            'PENDING',
            'PAID', -- client to artist
            'PARTIALLY_PAID', -- system to artist or client any time
            'CANCELLED', -- system to client when refund cancel order
            'OVERDUE'
        );
    END IF;
END $$;
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_status') THEN
        CREATE TYPE payment_status AS ENUM (
            'PENDING',
            'PROCESSING',
            'PAID', -- client to artist
            'REFUNDED', -- system to client when refund cancel order
            'WITHDRAWN', -- system to artist or client any time
            'FAILED',
            'EXPIRED'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS invoices (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    order_id VARCHAR(26) NOT NULL,
    status invoice_status DEFAULT 'PENDING',
    invoice_date TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS payments (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    invoice_id VARCHAR(26),
    payment_method VARCHAR(255) NOT NULL, -- e.g., Credit Card, Bank Transfer, PayPal
    amount DECIMAL(18,2),
    status payment_status DEFAULT 'PENDING',  -- Example: 'pending', 'processing', 'succeeded', 'failed'
    payment_start_date TIMESTAMPTZ NULL,
    payment_end_date TIMESTAMPTZ NULL,
    transaction_id VARCHAR(255) NULL,  -- Ensure unique transaction ID from payment gateway if any
    payment_link VARCHAR(500) NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);


-- EXAM SESSIONS
-- EXAM SESSION STATUS ENUM
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_session_status') THEN
        CREATE TYPE exam_session_status AS ENUM (
            'ACTIVE',
            'COMPLETED',
            'ABANDONED'
        );
    END IF;
END $$;
-- EXAM SESSION TYPE ENUM
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_session_type') THEN
        CREATE TYPE exam_session_type AS ENUM (
            'PRACTICE',
            'EXAM'
        );
    END IF;
END $$;

-- EXAM SESSIONS TABLE
CREATE TABLE IF NOT EXISTS exam_sessions (
    id VARCHAR(26) NOT NULL PRIMARY KEY,  -- ULID for the session ID
    session_id VARCHAR(26) NOT NULL UNIQUE,  -- Additional ULID for tracking the session
    user_id VARCHAR(26) NOT NULL,  -- Foreign key to identify the user taking the exam
    exam_id VARCHAR(26) NOT NULL,  -- Foreign key to link the session with a specific exam
    type exam_session_type NOT NULL DEFAULT 'EXAM',  -- Type of session: PRACTICE or EXAM
    status exam_session_status DEFAULT 'ACTIVE',  -- Status of the session
    start_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,  -- When the session started
    end_time TIMESTAMPTZ NULL,  -- When the session ended
    answers JSONB,  -- JSONB column to store the user's answers
    flagged_questions JSONB,  -- JSONB column flagged questions
    subject VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_exam_sessions FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_exam_exam_sessions FOREIGN KEY (exam_id) REFERENCES available_exams(id) ON DELETE CASCADE
);

-- Exam Sessions table indexes
CREATE INDEX IF NOT EXISTS idx_exam_sessions_answers ON exam_sessions USING gin (answers);

-- EXAM SCORES TABLE
CREATE TABLE IF NOT EXISTS exam_scores (
    id VARCHAR(26) NOT NULL PRIMARY KEY,  -- ULID for the score entry
    session_id VARCHAR(26) NOT NULL,  -- Foreign key to link the score to an exam session
    total_questions INT NOT NULL,  -- Total number of questions in the exam
    correct_answers INT NOT NULL,  -- Number of questions answered correctly
    score NUMERIC(5, 2) NOT NULL,  -- Total score for the exam
    accuracy NUMERIC(5, 2) GENERATED ALWAYS AS (correct_answers::NUMERIC / total_questions * 100) STORED,  -- Accuracy percentage
    metadata_scores JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session_exam_scores FOREIGN KEY (session_id) REFERENCES exam_sessions(session_id) ON DELETE CASCADE
);

-- Exam Scores table indexes
CREATE INDEX IF NOT EXISTS idx_exam_scores_session_id ON exam_scores (session_id);

CREATE TABLE IF NOT EXISTS user_demographics (
    id SERIAL PRIMARY KEY,
    province_id VARCHAR(50),
    city_id VARCHAR(50),
    district_id VARCHAR(50),
    village_id VARCHAR(50),
    birth_date DATE,
    education_level_id VARCHAR(50),
    program_study_id VARCHAR(50),
    last_occupation VARCHAR(100),
    interests JSONB,
    main_purpose VARCHAR(100),
    gender VARCHAR(20),
    phone_number VARCHAR(20),
    preferred_study_methods JSONB,
    weekly_study_time VARCHAR(50),
    primary_devices JSONB,
    learning_style VARCHAR(50),
    study_budget INTEGER,
    email VARCHAR(255) NOT NULL,
    target_jabatan VARCHAR(255),
    target_institution VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS feedback_surveys (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    nps VARCHAR(2) NOT NULL,
    csat VARCHAR(1) NOT NULL,
    ces VARCHAR(1) NOT NULL,
    fitur_favorit JSONB NOT NULL,
    perbaikan VARCHAR(20) NOT NULL,
    perbaikan_lainnya TEXT,
    frekuensi_penggunaan VARCHAR(20) NOT NULL,
    sumber_informasi VARCHAR(20) NOT NULL,
    sumber_informasi_lainnya TEXT,
    umpan_balik TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
-- Drop type if exists and create enum
DROP TYPE IF EXISTS exam_game_status;
CREATE TYPE exam_game_status AS ENUM ('RUNNING', 'PAUSED', 'COMPLETED', 'GAME_OVER');

-- CREATE TABLE IF NOT EXISTS for exam gamification with stopwatch
CREATE TABLE IF NOT EXISTS exam_gamification (
    id VARCHAR(26) NOT NULL PRIMARY KEY,  -- ULID for the record
    exam_session_id VARCHAR(26) NOT NULL UNIQUE,  -- Foreign key to exam_sessions
    current_question_id VARCHAR(26),  -- Current question ID (e.g., "193573")
    
    -- Gamification elements
    current_lives INTEGER NOT NULL DEFAULT 3,  -- Number of remaining lives
    streak_count INTEGER NOT NULL DEFAULT 0,  -- Current correct answer streak
    highest_streak INTEGER NOT NULL DEFAULT 0,  -- Best streak achieved
    hints_remaining JSONB DEFAULT '{"count": 3}'::jsonb,  -- Track remaining hints since questions have hints array
    
    -- Time tracking
    start_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,  -- When the session started
    end_time TIMESTAMPTZ,  -- When the session ended
    elapsed_time INTERVAL DEFAULT '0 seconds'::INTERVAL,  -- Total elapsed time
    pause_start_time TIMESTAMPTZ,  -- When the current pause started
    total_pause_time INTERVAL DEFAULT '0 seconds'::INTERVAL,  -- Total pause duration
    question_times JSONB DEFAULT '{}'::jsonb,  -- Time spent per question ID
    
    -- Status and metadata
    status exam_game_status DEFAULT 'RUNNING',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_exam_session_game 
        FOREIGN KEY (exam_session_id) 
        REFERENCES exam_sessions(session_id) 
        ON DELETE CASCADE
);

-- Create trigger function for exam type check
CREATE OR REPLACE FUNCTION check_exam_session_type()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM exam_sessions 
        WHERE session_id = NEW.exam_session_id 
        AND type IN ('PRACTICE', 'EXAM')
    ) THEN
        RAISE EXCEPTION 'Invalid exam session type. Must be either PRACTICE or EXAM';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for practice check
CREATE TRIGGER ensure_exam_session
    BEFORE INSERT OR UPDATE ON exam_gamification
    FOR EACH ROW
    EXECUTE FUNCTION check_exam_session_type();

-- Gamification table indexes
CREATE INDEX IF NOT EXISTS idx_gamification_exam_session ON exam_gamification(exam_session_id);
CREATE INDEX IF NOT EXISTS idx_gamification_hints ON exam_gamification USING gin (hints_remaining);
CREATE INDEX IF NOT EXISTS idx_gamification_question_times ON exam_gamification USING gin (question_times);

-- Update the enum for subscription types to include all tiers
DROP TYPE IF EXISTS subscription_type CASCADE;
CREATE TYPE subscription_type AS ENUM (
    'FREE',
    'LITE',
    'PLUS',
    'PREMIUM'
);

-- Recreate the subscription_tiers table
CREATE TABLE IF NOT EXISTS subscription_tiers (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    name subscription_type NOT NULL,
    price_monthly NUMERIC(10, 2) NOT NULL,
    original_price NUMERIC(10, 2) NULL,
    discount_percentage INTEGER NULL,
    billing_interval VARCHAR(20) DEFAULT 'monthly',
    features JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Insert the default tiers
INSERT INTO subscription_tiers (
    id,
    name,
    price_monthly,
    original_price,
    discount_percentage,
    features
) VALUES 
(
    'free_tier_001',
    'FREE',
    0.00,
    NULL,
    NULL,
    '{
        "practice_exam": {
            "packages": 1,
            "hearts": "limited",
            "hearts_count": 3
        },
        "personalized_test": false,
        "simulation_exam": {
            "packages": 1,
            "personalized_report": false
        },
        "ai_credits": {
            "amount": 500,
            "frequency": "once"
        }
    }'
),
(
    'lite_tier_001',
    'LITE',
    15000.00,
    30000.00,
    50,
    '{
        "practice_exam": {
            "packages": 3,
            "hearts": "limited",
            "hearts_count": 5
        },
        "personalized_test": {
            "available": true,
            "by_category": false
        },
        "simulation_exam": {
            "packages": 1,
            "personalized_report": true,
            "report_features": ["basic_stats", "bar_chart"]
        },
        "ai_credits": {
            "amount": 1000,
            "frequency": "monthly"
        }
    }'
),
(
    'plus_tier_001',
    'PLUS',
    35000.00,
    70000.00,
    50,
    '{
        "practice_exam": {
            "packages": 5,
            "hearts": "limited",
            "hearts_count": 8
        },
        "personalized_test": {
            "available": true,
            "by_category": true
        },
        "simulation_exam": {
            "packages": 2,
            "personalized_report": true,
            "report_features": ["hexagon_graph", "bar_chart", "detailed_stats"]
        },
        "ai_credits": {
            "amount": 1500,
            "frequency": "monthly"
        }
    }'
),
(
    'premium_tier_001',
    'PREMIUM',
    50000.00,
    100000.00,
    50,
    '{
        "practice_exam": {
            "packages": "unlimited",
            "hearts": "unlimited"
        },
        "personalized_test": {
            "available": true,
            "by_category": true,
            "advanced_analytics": true
        },
        "simulation_exam": {
            "packages": "unlimited",
            "personalized_report": true,
            "report_features": ["hexagon_graph", "bar_chart", "histogram", "detailed_stats", "performance_trends"]
        },
        "ai_credits": {
            "amount": 2000,
            "frequency": "monthly"
        },
        "priority_support": true
    }'
);

-- Create user_subscriptions table to track user subscriptions
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    tier_id VARCHAR(26) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMPTZ NULL,
    is_active BOOLEAN DEFAULT TRUE,
    auto_renewal BOOLEAN DEFAULT FALSE,
    payment_status payment_status DEFAULT 'PENDING',
    last_payment_date TIMESTAMPTZ NULL,
    next_payment_date TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_subscriptions_user 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_user_subscriptions_tier 
        FOREIGN KEY (tier_id) 
        REFERENCES subscription_tiers(id)
);

-- Subscription related indexes
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_tier_id ON user_subscriptions(tier_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_is_active ON user_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_tiers_name ON subscription_tiers(name);
CREATE INDEX IF NOT EXISTS idx_subscription_tiers_price ON subscription_tiers(price_monthly);

-- FUNCTION MODIFIED AT AUTO
CREATE OR REPLACE FUNCTION update_modified_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modified_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_user_demographics_modtime
BEFORE UPDATE ON user_demographics
FOR EACH ROW
EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER user_modified_at
BEFORE UPDATE ON users
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER available_exams_modified_at
BEFORE UPDATE ON available_exams
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER terms_of_service_modified_at
BEFORE UPDATE ON terms_of_services
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER category_modified_at
BEFORE UPDATE ON categories
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER tag_modified_at
BEFORE UPDATE ON tags
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER order_modified_at
BEFORE UPDATE ON orders
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER payment_modified_at
BEFORE UPDATE ON payments
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER invoice_modified_at
BEFORE UPDATE ON invoices
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE OR REPLACE FUNCTION set_due_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.due_date := NEW.invoice_date + INTERVAL '1 day';
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_due_date_trigger
BEFORE INSERT ON invoices
FOR EACH ROW
EXECUTE FUNCTION set_due_date();

CREATE TRIGGER update_media_modified_at
BEFORE UPDATE ON media
FOR EACH ROW
EXECUTE FUNCTION update_modified_at();

-- TRIGGER TO UPDATE MODIFIED_AT ON EXAM SESSIONS
CREATE TRIGGER exam_sessions_modified_at
BEFORE UPDATE ON exam_sessions
FOR EACH ROW EXECUTE FUNCTION update_modified_at();


-- TRIGGER TO UPDATE MODIFIED_AT ON EXAM SCORES
CREATE TRIGGER exam_scores_modified_at
BEFORE UPDATE ON exam_scores
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

-- Add modified_at trigger
CREATE TRIGGER exam_gamification_modified_at 
    BEFORE UPDATE ON exam_gamification 
    FOR EACH ROW 
    EXECUTE FUNCTION update_modified_at();

-- Create trigger for modified_at
CREATE TRIGGER subscription_tiers_modified_at
    BEFORE UPDATE ON subscription_tiers
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

-- Create trigger for modified_at
CREATE TRIGGER user_subscriptions_modified_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();