// store/slices/bundlesSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Define the raw API response types
interface NullableString {
  String: string;
  Valid: boolean;
}

export interface NullableInt {
  Int32: number;
  Valid: boolean;
}

interface NullableTime {
  Time: string;
  Valid: boolean;
}

interface BundleCategory {
  id: string;
  name: string;
}

// API Bundle type
export interface Bundle {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPercentage: NullableInt;
  thumbnailUrl: NullableString;
  bannerUrl: NullableString;
  visibility: string;
  validFrom: NullableTime;
  validUntil: NullableTime;
  createdAt: string;
  modifiedAt: string;
  userId: string;
  category: BundleCategory;
  isFreeAccess: boolean;
  metadata?: string;
}

// API response type - updated to match what your API is returning
export interface BundleResponse {
  bundles: Bundle[];  // The array of bundles from your API
  pagination: {
    total_data: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}
  
// Update BundleUI interface to properly handle the discountPercentage type
export interface BundleUI {
  id: string;
  name: string;
  description: string;
  price: number;
  // Use a union type to handle both possible formats
  discountPercentage: number | null | NullableInt;
  thumbnailUrl: string | null;
  bannerUrl: string | null;
  visibility: string;
  validFrom: string | null;
  validUntil: string | null;
  createdAt: string;
  modifiedAt: string;
  categoryId: string;
  categoryName: string;
  // Keep the original category structure for backward compatibility
  category?: {
    id: string;
    name: string;
  };
  isFreeAccess: boolean;
}

// Add bundle details type
export interface BundleDetails {
    bundle: Bundle;
    examCount: number;
  }
  
  // Add UI version of bundle details
  export interface BundleDetailsUI {
    bundle: BundleUI;
    examCount: number;
  }
  
  // Update the state interface to include bundle details
  interface BundlesState {
    items: BundleUI[] | null;
    filteredItems: BundleUI[] | null;
    loading: boolean;
    error: string | null;
    activeCategory: string | null;
    // Add bundle details to the state
    currentBundleDetails: BundleDetailsUI | null;
    bundleDetailsLoading: boolean;
    bundleDetailsError: string | null;
  }
  
  const initialState: BundlesState = {
    items: null,
    filteredItems: null,
    loading: false,
    error: null,
    activeCategory: null,
    // Initialize new state properties
    currentBundleDetails: null,
    bundleDetailsLoading: false,
    bundleDetailsError: null,
  };

// Helper function to transform API bundle data to UI friendly format
const transformBundleData = (bundle: Bundle): BundleUI => {
  return {
    id: bundle.id,
    name: bundle.name,
    description: bundle.description,
    price: bundle.price,
    discountPercentage: bundle.discountPercentage?.Valid ? bundle.discountPercentage.Int32 : null,
    thumbnailUrl: bundle.thumbnailUrl?.Valid ? bundle.thumbnailUrl.String : null,
    bannerUrl: bundle.bannerUrl?.Valid ? bundle.bannerUrl.String : null,
    visibility: bundle.visibility || 'PRIVATE',
    validFrom: bundle.validFrom?.Valid ? bundle.validFrom.Time : null,
    validUntil: bundle.validUntil?.Valid ? bundle.validUntil.Time : null,
    createdAt: bundle.createdAt,
    modifiedAt: bundle.modifiedAt,
    // Flatten category for easy access
    categoryId: bundle.category?.id || "",
    categoryName: bundle.category?.name || "",
    // Keep original structure for backward compatibility
    category: bundle.category ? {
      id: bundle.category.id,
      name: bundle.category.name
    } : undefined,

    isFreeAccess: bundle.isFreeAccess
  };
};

export const fetchBundleDetails = createAsyncThunk(
    'bundles/fetchBundleDetails',
    async (bundleId: string, { rejectWithValue }) => {
    try {
        const response = await fetch(`/api/exam-bundles/${bundleId}`);
        
        if (response.status === 401) {
          return rejectWithValue('You need to be logged in to view bundle details');
        }
        
        if (!response.ok) {
          const errorText = await response.text();
          return rejectWithValue(errorText || 'Failed to fetch bundle details');
        }
  
        const data = await response.json();
        console.log('Bundle details response:', data);
        
        // Transform the bundle data to UI friendly format
        const transformedData: BundleDetailsUI = {
          bundle: transformBundleData(data.bundle),
          examCount: data.examCount
        };
        
        return transformedData;
      } catch (error: any) {
        console.error('Error in fetchBundleDetails:', error);
        return rejectWithValue(error.message || 'An unknown error occurred');
      }
    }
  );
  
// Create async thunk for fetching bundles
// Modified fetchBundles thunk to handle the actual API response structure
export const fetchBundles = createAsyncThunk(
  'bundles/fetchBundles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/exam-bundles');
      
      if (response.status === 401) {
        return rejectWithValue('You need to be logged in to view exam bundles');
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to fetch exam bundles');
      }

      // Get raw response data for detailed analysis
      const rawData = await response.json();
      
      // Debug logging
      console.log('Raw API response:', rawData);
      
      let bundlesArray: any[] = [];
      
      // Case 1: Direct array
      if (Array.isArray(rawData)) {
        console.log('Structure: API returned direct array');
        bundlesArray = rawData;
      }
      // Case 2: Has "bundles" property that is an array
      else if (rawData && Array.isArray(rawData.bundles)) {
        console.log('Structure: bundles array property found');
        bundlesArray = rawData.bundles;
      }
      // Case 2.5: Has nested "bundles.bundles" property that is an array
      else if (rawData && rawData.bundles && Array.isArray(rawData.bundles.bundles)) {
        console.log('Structure: nested bundles.bundles array property found');
        bundlesArray = rawData.bundles.bundles;
      }
      // Case 3: Has "data" property that is an array (common pattern)
      else if (rawData && Array.isArray(rawData.data)) {
        console.log('Structure: data array property found');
        bundlesArray = rawData.data;
      }
      // Case 4: Empty response - initialize with empty array
      else {
        console.log('Structure: No bundles array found, using empty array');
        // Instead of failing, we'll just use an empty array and log a warning
        console.warn('Could not locate bundles array in response, using empty array');
        bundlesArray = [];
      }
      
      console.log('Bundles array to be processed:', bundlesArray);
      
      // Even if bundles array is empty, we'll still process it (won't throw an error)
      const transformedBundles = bundlesArray
        .map(bundle => 
          // Safety check to ensure each bundle has required properties
          bundle ? transformBundleData(bundle) : null
        )
        .filter((bundle): bundle is BundleUI => bundle !== null); // Type guard to ensure non-null values with proper typing
      
      console.log('Transformed bundles:', transformedBundles);
      
      return transformedBundles;
    } catch (error: any) {
      console.error('Error in fetchBundles:', error);
      return rejectWithValue(error.message || 'An unknown error occurred');
    }
  }
);

// Create async thunk for purchasing a bundle
export const purchaseBundle = createAsyncThunk(
  'bundles/purchaseBundle',
  async (bundleId: string, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/purchase-bundle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ bundleId }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to purchase bundle');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'An unknown error occurred during purchase');
    }
  }
);

// Create the slice
const bundlesSlice = createSlice({
  name: 'bundles',
  initialState,
  reducers: {
    clearBundles: (state) => {
        state.items = null;
        state.filteredItems = null;
        state.error = null;
    },
    // Add reducer to clear bundle details
    clearBundleDetails: (state) => {
        state.currentBundleDetails = null;
        state.bundleDetailsError = null;
    },
    setActiveCategory: (state, action: PayloadAction<string | null>) => {
      state.activeCategory = action.payload;
      
      // Filter items by category if a category is selected
      if (state.items && action.payload) {
        // Use case-insensitive match
        const categoryLower = action.payload.toLowerCase();
        state.filteredItems = state.items.filter(bundle => 
          (bundle.categoryName?.toLowerCase() === categoryLower) || 
          (bundle.category?.name?.toLowerCase() === categoryLower)
        );
      } else if (state.items) {
        state.filteredItems = state.items;
      }
    },
    filterBundles: (state, action: PayloadAction<string>) => {
      const searchTerm = action.payload.toLowerCase();
      if (!state.items) return;
      
      if (searchTerm === '') {
        // If there's an active category, filter by that
        if (state.activeCategory) {
          const categoryLower = state.activeCategory.toLowerCase();
          state.filteredItems = state.items.filter(bundle => 
            (bundle.categoryName?.toLowerCase() === categoryLower) ||
            (bundle.category?.name?.toLowerCase() === categoryLower)
          );
        } else {
          state.filteredItems = state.items;
        }
      } else {
        // Filter by search term and possibly category
        let filtered = state.items.filter(bundle => 
          bundle.name.toLowerCase().includes(searchTerm) || 
          bundle.description.toLowerCase().includes(searchTerm)
        );
        
        // Apply category filter if needed
        if (state.activeCategory) {
          const categoryLower = state.activeCategory.toLowerCase();
          filtered = filtered.filter(bundle => 
            (bundle.categoryName?.toLowerCase() === categoryLower) ||
            (bundle.category?.name?.toLowerCase() === categoryLower)
          );
        }
        
        state.filteredItems = filtered;
      }
    },
    // Add reducer for initializing with server data
    initializeWithServerData: (state, action: PayloadAction<BundleUI[]>) => {
      state.items = action.payload;
      state.filteredItems = action.payload;
      state.loading = false;
      state.error = null;
    },
    // Add reducer for initializing bundle details with server data
    initializeBundleDetailsWithServerData: (state, action: PayloadAction<BundleDetailsUI>) => {
        state.currentBundleDetails = action.payload;
        state.bundleDetailsLoading = false;
        state.bundleDetailsError = null;
        }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchBundles pending state
      .addCase(fetchBundles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      // Handle fetchBundles fulfilled state
      .addCase(fetchBundles.fulfilled, (state, action: PayloadAction<BundleUI[]>) => {
        state.items = action.payload;
        state.filteredItems = action.payload;
        state.loading = false;
        state.error = null;
      })
      // Handle fetchBundles rejected state
      .addCase(fetchBundles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch exam bundles';
      })
      .addCase(fetchBundleDetails.pending, (state) => {
        state.bundleDetailsLoading = true;
        state.bundleDetailsError = null;
      })
      .addCase(fetchBundleDetails.fulfilled, (state, action: PayloadAction<BundleDetailsUI>) => {
        state.currentBundleDetails = action.payload;
        state.bundleDetailsLoading = false;
        state.bundleDetailsError = null;
      })
      .addCase(fetchBundleDetails.rejected, (state, action) => {
        state.bundleDetailsLoading = false;
        state.bundleDetailsError = action.payload as string || 'Failed to fetch bundle details';
        state.currentBundleDetails = null;
      });
  },
});

export const { 
    clearBundles, 
    setActiveCategory, 
    filterBundles, 
    initializeWithServerData,
    // Export new actions
    clearBundleDetails,
    initializeBundleDetailsWithServerData
  } = bundlesSlice.actions;
  
export default bundlesSlice.reducer;