"use client";

import React from "react";
import "@fontsource/sora"; // Importing the Sora font
import styled from "styled-components";
import dynamic from "next/dynamic";
import Loading from "../Loading";
import { useLanguage } from "@/app/language-wrapper"; // Adjust the import path to match your project structure

// Dynamically import components using Next.js dynamic
const DotLottie = dynamic(() => import("@/components/shared/dotlottie-animation"), {
  loading: () => <Loading />,
  ssr: true // If this component doesn't need server-side rendering
});

// Define TypeScript interfaces for our data structures
interface Feature {
  title: string;
  description: string;
  icon: string;
  width: string;
  height: string;
}

// Features data with translations
const getFeatures = (t: (key: string) => string): Feature[] => [
  {
    title: t('pembelajaran_dipersonalisasi'),
    description: t('pembelajaran_description'),
    icon: "https://cdn.terang.ai/dotlotties/ai-assisted.lottie",
    width: "100%",
    height: "100%",
  },
  {
    title: t('analisis_kinerja'),
    description: t('analisis_description'),
    icon: "https://cdn.terang.ai/dotlotties/analysis.lottie",
    width: "100%",
    height: "100%",
  },
  {
    title: t('bank_soal'),
    description: t('bank_soal_description'),
    icon: "https://cdn.terang.ai/dotlotties/books.lottie",
    width: "100%",
    height: "100%",
  },
  {
    title: t('simulasi_ujian'),
    description: t('simulasi_description'),
    icon: "https://cdn.terang.ai/dotlotties/exam-timer.lottie",
    width: "100%",
    height: "100%",
  },
];

// Styled components
const ShowcaseSection = styled.section`
  padding-top: 5rem; /* py-20 */
  padding-bottom: 5rem;
  background-color: #f7fafc; /* bg-gray-100 */
  padding: 2rem;
`;

const TitleContainer = styled.div`
  position: relative;
  width: 100%;
  margin: 3rem auto;
  text-align: center;
`;

const Title = styled.h1`
  font-size: ${(props) => props.theme.fontxxl};
  font-weight: 700; /* font-bold */
  text-align: center;
  color: #2d3748; /* text-gray-800 */
  margin-bottom: 2.5rem; /* mb-10 */
  padding: 0 0.5rem; /* px-2 */
  font-family: "Sora", sans-serif;
  position: relative;
  line-height: 1;
`;

const TitlePart = styled.span`
  display: inline;

  @media (max-width: 768px) {
    display: block;
  }
`;
const TitleTextContainer = styled.div`
  position: relative;
  display: inline-block;
  margin-top: 0.5rem;
  margin-left: 1rem;
  margin-right: 1rem;
`;

const TitleSVG = styled.svg`
  position: absolute;
  bottom: 0px; /* Adjust as needed */
  left: 50%;
  transform: translateX(-50%);
  width: 130%;
  height: 130%;
  pointer-events: none;
`;

const TitleText = styled.span`
  color: #f7fafc;
  font-weight: 900;
  -webkit-text-stroke: 1.8px #f7fafc;
  letter-spacing: 2px;
  position: relative;
  padding: 0 1rem;
  z-index: 1;
  display: inline;
`;

const Container = styled.div`
  max-width: 1280px; /* container */
  margin: 0 auto;
  padding-left: 1rem; /* px-4 */
  padding-right: 1rem;
`;

const FlexWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-left: -1rem; /* -mx-4 */
  margin-right: -1rem;
`;

const FeatureItem = styled.div`
  width: 100%;
  padding-left: 1rem; /* px-4 */
  padding-right: 1rem;
  margin-bottom: 2rem; /* mb-8 */

  @media (min-width: 768px) {
    width: 50%; /* md:w-1/2 */
  }

  @media (min-width: 1024px) {
    width: 25%; /* lg:w-1/4 */
  }
`;

const FeatureBox = styled.div`
  background-color: #ffffff; /* bg-white */
  border-radius: 2rem; /* rounded-lg */
  box-shadow: 0 5px 10px -1px rgba(0, 0, 0, 0.01),
    0 2px 3px 0px rgba(0, 0, 0, 0.01); /* shadow-lg */
  padding: 1.5rem; /* p-6 */
  text-align: center;
  transition: box-shadow 0.3s; /* transition-shadow duration-300 */

  &:hover {
    box-shadow: 0 15px 25px -3px rgba(0, 0, 0, 0.1),
      0 8px 10px -2px rgba(0, 0, 0, 0.05); /* hover:shadow-xl */
  }
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem; /* text-xl */
  font-weight: 600; /* font-semibold */
  color: #2d3748; /* text-gray-800 */
  margin-bottom: 0.5rem; /* mb-2 */
  font-family: "Sora", sans-serif;
`;

const FeatureDescription = styled.p`
  color: #718096; /* text-gray-600 */
  font-family: "Sora", sans-serif;
`;

// Type definition for DotLottie props to satisfy TypeScript
interface DotLottieProps {
  src: string;
  width: string | number;
  height: string | number;
  devicePixelRatio?: number;
}

// Showcase Component
const Showcase: React.FC = () => {
  const { t } = useLanguage();
  const features = getFeatures(t);

  return (
    <ShowcaseSection id="showcase">
      <TitleContainer>
        <Title>
          <TitlePart>{t('how')}</TitlePart>
          <TitleTextContainer>
            <TitleText><strong>Terang AI</strong></TitleText>
            <TitleSVG viewBox="0 0 500 100" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M1106,1813.61193 C1391.48757,1788.3249 1542.09692,1785.22818 1557.82804,1804.32178 C1581.42472,1832.96217 1297.6495,1822.13368 1191.16891,1835.26224 C1084.68832,1848.39079 1016.09991,1866.56524 1566,1841.45052"
                fill="none"
                stroke="#4a90e2"
                strokeLinecap="round"
                strokeWidth="43"
                transform="translate(-1084 -1770)"
              />
            </TitleSVG>
          </TitleTextContainer>
          <TitlePart>{t('different_from_others')}</TitlePart>
        </Title>
      </TitleContainer>
      <Container>
        <FlexWrapper>
          {features.map((feature, index) => (
            <FeatureItem key={index}>
              <FeatureBox>
                <DotLottie
                  src={feature.icon}
                  width={feature.width}
                  height={feature.height}
                  devicePixelRatio={1}
                />
                <FeatureTitle>{feature.title}</FeatureTitle>
                <FeatureDescription>{feature.description}</FeatureDescription>
              </FeatureBox>
            </FeatureItem>
          ))}
        </FlexWrapper>
      </Container>
    </ShowcaseSection>
  );
};

export default Showcase;