package custom

import (
	"bytes"
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	// Update this import path to match your project structure
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/terang-ai/backend-service/lib"
)

// HintsRemaining with JSON scanning capabilities
type HintsRemaining struct {
	Count string `json:"count"`
}

// Scan implements sql.Scanner interface
func (h *HintsRemaining) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("unexpected type for hints_remaining: %T", value)
	}

	return json.Unmarshal(b, h)
}

// Value implements driver.Valuer interface
func (h HintsRemaining) Value() (driver.Value, error) {
	return json.Marshal(h)
}

// QuestionTimes with JSON scanning capabilities
type QuestionTimes map[string]float64

// <PERSON>an implements sql.Scanner interface
func (qt *QuestionTimes) Scan(value interface{}) error {
	if value == nil {
		*qt = make(QuestionTimes)
		return nil
	}

	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("unexpected type for question_times: %T", value)
	}

	return json.Unmarshal(b, qt)
}

// Value implements driver.Valuer interface
func (qt QuestionTimes) Value() (driver.Value, error) {
	if qt == nil {
		return json.Marshal(make(QuestionTimes))
	}
	return json.Marshal(qt)
}

// PostgresInterval handles PostgreSQL interval type
type PostgresInterval struct {
	time.Duration
}

// Scan implements the sql.Scanner interface
func (pi *PostgresInterval) Scan(src interface{}) error {
	if src == nil {
		pi.Duration = 0
		return nil
	}

	switch v := src.(type) {
	case []byte:
		return pi.parsePgInterval(string(v))
	case string:
		return pi.parsePgInterval(v)
	}
	return fmt.Errorf("cannot scan type %T into PostgresInterval", src)
}

func (pi *PostgresInterval) parsePgInterval(interval string) error {
	// Remove any leading/trailing whitespace
	interval = strings.TrimSpace(interval)

	// Handle intervals in the format 'HH:MM:SS'
	if strings.Count(interval, ":") == 2 {
		parts := strings.Split(interval, ":")
		hours, err := strconv.Atoi(parts[0])
		if err != nil {
			return err
		}
		minutes, err := strconv.Atoi(parts[1])
		if err != nil {
			return err
		}
		seconds, err := strconv.Atoi(parts[2])
		if err != nil {
			return err
		}
		pi.Duration = time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second
		return nil
	}

	// Parse intervals like '0 seconds'
	if strings.HasSuffix(interval, "seconds") || strings.HasSuffix(interval, "minutes") || strings.HasSuffix(interval, "hours") {
		duration, err := time.ParseDuration(interval)
		if err != nil {
			return err
		}
		pi.Duration = duration
		return nil
	}

	// Attempt to parse as standard duration
	duration, err := time.ParseDuration(interval)
	if err != nil {
		return err
	}
	pi.Duration = duration
	return nil
}

// Value implements the driver.Valuer interface
func (pi PostgresInterval) Value() (driver.Value, error) {
	hours := int(pi.Duration.Hours())
	minutes := int(pi.Duration.Minutes()) % 60
	seconds := int(pi.Duration.Seconds()) % 60
	return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds), nil
}

// MarshalJSON implements JSON marshaling
func (pi PostgresInterval) MarshalJSON() ([]byte, error) {
	return json.Marshal(pi.Duration.String())
}

// Request structs
type CreateGamificationRequest struct {
	ExamSessionID string `json:"examSessionId" binding:"required"`
	CurrentLives  *int   `json:"currentLives"`
}

type UpdateGamificationRequest struct {
	CurrentQuestionID *string            `json:"currentQuestionId,omitempty"`
	CurrentLives      *int               `json:"currentLives,omitempty"`
	StreakCount       *int               `json:"streakCount,omitempty"`
	HighestStreak     *int               `json:"highestStreak,omitempty"`
	Status            *string            `json:"status,omitempty"`
	EndTime           *time.Time         `json:"endTime,omitempty"`
	ElapsedTime       *string            `json:"elapsedTime,omitempty"`    // Should be in "HH:MM:SS" format
	TotalPauseTime    *string            `json:"totalPauseTime,omitempty"` // Should be in "HH:MM:SS" format
	QuestionTimes     map[string]float64 `json:"question_times,omitempty"` // Exactly matches the payload format
}

// Main model struct
type ExamGamification struct {
	ID                string           `db:"id" json:"id"`
	ExamSessionID     string           `db:"exam_session_id" json:"examSessionId"`
	CurrentQuestionID *string          `db:"current_question_id" json:"currentQuestionId,omitempty"`
	CurrentLives      int              `db:"current_lives" json:"currentLives"`
	StreakCount       int              `db:"streak_count" json:"streakCount"`
	HighestStreak     int              `db:"highest_streak" json:"highestStreak"`
	HintsRemaining    HintsRemaining   `db:"hints_remaining" json:"hintsRemaining"`
	StartTime         *time.Time       `db:"start_time" json:"startTime"`
	EndTime           *time.Time       `db:"end_time" json:"endTime,omitempty"`
	ElapsedTime       PostgresInterval `db:"elapsed_time" json:"elapsedTime"`
	PauseStartTime    *time.Time       `db:"pause_start_time" json:"pauseStartTime,omitempty"`
	TotalPauseTime    PostgresInterval `db:"total_pause_time" json:"totalPauseTime"`
	QuestionTimes     QuestionTimes    `db:"question_times" json:"questionTimes"`
	Status            string           `db:"status" json:"status"`
	CreatedAt         time.Time        `db:"created_at" json:"createdAt"`
	ModifiedAt        time.Time        `db:"modified_at" json:"modifiedAt"`
}

func createGamification(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req CreateGamificationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(400, gin.H{"error": "Invalid request payload"})
			return
		}

		// Set default lives to 3 if not provided
		lives := 3
		if req.CurrentLives != nil {
			lives = *req.CurrentLives
		}

		id := lib.GenerateULID()

		query := `
            INSERT INTO exam_gamification (
                id, 
                exam_session_id, 
                current_lives, 
                hints_remaining, 
                question_times,
                status,
                streak_count,
                highest_streak,
                elapsed_time,
                total_pause_time
            ) VALUES (
                $1, 
                $2, 
                $3, 
                '{"count": "3"}'::jsonb, 
                '{}'::jsonb,
                'RUNNING'::exam_game_status,
                0,
                0,
                '0 seconds'::interval,
                '0 seconds'::interval
            )
            RETURNING *`

		var game ExamGamification
		err := dbx.Get(&game, query, id, req.ExamSessionID, lives)
		if err != nil {
			if strings.Contains(err.Error(), "unique constraint") {
				log.Printf("Duplicate exam session: %s - %v", req.ExamSessionID, err)
				c.JSON(400, gin.H{"error": "Gamification already exists for this exam session"})
				return
			}

			if strings.Contains(err.Error(), "foreign key constraint") {
				log.Printf("Invalid exam session ID: %s - %v", req.ExamSessionID, err)
				c.JSON(400, gin.H{"error": "Invalid exam session ID"})
				return
			}

			log.Printf("Error creating gamification - ID: %s, SessionID: %s, Error: %v",
				id, req.ExamSessionID, err)
			c.JSON(500, gin.H{"error": "Failed to create gamification"})
			return
		}

		c.JSON(201, gin.H{
			"message": "Gamification created successfully",
			"data":    game,
		})
	}
}

func getGamificationBySession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := c.Param("sessionId")

		var game ExamGamification
		err := dbx.Get(&game, "SELECT * FROM exam_gamification WHERE exam_session_id = $1", sessionId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(404, gin.H{"error": "Gamification not found"})
				return
			}
			c.JSON(500, gin.H{"error": "Failed to fetch gamification"})
			return
		}

		c.JSON(200, game)
	}
}

func updateGamificationBySession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := c.Param("sessionId")
		log.Printf("Received request for session: %s", sessionId)

		// Get raw body for logging
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			log.Printf("Failed to read request body: %v", err)
			c.JSON(400, gin.H{"error": "Failed to read request"})
			return
		}
		log.Printf("Received body: %s", string(body))
		// Restore body for binding
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		var req UpdateGamificationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			log.Printf("JSON binding error: %v", err)
			log.Printf("Raw request body: %s", string(body))
			c.JSON(400, gin.H{
				"error": fmt.Sprintf("Invalid request payload: %v", err),
			})
			return
		}

		// Build dynamic update query
		query := "UPDATE exam_gamification SET modified_at = CURRENT_TIMESTAMP"
		args := []interface{}{}
		argCount := 1

		if req.QuestionTimes != nil {
			// Get current gamification to merge question times
			var currentGame ExamGamification
			err := dbx.Get(&currentGame, "SELECT * FROM exam_gamification WHERE exam_session_id = $1", sessionId)
			if err != nil && err != sql.ErrNoRows {
				log.Printf("Failed to fetch current gamification: %v", err)
				c.JSON(500, gin.H{"error": "Failed to fetch current state"})
				return
			}

			// Initialize current times if nil
			if currentGame.QuestionTimes == nil {
				currentGame.QuestionTimes = make(QuestionTimes)
			}

			// Merge new times with existing times
			for k, v := range req.QuestionTimes {
				currentGame.QuestionTimes[k] = v
			}

			jsonBytes, err := json.Marshal(currentGame.QuestionTimes)
			if err != nil {
				log.Printf("Failed to marshal question times: %v", err)
				c.JSON(400, gin.H{"error": fmt.Sprintf("Invalid question times format: %v", err)})
				return
			}

			query += fmt.Sprintf(", question_times = $%d::jsonb", argCount)
			args = append(args, string(jsonBytes))
			argCount++
			log.Printf("Updating question times with: %s", string(jsonBytes))
		}

		query += fmt.Sprintf(" WHERE exam_session_id = $%d RETURNING *", argCount)
		args = append(args, sessionId)

		log.Printf("Executing query: %s with args: %v", query, args)

		var game ExamGamification
		err = dbx.Get(&game, query, args...)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(404, gin.H{"error": "Gamification not found"})
				return
			}
			log.Printf("Database error: %v", err)
			c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to update gamification: %v", err)})
			return
		}

		c.JSON(200, game)
	}
}

func pauseGamificationBySession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := c.Param("sessionId")

		query := `
			UPDATE exam_gamification 
			SET 
				status = 'PAUSED',
				pause_start_time = CURRENT_TIMESTAMP,
				modified_at = CURRENT_TIMESTAMP
			WHERE exam_session_id = $1 AND status = 'RUNNING'
			RETURNING *`

		var game ExamGamification
		err := dbx.Get(&game, query, sessionId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(400, gin.H{"error": "Cannot pause: gamification not found or not running"})
				return
			}
			c.JSON(500, gin.H{"error": "Failed to pause gamification"})
			return
		}

		c.JSON(200, game)
	}
}

func resumeGamificationBySession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := c.Param("sessionId")

		tx, err := dbx.Beginx()
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to start transaction"})
			return
		}
		defer tx.Rollback()

		// Fetch the current pause_start_time
		var pauseStartTime time.Time
		err = tx.Get(&pauseStartTime, "SELECT pause_start_time FROM exam_gamification WHERE exam_session_id = $1 AND status = 'PAUSED' FOR UPDATE", sessionId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(400, gin.H{"error": "Cannot resume: gamification not found or not paused"})
				return
			}
			c.JSON(500, gin.H{"error": "Failed to fetch gamification"})
			return
		}

		// Calculate the pause duration
		pauseDuration := time.Since(pauseStartTime)
		pauseDurationStr := fmt.Sprintf("%02d:%02d:%02d", int(pauseDuration.Hours()), int(pauseDuration.Minutes())%60, int(pauseDuration.Seconds())%60)

		query := `
			UPDATE exam_gamification 
			SET 
				status = 'RUNNING',
				total_pause_time = total_pause_time + $1::interval,
				pause_start_time = NULL,
				modified_at = CURRENT_TIMESTAMP
			WHERE exam_session_id = $2 AND status = 'PAUSED'
			RETURNING *`

		var game ExamGamification
		err = tx.Get(&game, query, pauseDurationStr, sessionId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(400, gin.H{"error": "Cannot resume: gamification not found or not paused"})
				return
			}
			c.JSON(500, gin.H{"error": "Failed to resume gamification"})
			return
		}

		if err := tx.Commit(); err != nil {
			c.JSON(500, gin.H{"error": "Failed to commit transaction"})
			return
		}

		c.JSON(200, game)
	}
}

func useHintBySession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := c.Param("sessionId")

		tx, err := dbx.Beginx()
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to start transaction"})
			return
		}
		defer tx.Rollback()

		// Get current hints
		var game ExamGamification
		err = tx.Get(&game, "SELECT * FROM exam_gamification WHERE exam_session_id = $1 FOR UPDATE", sessionId)
		if err != nil {
			c.JSON(404, gin.H{"error": "Gamification not found"})
			return
		}

		// Convert current count to int for comparison
		currentCount, err := strconv.Atoi(game.HintsRemaining.Count)
		if err != nil {
			c.JSON(500, gin.H{"error": "Invalid hints count in database"})
			return
		}

		if currentCount <= 0 {
			c.JSON(400, gin.H{"error": "No hints remaining"})
			return
		}

		// Decrement hints count
		newCount := currentCount - 1
		game.HintsRemaining.Count = strconv.Itoa(newCount)

		// Update the hints_remaining field
		query := `
            UPDATE exam_gamification 
            SET 
                hints_remaining = $1::jsonb,
                modified_at = CURRENT_TIMESTAMP
            WHERE exam_session_id = $2
            RETURNING *`

		hintsJSON, err := json.Marshal(game.HintsRemaining)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to marshal hints_remaining"})
			return
		}

		err = tx.Get(&game, query, hintsJSON, sessionId)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to use hint"})
			return
		}

		if err := tx.Commit(); err != nil {
			c.JSON(500, gin.H{"error": "Failed to commit transaction"})
			return
		}

		c.JSON(200, game)
	}
}

// Delete function remains the same because it uses the ID
func deleteGamification(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")

		result, err := dbx.Exec("DELETE FROM exam_gamification WHERE id = $1", id)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to delete gamification"})
			return
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			c.JSON(404, gin.H{"error": "Gamification not found"})
			return
		}

		c.JSON(200, gin.H{"message": "Gamification deleted successfully"})
	}
}

// Update ExamTimeRecord struct with proper db tags
type ExamTimeRecord struct {
	ExamSessionID string        `db:"exam_session_id"`
	QuestionTimes QuestionTimes `db:"question_times"`
	ExamID        string        `db:"exam_id"`
}

type QuestionAnalysis struct {
	QuestionID    string
	Title         string
	Hints         []string
	AverageTime   float64
	TotalAttempts int64
	ExamID        string // New field
}

type QuestionTimeResponse struct {
	Success  bool               `json:"success"`
	Data     []QuestionAnalysis `json:"data"`
	Metadata struct {
		TotalQuestions int       `json:"totalQuestions"`
		TotalSessions  int       `json:"totalSessions"`
		Timestamp      time.Time `json:"timestamp"`
	} `json:"metadata"`
}

type questionStats struct {
	totalTime float64
	attempts  int64
	examId    string
}

type HintInfo struct {
	hints []string
	title string
}

// Modified to use goroutines for parallel processing
func getAverageQuestionTimesParallel(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()

		// Create a channel to receive results and errors
		recordsCh := make(chan []ExamTimeRecord, 1)
		errorCh := make(chan error, 2) // We'll have 2 parallel operations

		// Fetch exam records in a goroutine
		go func() {
			query := `
                SELECT es.exam_id, eg.question_times, eg.exam_session_id
                FROM exam_gamification eg
                JOIN exam_sessions es ON eg.exam_session_id = es.session_id
                WHERE es.status = 'COMPLETED' 
                AND es.type = 'EXAM'
            `

			var records []ExamTimeRecord
			err := dbx.SelectContext(ctx, &records, query)
			if err != nil {
				errorCh <- fmt.Errorf("failed to fetch exam records: %v", err)
				return
			}

			log.Printf("Found %d exam records", len(records))
			recordsCh <- records
		}()

		// Process the records
		select {
		case <-ctx.Done():
			c.JSON(500, gin.H{"error": "Request timed out"})
			return
		case err := <-errorCh:
			log.Printf("Error: %v", err)
			c.JSON(500, gin.H{"error": err.Error()})
			return
		case records := <-recordsCh:
			// Process records to calculate stats
			questionStatsMap := make(map[string]*questionStats)
			questionIDs := make([]string, 0)

			// Use a mutex to protect concurrent access to the maps
			statsMutex := sync.Mutex{}

			// Process in parallel using worker pool
			var wg sync.WaitGroup
			workerCount := 4 // Adjust based on your hardware
			recordChunks := chunkSlice(records, workerCount)

			for _, chunk := range recordChunks {
				wg.Add(1)
				go func(chunk []ExamTimeRecord) {
					defer wg.Done()

					localStats := make(map[string]*questionStats)
					localIDs := make(map[string]struct{})

					// Process each exam session in this chunk
					for _, record := range chunk {
						for questionID, timeSpent := range record.QuestionTimes {
							localIDs[questionID] = struct{}{}

							if stats, exists := localStats[questionID]; exists {
								stats.totalTime += timeSpent
								stats.attempts++
							} else {
								localStats[questionID] = &questionStats{
									totalTime: timeSpent,
									attempts:  1,
									examId:    record.ExamID,
								}
							}
						}
					}

					// Merge results under lock
					statsMutex.Lock()
					defer statsMutex.Unlock()

					for qID, stats := range localStats {
						if existingStats, exists := questionStatsMap[qID]; exists {
							existingStats.totalTime += stats.totalTime
							existingStats.attempts += stats.attempts
						} else {
							questionStatsMap[qID] = stats
							questionIDs = append(questionIDs, qID)
						}
					}
				}(chunk)
			}

			// Wait for all workers to complete
			wg.Wait()

			// If we have no questions processed, return empty response
			if len(questionIDs) == 0 {
				response := QuestionTimeResponse{
					Success: true,
					Data:    []QuestionAnalysis{},
					Metadata: struct {
						TotalQuestions int       `json:"totalQuestions"`
						TotalSessions  int       `json:"totalSessions"`
						Timestamp      time.Time `json:"timestamp"`
					}{
						TotalQuestions: 0,
						TotalSessions:  len(records),
						Timestamp:      time.Now(),
					},
				}
				c.JSON(200, response)
				return
			}

			// Fetch hints and titles concurrently
			hintsCh := make(chan map[string]HintInfo, 1)

			go func() {
				hintsQuery := `
					SELECT q->>'id' as question_id,
						q->'hints' as hints,
						q->'question'->0->'contents'->0->>'content' as title
					FROM exam_question_answer_hints eqah,
					jsonb_array_elements(eqah.data) as q
					WHERE q->>'id' = ANY($1)
				`

				type QuestionHints struct {
					QuestionID string          `db:"question_id"`
					Hints      json.RawMessage `db:"hints"`
					Title      string          `db:"title"`
				}

				var questionHints []QuestionHints
				err := dbx.SelectContext(ctx, &questionHints, hintsQuery, pq.Array(questionIDs))
				if err != nil {
					errorCh <- fmt.Errorf("failed to fetch question hints: %v", err)
					return
				}

				// Map hints and titles
				hintsMap := make(map[string]HintInfo)

				for _, qh := range questionHints {
					var hints []string
					if err := json.Unmarshal(qh.Hints, &hints); err != nil {
						log.Printf("Error unmarshaling hints for question %s: %v", qh.QuestionID, err)
						continue
					}
					hintsMap[qh.QuestionID] = HintInfo{
						hints: hints,
						title: qh.Title,
					}
				}

				hintsCh <- hintsMap
			}()

			// Wait for hints to be fetched
			var hintsMap map[string]HintInfo
			select {
			case <-ctx.Done():
				c.JSON(500, gin.H{"error": "Request timed out when fetching hints"})
				return
			case err := <-errorCh:
				log.Printf("Error fetching hints: %v", err)
				c.JSON(500, gin.H{"error": err.Error()})
				return
			case hintsMap = <-hintsCh:
				// Received hints map, continue processing
			}

			// Build the analysis data
			var analysis []QuestionAnalysis
			for questionID, stats := range questionStatsMap {
				averageTime := stats.totalTime / float64(stats.attempts)

				hintInfo, exists := hintsMap[questionID]
				if !exists {
					hintInfo = HintInfo{
						hints: []string{},
						title: "",
					}
				}

				analysis = append(analysis, QuestionAnalysis{
					QuestionID:    questionID,
					Title:         hintInfo.title,
					Hints:         hintInfo.hints,
					AverageTime:   float64(int64(averageTime*100)) / 100,
					TotalAttempts: stats.attempts,
					ExamID:        stats.examId,
				})
			}

			// Respond with the analysis data
			response := QuestionTimeResponse{
				Success: true,
				Data:    analysis,
				Metadata: struct {
					TotalQuestions int       `json:"totalQuestions"`
					TotalSessions  int       `json:"totalSessions"`
					Timestamp      time.Time `json:"timestamp"`
				}{
					TotalQuestions: len(analysis),
					TotalSessions:  len(records),
					Timestamp:      time.Now(),
				},
			}

			c.JSON(200, response)
		}
	}
}

// Utility function to split a slice into chunks for parallel processing
func chunkSlice(slice []ExamTimeRecord, numChunks int) [][]ExamTimeRecord {
	if len(slice) == 0 {
		return [][]ExamTimeRecord{{}}
	}

	if numChunks <= 0 {
		numChunks = 1
	}

	if numChunks > len(slice) {
		numChunks = len(slice)
	}

	chunkSize := (len(slice) + numChunks - 1) / numChunks
	chunks := make([][]ExamTimeRecord, 0, numChunks)

	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}

	return chunks
}

// Improved version of getAverageQuestionTimes using batch processing
func getAverageQuestionTimes(dbx *sqlx.DB) gin.HandlerFunc {
	return getAverageQuestionTimesParallel(dbx)
}

// Register the routes with appropriate timeouts
func RegisterGamificationRoutes(r *gin.Engine, dbx *sqlx.DB) {
	gamificationGroup := r.Group("/v0/gamification")
	{
		gamificationGroup.POST("", createGamification(dbx))
		gamificationGroup.GET("/session/:sessionId", getGamificationBySession(dbx))
		gamificationGroup.PUT("/session/:sessionId", updateGamificationBySession(dbx))
		gamificationGroup.DELETE("/:id", deleteGamification(dbx)) // Still uses ID for deletion
		gamificationGroup.POST("/session/:sessionId/pause", pauseGamificationBySession(dbx))
		gamificationGroup.POST("/session/:sessionId/resume", resumeGamificationBySession(dbx))
		gamificationGroup.POST("/session/:sessionId/use-hint", useHintBySession(dbx))

		gamificationGroup.GET("/analytics/question-times", getAverageQuestionTimes(dbx))
	}
}
