package controller

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/terms_of_services"
	"github.com/terang-ai/backend-service/repository"
)

type TermsOfServiceController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "wip", "user")
}

func NewTermsOfServiceController(db *sql.DB, redis *redis.Client) *TermsOfServiceController {
	return &TermsOfServiceController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "terms_of_services", "terms_of_service"), // Initialize with specific table and entity name
		Entity: "terms_of_service",
	}
}

func (c *TermsOfServiceController) DeleteTermsOfService(ctx *gin.Context) {
	var uri model.TermsOfServiceUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete TermsOfService
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete TermsOfService failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "TermsOfService not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete TermsOfService successfully"})
}

func (c *TermsOfServiceController) GetAllTermsOfServices(ctx *gin.Context) {
	var entity model.TermsOfService
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "terms_of_service retrieved successfully", "_pagination": paginationInfo})
}

func (c *TermsOfServiceController) GetAllTermsOfServicesByArtistId(ctx *gin.Context) {
	var uri model.TermsOfServiceArtistUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	var entity model.TermsOfService
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAllBySomethingById("artist_id", uri.ARTISTID, page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "terms_of_service retrieved successfully", "_pagination": paginationInfo})
}

func (c *TermsOfServiceController) GetOneTermsOfServicesByArtistId(ctx *gin.Context) {
	var uri model.TermsOfServiceIdArtistUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	var entity model.TermsOfService
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetOneBySomethingById("id", uri.ID, "artist_id", uri.ARTISTID, page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "terms_of_service retrieved successfully", "_pagination": paginationInfo})
}

func (c *TermsOfServiceController) GetAllTermsOfServicesWithCache(ctx *gin.Context) {
	var entity model.TermsOfService
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, cache_status, err := c.Repo.GetAllWithCache(ctx.Request.Context(), page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "is_cache_hit": cache_status, "msg": "TermsOfServices retrieved successfully", "_pagination": paginationInfo})
}

func (c *TermsOfServiceController) GetOneTermsOfService(ctx *gin.Context) {
	var uri model.TermsOfServiceUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.TermsOfService
	entity := &model.TermsOfService{}

	// Call repository method to retrieve one TermsOfService by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "TermsOfService not found"})
		return
	}

	// Type assertion to *model.TermsOfService
	if wipEntity, ok := result.(*model.TermsOfService); ok {
		// Update entity with fetched data
		*entity = *wipEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get TermsOfService successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.TermsOfService failed", "msg": "internal error"})
}

func (c *TermsOfServiceController) InsertTermsOfService(ctx *gin.Context) {
	var post model.PostTermsOfService // Replace with your specific post type
	var entity model.TermsOfService
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert TermsOfService failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert TermsOfService successfully"})
}

func (c *TermsOfServiceController) UpdateTermsOfService(ctx *gin.Context) {
	var updates model.UpdateTermsOfService
	var uri model.TermsOfServiceUri
	var entity model.TermsOfService

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update TermsOfService by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update TermsOfService", "error": err.Error()})
		return
	}

	// Type assertion to *model.TermsOfService
	if updatedTermsOfService, ok := updated.(*model.TermsOfService); ok {
		// Update entity with fetched data after update
		entity = *updatedTermsOfService
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "TermsOfService updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.TermsOfService failed", "msg": "internal error"})
}
