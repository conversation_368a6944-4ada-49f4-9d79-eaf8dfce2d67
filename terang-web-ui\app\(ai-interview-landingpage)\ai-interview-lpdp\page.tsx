"use client";

import dynamic from "next/dynamic";
import { ThemeProvider } from "styled-components";
import { light } from "@/components/landingpage/styles/Themes";
import Loading from "@/components/landingpage/components/Loading";

// Dynamic imports with loading fallbacks
const GlobalStyles = dynamic(
  () => import("@/components/landingpage/styles/GlobalStyles"),
  { ssr: true, loading: () => <Loading /> },
);
const Navigation = dynamic(
  () => import("@/components/landingpage/components/Navigation"),
  { ssr: true, loading: () => <Loading /> },
);
const AIInterview = dynamic(
  () => import("@/components/ai-interview-landingpage/index"),
  { ssr: true, loading: () => <Loading /> },
);
const Footer = dynamic(
  () => import("@/components/landingpage/components/Footer"),
  { ssr: true, loading: () => <Loading /> },
);

function AIInterviewPage() {
  return (
    <main>
      <GlobalStyles />
      <ThemeProvider theme={light}>
        <Navigation additionalPage={true} />
        <AIInterview />
        <Footer />
      </ThemeProvider>
    </main>
  );
}

export default AIInterviewPage;
