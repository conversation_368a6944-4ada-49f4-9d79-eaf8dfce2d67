// components/VideoShowcase.tsx
import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, Pause, <PERSON>, Mic, BarChart3, CheckCircle, ArrowRight, Sparkles, Video, BookOpen, Users } from 'lucide-react';
import styled from 'styled-components';
import { useLanguage } from '@/app/language-wrapper';

// Styled Components
const TitleContainer = styled.div`
  position: relative;
  width: 100%;
  margin: 3rem auto;
  text-align: center;
`;

const Title = styled.h2`
  font-size: ${(props) => props.theme.fontxxl || '4rem'};
  text-transform: capitalize;
  color: #111827; // equivalent to text-gray-900
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  position: relative;
  margin-bottom: 2rem;

  @media (max-width: 64em) {
    font-size: 3.75rem; // equivalent to text-6xl on md
  }
  @media (max-width: 40em) {
    font-size: 2.25rem; // smaller for mobile
  }
`;

const TitleWrapper = styled.span`
  margin-left: 1.2rem;
  position: relative;
  display: inline-block;
  z-index: 2;
  @media (max-width: 64em) {
    margin-left: 0.1rem;
  }
  @media (max-width: 40em) {
    margin-left: 0.1rem;
  }
`;

const TitleText = styled.span`
  color: #f7fafc;
  background-clip: text;
  -webkit-background-clip: text;
  font-weight: 900;
  letter-spacing: 2px;
  position: relative;
  z-index: 3;
  @media (max-width: 64em) {
    letter-spacing: 1.5px;
  }
`;

const TitleSVG = styled.svg`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scaleX(0.64) scaleY(0.77) rotate(2deg);
  width: 165%;
  height: auto;
  z-index: 1;
  pointer-events: none;
  @media (max-width: 64em) {
    height: 390%;
  }
  @media (max-width: 40em) {
    height: 390%;
  }
`;

const StyledTitleSVG = styled(TitleSVG)`
  path {
    fill: #4a90e2;
    stroke: #4a90e2;
    stroke-dasharray: 8;
  }

  @keyframes dash {
    to {
      stroke-dashoffset: 1000;
    }
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background-image: 
      radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.2) 1px, transparent 0),
      linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
      linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%);
    background-size: 10px 10px, 20px 20px, 20px 20px;
    mix-blend-mode: overlay;
    pointer-events: none;
  }
`;

// Custom Icon Components
const AIVoiceIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <img 
    src="https://cdn.terang.ai/landingpage-assets/ai-voice.svg" 
    alt="AI Voice" 
    className={className}
  />
);

const ApplicationIcon: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <img 
    src="https://cdn.terang.ai/landingpage-assets/application.svg" 
    alt="Application" 
    className={className}
  />
);

// Interfaces
interface VideoFeature {
  title: string;
  description: string;
  icon: React.ElementType;
  bgColor: string;
  videoSrc: string;
  posterSrc: string;
  pattern: string;
  key: string;
  features: string[];
  buttonText: string;
  gradient: string;
}

interface LazyVideoProps {
  src: string;
  poster: string;
  title: string;
}

interface TabConfig {
  id: string;
  label: string;
  icon: React.ElementType;
  features: VideoFeature[];
}

// LazyVideo Component
const LazyVideo: React.FC<LazyVideoProps> = ({ src, poster, title }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState<boolean>(false);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  useEffect(() => {
    const options: IntersectionObserverInit = {
      root: null,
      rootMargin: '0px',
      threshold: 0.7
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsInView(true);
          if (videoRef.current && videoRef.current.paused) {
            videoRef.current.play()
              .then(() => setIsPlaying(true))
              .catch((error) => console.error('Error autoplaying video:', error));
          }
        } else {
          if (videoRef.current && !videoRef.current.paused) {
            videoRef.current.pause();
            setIsPlaying(false);
          }
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, options);

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  const handleVideoLoaded = (): void => {
    setIsLoaded(true);
    if (isInView && videoRef.current) {
      videoRef.current.play()
        .then(() => setIsPlaying(true))
        .catch((error) => console.error('Error autoplaying video:', error));
    }
  };

  const handlePlayPause = (): void => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play()
          .then(() => setIsPlaying(true))
          .catch((error) => console.error('Error playing video:', error));
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  return (
    <div 
      ref={containerRef} 
      className="aspect-video rounded-3xl overflow-hidden relative bg-gray-100 shadow-2xl border border-white/20 group backdrop-blur-sm"
    >
      {/* Poster Image */}
      <div 
        className={`absolute inset-0 transition-all duration-500 ${
          isLoaded ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
        }`}
      >
        <img 
          src={poster} 
          alt={`${title} thumbnail`}
          className="w-full h-full object-contain bg-gray-100"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-blue-900/20" />
      </div>

      {/* Video */}
      {isInView && (
        <video
          ref={videoRef}
          className={`w-full h-full object-contain transition-all duration-500 ${
            isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}
          playsInline
          muted
          loop
          onLoadedData={handleVideoLoaded}
          onEnded={() => setIsPlaying(false)}
          onPause={() => setIsPlaying(false)}
          onPlay={() => setIsPlaying(true)}
        >
          <source src={src} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      )}

      {/* Play/Pause Overlay */}
      <button 
        className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          isPlaying ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
        }`}
        onClick={handlePlayPause}
        aria-label={`${isPlaying ? 'Pause' : 'Play'} ${title} video`}
      >
        <div className="bg-white/95 backdrop-blur-md p-6 rounded-full shadow-2xl transform transition-all duration-300 group-hover:scale-110 hover:bg-white border border-white/30">
          {isPlaying ? (
            <Pause className="w-8 h-8 text-gray-900" />
          ) : (
            <Play className="w-8 h-8 text-gray-900 ml-1" />
          )}
        </div>
      </button>

      {/* Gradient overlay for better contrast */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent pointer-events-none" />
    </div>
  );
};

// Main VideoShowcase Component
const VideoShowcase: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('ai-interview');
  const { t } = useLanguage();

  // Feature Data Functions with proper localization
  const getAIInterviewFeatures = (): VideoFeature[] => [
    {
      title: t('lpdp_interview_simulation'),
      description: t('lpdp_interview_description'),
      icon: Brain,
      bgColor: "from-purple-50 to-pink-50",
      videoSrc: "https://cdn.terang.ai/videos/1-interview-lpdp.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-1.webp",
      pattern: "squares",
      key: "lpdp_interview",
      features: [
        t('personal_background_motivation'),
        t('ai_professor_terra_interactive'),
        t('realtime_feedback_analysis'),
        t('complete_simulation_45_60_minutes')
      ],
      buttonText: t('start_free_lpdp_interview'),
      gradient: "from-purple-600 to-pink-600"
    },
    {
      title: t('natural_voice_interview'),
      description: t('voice_interview_description'),
      icon: Mic,
      bgColor: "from-emerald-50 to-teal-50",
      videoSrc: "https://cdn.terang.ai/videos/2-interview-lpdp.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-2.webp",
      pattern: "bokeh",
      key: "voice_conversation",
      features: [
        t('indonesian_voice_recognition'),
        t('natural_conversation_flow'),
        t('realtime_audio_analysis'),
        t('interactive_response_system')
      ],
      buttonText: t('start_voice_interview'),
      gradient: "from-emerald-600 to-teal-600"
    },
    {
      title: t('smart_performance_insights'),
      description: t('performance_insights_description'),
      icon: BarChart3,
      bgColor: "from-blue-50 to-indigo-50",
      videoSrc: "https://cdn.terang.ai/videos/3-interview-lpdp.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-3.webp",
      pattern: "crosshatch",
      key: "performance_insights",
      features: [
        t('detailed_performance_score'),
        t('ai_generated_learning_path'),
        t('improvement_recommendations'),
        t('curated_learning_resources')
      ],
      buttonText: t('start_now'),
      gradient: "from-blue-600 to-indigo-600"
    }
  ];

  const getExamPrepFeatures = (): VideoFeature[] => [
    {
      title: t('gamifikasi_latihan'),
      description: t('gamified_practice_description'),
      icon: Video,
      bgColor: "from-indigo-50 to-purple-50",
      videoSrc: "https://cdn.terang.ai/videos/latihan-demo.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/latihan-soal.webp",
      pattern: "squares",
      key: "gamified_practice",
      features: [
        t('interactive_game_based_learning'),
        t('achievement_badge_system'),
        t('progress_tracking'),
        t('instant_feedback')
      ],
      buttonText: t('start_free_practice'),
      gradient: "from-indigo-600 to-purple-600"
    },
    {
      title: t('pembimbing_ai'),
      description: t('ai_tutor_description'),
      icon: Brain,
      bgColor: "from-emerald-50 to-green-50",
      videoSrc: "https://cdn.terang.ai/videos/tutor-pembahasan-ai.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/tutor-pembahasan-ai.webp",
      pattern: "bokeh",
      key: "ai_tutor",
      features: [
        t('personal_ai_tutor'),
        t('step_by_step_explanations'),
        t('smart_learning_tips'),
        t('personalized_study_path')
      ],
      buttonText: t('try_ai_tutor'),
      gradient: "from-emerald-600 to-green-600"
    },
    {
      title: t('analisis_performa'),
      description: t('analisis_performa_description'),
      icon: BarChart3,
      bgColor: "from-blue-50 to-cyan-50",
      videoSrc: "https://cdn.terang.ai/videos/skill-insights.mp4",
      posterSrc: "https://cdn.terang.ai/images/landingpage/skill-insights.webp",
      pattern: "crosshatch",
      key: "performance_analysis",
      features: [
        t('comprehensive_analytics'),
        t('weakness_identification'),
        t('improvement_roadmap'),
        t('personalized_resources')
      ],
      buttonText: t('view_analytics'),
      gradient: "from-blue-600 to-cyan-600"
    }
  ];

  const tabs: TabConfig[] = [
    {
      id: 'ai-interview',
      label: t('ai_interview'),
      icon: AIVoiceIcon,
      features: getAIInterviewFeatures()
    },
    {
      id: 'exam-prep',
      label: t('exam_prep'),
      icon: ApplicationIcon,
      features: getExamPrepFeatures()
    }
  ];

  const currentFeatures = tabs.find(tab => tab.id === activeTab)?.features || [];

  const getNavigationLink = (tabId: string): string => {
    return tabId === 'ai-interview' ? '/available-interviews' : '/available-exams';
  };

  return (
    <section id="features" className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden pb-12">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Updated Header using styled-components */}
        <div className="pt-20">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full text-blue-800 text-sm font-medium mb-8 border border-blue-200">
              <Video className="w-5 h-5 mr-2" />
              {t('live_demo_experience')}
              <Sparkles className="w-5 h-5 ml-2" />
            </div>
            
            <TitleContainer>
              <Title>
                {t('see_how')}{" "}
                <TitleWrapper>
                  <StyledTitleSVG viewBox="0 0 504 106" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M1106,1813.61193 C1391.48757,1788.3249 1542.09692,1785.22818 1557.82804,1804.32178 C1581.42472,1832.96217 1297.6495,1822.13368 1191.16891,1835.26224 C1084.68832,1848.39079 1016.09991,1866.56524 1566,1841.45052"
                      strokeLinecap="round"
                      strokeWidth="43"
                      transform="translate(-1084 -1770)"
                    />
                  </StyledTitleSVG>
                  <TitleText>Terang AI</TitleText>
                </TitleWrapper>
                {" "}{t('works')}
              </Title>
            </TitleContainer>
            
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12">
              {t('revolutionary_learning')}
            </p>
          </div>
        </div>

        {/* Custom Tab Navigation with softer blue gradient */}
        <div className="flex justify-center mb-16">
          <div className="inline-flex bg-white/80 backdrop-blur-md p-2 rounded-2xl shadow-xl border border-white/20">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-3 px-3 py-3 rounded-xl font-semibold text-lg transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-blue-400 via-blue-400 to-indigo-400 text-white shadow-lg scale-105'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <tab.icon className={`w-12 h-12 ${activeTab === tab.id ? 'brightness-0 invert' : ''}`} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Features Content */}
        <div className="space-y-32">
          {currentFeatures.map((feature, index) => (
            <div 
              key={feature.key} 
              className={`flex flex-col lg:flex-row gap-16 items-center ${
                index % 2 === 0 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Video Side */}
              <div className="w-full lg:w-1/2">
                <LazyVideo
                  src={feature.videoSrc}
                  poster={feature.posterSrc}
                  title={feature.title}
                />
              </div>
              
              {/* Content Side */}
              <div className="w-full lg:w-1/2">
                <div className={`relative p-10 rounded-3xl bg-gradient-to-br ${feature.bgColor} border border-white/20 shadow-2xl backdrop-blur-sm overflow-hidden group hover:scale-105 transition-all duration-500`}>
                  {/* Pattern overlay */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent transform rotate-12 scale-150" />
                  </div>
                  
                  {/* Icon */}
                  <div className="relative z-10 mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-white/80 backdrop-blur-md shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <feature.icon className="w-8 h-8 text-gray-800" />
                    </div>
                  </div>
                  
                  {/* Title */}
                  <h3 className="relative z-10 text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                    {feature.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="relative z-10 text-gray-700 text-lg leading-relaxed mb-8">
                    {feature.description}
                  </p>
                  
                  {/* Feature List */}
                  <div className="relative z-10 space-y-4 mb-10">
                    {feature.features.map((featureItem, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-4">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        </div>
                        <span className="text-gray-800 font-medium">{featureItem}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <div className="relative z-10">
                    <a 
                      href={getNavigationLink(activeTab)}
                      className={`group/btn inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r ${feature.gradient} text-white font-bold text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 transform no-underline`}
                    >
                      {feature.buttonText}
                      <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default VideoShowcase;