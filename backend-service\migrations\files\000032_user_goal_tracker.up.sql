-- Create user_goal_tracker table
CREATE TABLE IF NOT EXISTS user_goal_tracker (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    verbal_reasoning INTEGER NOT NULL,
    quantitative_reasoning INTEGER NOT NULL,
    problem_solving INTEGER NOT NULL,
    passed_lpdp_tbs BOOLEAN NOT NULL,
    felt_helped BOOLEAN NOT NULL,
    helpfulness_rating INTEGER NOT NULL CHECK (helpfulness_rating >= 1 AND helpfulness_rating <= 10),
    most_helpful_aspect TEXT,
    improvement_suggestions TEXT,
    contact_consent BOOLEAN NOT NULL DEFAULT FALSE,
    phone_number VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_user_goal_tracker_email ON user_goal_tracker(email);
CREATE UNIQUE INDEX unique_user_goal_tracker_email ON user_goal_tracker(email);

-- Add comments to explain the fields
COMMENT ON TABLE user_goal_tracker IS 'Tracks user goals and feedback for LPDP exam preparation';
COMMENT ON COLUMN user_goal_tracker.verbal_reasoning IS 'Verbal reasoning score in TBS LPDP Batch 1';
COMMENT ON COLUMN user_goal_tracker.quantitative_reasoning IS 'Quantitative reasoning score in TBS LPDP Batch 1';
COMMENT ON COLUMN user_goal_tracker.problem_solving IS 'Problem solving score in TBS LPDP Batch 1';
COMMENT ON COLUMN user_goal_tracker.passed_lpdp_tbs IS 'Whether the user passed TBS LPDP Batch 1';
COMMENT ON COLUMN user_goal_tracker.felt_helped IS 'Whether the user felt helped in preparation for TBS LPDP Batch 1';
COMMENT ON COLUMN user_goal_tracker.helpfulness_rating IS 'Rating of how helpful Terang AI was (1-10)';
COMMENT ON COLUMN user_goal_tracker.most_helpful_aspect IS 'What aspect the user found most helpful';
COMMENT ON COLUMN user_goal_tracker.improvement_suggestions IS 'User suggestions for features to improve';
COMMENT ON COLUMN user_goal_tracker.contact_consent IS 'Whether the user consents to being contacted';
COMMENT ON COLUMN user_goal_tracker.phone_number IS 'User phone number if consent is given';
