package controller

import "github.com/gin-gonic/gin"

type CategoryControllerInterface interface {
	InsertCategory(*gin.Context)
	GetAllCategories(*gin.Context)
	GetOneCategory(*gin.Context)
	UpdateCategory(*gin.Context)
	DeleteCategory(*gin.Context)

	InsertExamCategory(*gin.Context)
	GetAllExamCategories(*gin.Context)
	GetOneExamCategory(*gin.Context)
	UpdateExamCategory(*gin.Context)
	DeleteExamCategory(*gin.Context)

	GetAllCategoryPublic(*gin.Context)
}
