package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"
	"github.com/redis/go-redis/v9"

	lib "github.com/terang-ai/backend-service/lib"
	model "github.com/terang-ai/backend-service/model/available_exams"
)

type AvailableExamRepository struct {
	Db    *sqlx.DB
	Redis *redis.Client
}

// AvailableExamPaginationInfo represents pagination metadata.
type AvailableExamPaginationInfo struct {
	TotalData   int `json:"total_data"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
}

// ExamBundle represents the exam_bundles table structure
type ExamBundle struct {
	ID                 string                 `db:"id" json:"id"`
	Name               string                 `db:"name" json:"name"`
	Description        []byte                 `db:"description" json:"description"`
	Price              float64                `db:"price" json:"price"`
	DiscountPercentage *int                   `db:"discount_percentage" json:"discount_percentage,omitempty"`
	ThumbnailURL       *string                `db:"thumbnail_url" json:"thumbnail_url,omitempty"`
	BannerURL          *string                `db:"banner_url" json:"banner_url,omitempty"`
	Visibility         model.VisibilityStatus `db:"visibility" json:"visibility"`
	ValidFrom          *time.Time             `db:"valid_from" json:"valid_from,omitempty"`
	ValidUntil         *time.Time             `db:"valid_until" json:"valid_until,omitempty"`
	Metadata           json.RawMessage        `db:"metadata" json:"metadata,omitempty"`
	CreatedAt          time.Time              `db:"created_at" json:"created_at"`
	ModifiedAt         time.Time              `db:"modified_at" json:"modified_at"`
	UserID             string                 `db:"user_id" json:"user_id"`
	IsPurchased        bool                   `db:"is_purchased" json:"is_purchased"`
	PurchaseDate       *time.Time             `db:"purchase_date" json:"purchase_date,omitempty"`
}

// ExamBundleWithExams combines bundle details with associated exams
type ExamBundleWithExams struct {
	ExamBundle ExamBundle                  `json:"bundle"`
	Exams      []model.AvailableExamV2     `json:"exams"`
	Pagination AvailableExamPaginationInfo `json:"pagination"`
}

// ExamWithBundleInfo represents an available exam with additional bundle information
type ExamWithBundleInfo struct {
	model.AvailableExam
	BundleInfo   []BundleInfo `json:"bundle_info,omitempty"`
	MediaURL     string       `json:"media_url,omitempty"`
	CategoryName string       `json:"category_name,omitempty"`
	CategoryID   string       `json:"category_id,omitempty"`
	IsPurchased  bool         `json:"is_purchased"`
	PurchaseDate time.Time    `json:"purchase_date,omitempty"`
}

// BundleInfo represents bundle details for an exam
type BundleInfo struct {
	BundleID           string    `json:"bundle_id"`
	BundleName         string    `json:"bundle_name"`
	BundleDescription  []byte    `json:"bundle_description"`
	BundlePrice        float64   `json:"bundle_price"`
	DiscountPercentage int       `json:"discount_percentage,omitempty"`
	ThumbnailURL       string    `json:"thumbnail_url,omitempty"`
	ValidFrom          time.Time `json:"valid_from,omitempty"`
	ValidUntil         time.Time `json:"valid_until,omitempty"`
}

func NewAvailableExamRepository(db *sqlx.DB, redis *redis.Client) AvailableExamsRepositoryInterface {
	return &AvailableExamRepository{Db: db, Redis: redis}
}

func (m *AvailableExamRepository) InsertAvailableExam(userId string, post model.PostAvailableExam) (*model.AvailableExam, error) {
	// Check if the database connection is nil
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedUserId := p.Sanitize(userId)
	sanitizedName := p.Sanitize(post.Name)
	sanitizedSubname := p.Sanitize(post.Subname)
	sanitizedDescription := p.Sanitize(post.Description)
	sanitizedType := p.Sanitize(string(post.Type))
	sanitizedSubject := p.Sanitize(string(post.Subject))

	// Prepare the SQL statement for insertion
	query := `
        INSERT INTO available_exams(id, user_id, name, subname, description, baseline_price, visibility, duration, type, subject)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
    `

	// Generate ULID for ID
	ulidId := lib.GenerateULID()

	// Set default visibility if not provided
	visibility := model.VisibilityDraft
	if post.Visibility != "" {
		visibility = model.VisibilityStatus(post.Visibility)
	}

	// Create a AvailableExam object to hold the returned data
	var availableExam model.AvailableExam

	// Execute the statement with named parameters
	err := m.Db.QueryRowx(query, ulidId, sanitizedUserId, sanitizedName, sanitizedSubname, sanitizedDescription, post.BaselinePrice, visibility, post.Duration, sanitizedType, sanitizedSubject).StructScan(&availableExam)
	if err != nil {
		log.Println("Error executing statement:", err)
		return nil, err
	}

	return &availableExam, nil
}

func (m *AvailableExamRepository) DeleteAvailableExam(id string) (bool, error) {
	// Prepare statement to check if the entity exists
	existsQuery := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE id = $1)", "available_exams")
	existsStmt, err := m.Db.Preparex(existsQuery)
	if err != nil {
		log.Printf("Error preparing %s existence check statement: %s\n", "availableExam", err)
		return false, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.Get(&exists, id)
	if err != nil {
		log.Printf("Error checking if %s exists: %s\n", "availableExam", err)
		return false, err
	}

	if !exists {
		return false, fmt.Errorf("%s with ID %s does not exist", "availableExam", id)
	}

	// Prepare statement to delete the entity
	deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE id = $1", "available_exams")
	deleteStmt, err := m.Db.Preparex(deleteQuery)
	if err != nil {
		log.Printf("Error preparing %s deletion statement: %s\n", "availableExam", err)
		return false, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.Exec(id)
	if err != nil {
		log.Printf("Error deleting %s: %s\n", "availableExam", err)
		return false, err
	}

	return true, nil
}

func (m *AvailableExamRepository) GetAllAvailableExams(page int, pageSize int) ([]model.AvailableExam, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := "SELECT * FROM available_exams ORDER BY id LIMIT $1 OFFSET $2"

	var dataPool []model.AvailableExam
	err := m.Db.Select(&dataPool, query, pageSize, offset)
	if err != nil {
		log.Println("Error querying data:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Count total number of availableExam
	totalDataQuery := "SELECT COUNT(*) FROM available_exams"
	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery)
	if err != nil {
		log.Println("Error retrieving total availableExam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *AvailableExamRepository) GetAllAvailableExamsV2(email string, page int, pageSize int, examType model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := `
	WITH latest_order AS (
		SELECT 
			exam_id, 
			status, 
			created_at,
			ROW_NUMBER() OVER (PARTITION BY exam_id ORDER BY created_at DESC) as rn
		FROM orders
		WHERE client_id = (SELECT id FROM users WHERE email = $1)
	)
	SELECT 
		ae.*,
		COALESCE(m.url, '') AS media_url,
		c.name AS category_name,
		CASE 
			WHEN lo.status = 'COMPLETED' THEN TRUE
			ELSE FALSE
		END AS is_purchased,
		lo.created_at AS purchase_date
	FROM 
		available_exams ae
	LEFT JOIN
		media m ON ae.id = m.available_exam_id
	LEFT JOIN 
		exam_categories ec ON ae.id = ec.exam_id
	LEFT JOIN 
		categories c ON ec.category_id = c.id
	LEFT JOIN 
		latest_order lo ON ae.id = lo.exam_id AND lo.rn = 1
	WHERE 
		ae.type = $2
	ORDER BY 
		ae.id
	LIMIT $3 OFFSET $4`

	var dataPool []model.AvailableExamV2
	err := m.Db.Select(&dataPool, query, email, examType, pageSize, offset)
	if err != nil {
		log.Println("Error querying data:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	totalDataQuery := `
	SELECT COUNT(DISTINCT ae.id)
	FROM 
		available_exams ae
	LEFT JOIN
		media m ON ae.id = m.available_exam_id
	LEFT JOIN 
		exam_categories ec ON ae.id = ec.exam_id
	LEFT JOIN 
		categories c ON ec.category_id = c.id
	LEFT JOIN 
		orders o ON ae.id = o.exam_id AND o.client_id = (SELECT id FROM users WHERE email = $2)
	WHERE 
		ae.type = $1`

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, examType, email)
	if err != nil {
		log.Println("Error retrieving total available exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *AvailableExamRepository) GetAvailableExamBundlesV2(email string, page int, pageSize int, examType model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize

	// Define a local struct that extends AvailableExamV2 with the ClientEmail field
	type ExtendedExamV2 struct {
		model.AvailableExamV2
		ClientEmail *string `db:"client_email"`
	}

	// Step 1: Get all exams, with bundle information if available
	query := `
    WITH bundle_items AS (
        SELECT 
            ebi.exam_id,
            eb.id AS bundle_id,
            eb.name AS bundle_name,
            eb.price AS bundle_price,
            eb.discount_percentage
        FROM 
            exam_bundle_items ebi
        INNER JOIN
            exam_bundles eb ON ebi.bundle_id = eb.id
        WHERE 
            eb.visibility = 'PUBLIC'
    ),
    latest_exam_order AS (
        SELECT 
            exam_id, 
            status, 
            created_at,
            client_email,
            ROW_NUMBER() OVER (PARTITION BY exam_id ORDER BY created_at DESC) as rn
        FROM orders
        WHERE client_id = (SELECT id FROM users WHERE email = $1) OR client_email = $1
    ),
    free_daerah_3t_categories AS (
        SELECT 
            fd.category_id
        FROM 
            free_daerah_3t fd
        WHERE 
            fd.user_email = $1
            AND fd.is_active = true
    ),
    free_daerah_3t_exams AS (
        SELECT DISTINCT
            ec.exam_id,
            true AS is_free_access
        FROM 
            exam_categories ec
        JOIN
            free_daerah_3t_categories fdc ON ec.category_id = fdc.category_id
    )
    SELECT 
        ae.*,
        COALESCE(m.url, '') AS media_url,
        c.name AS category_name,
        c.id AS category_id,
        c.image_url AS category_image_url,
        bi.bundle_id,
        bi.bundle_name,
        bi.bundle_price,
        bi.discount_percentage,
        COALESCE(fde.is_free_access, false) AS is_free_access,
        CASE 
            WHEN leo.status = 'COMPLETED' THEN TRUE
            ELSE FALSE
        END AS is_purchased,
        leo.created_at AS purchase_date,
        leo.client_email
    FROM 
        available_exams ae
    LEFT JOIN
        bundle_items bi ON ae.id = bi.exam_id
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id
    LEFT JOIN 
        latest_exam_order leo ON ae.id = leo.exam_id AND leo.rn = 1
    LEFT JOIN
        free_daerah_3t_exams fde ON ae.id = fde.exam_id
    WHERE 
        ae.type = $3
    ORDER BY 
        ae.id
    LIMIT $2 OFFSET $4`

	// Create a struct to hold the exam data plus bundle info
	type ExamWithBundleData struct {
		ExtendedExamV2
		BundleID           *string  `db:"bundle_id"`           // Nullable
		BundleName         *string  `db:"bundle_name"`         // Nullable
		BundlePrice        *float64 `db:"bundle_price"`        // Nullable
		DiscountPercentage *int     `db:"discount_percentage"` // Nullable
		IsFreeAccess       bool     `db:"is_free_access"`      // Boolean from query
	}

	var examsWithBundleData []ExamWithBundleData
	err := m.Db.Select(&examsWithBundleData, query, email, pageSize, examType, offset)
	if err != nil {
		log.Println("Error querying exams with bundle info:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Step 2: Get all purchased bundles for this user
	purchasedBundlesQuery := `
    SELECT 
        o.bundle_id
    FROM 
        orders o
    WHERE 
        (o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1)
        AND o.bundle_id IS NOT NULL
        AND o.status = 'COMPLETED'`

	var purchasedBundleIDs []string
	err = m.Db.Select(&purchasedBundleIDs, purchasedBundlesQuery, email)
	if err != nil {
		log.Println("Error querying purchased bundles:", err)
		// Continue with the process, just log the error
	}

	// Create a map for quick lookup
	purchasedBundleMap := make(map[string]bool)
	for _, bundleID := range purchasedBundleIDs {
		purchasedBundleMap[bundleID] = true
	}

	log.Printf("Found %d purchased bundles for user %s: %v", len(purchasedBundleIDs), email, purchasedBundleIDs)

	// Step 3: Process exams
	var result []model.AvailableExamV2
	for _, examWithBundle := range examsWithBundleData {
		// Check if this exam's bundle has been purchased
		if examWithBundle.BundleID != nil && purchasedBundleMap[*examWithBundle.BundleID] {
			log.Printf("Marking exam %s as purchased because it's in purchased bundle %s",
				examWithBundle.Id, *examWithBundle.BundleID)
			examWithBundle.IsPurchased = true
			// If purchase_date is nil, set it to current time as a fallback
			if examWithBundle.PurchaseDate == nil {
				now := time.Now()
				examWithBundle.PurchaseDate = &now
			}
		}

		// Set price to 0 for free access exams - without changing the purchased status
		if examWithBundle.IsFreeAccess && examWithBundle.BaselinePrice != nil {
			zeroPrice := 0.0
			examWithBundle.BaselinePrice = &zeroPrice
		}

		// Transfer the IsFreeAccess value to the model
		// Handle type conversion: If IsFreeAccess is *string in the model
		if examWithBundle.IsFreeAccess {
			freeAccessStr := "true"
			examWithBundle.ExtendedExamV2.IsFreeAccess = &freeAccessStr
		} else {
			freeAccessStr := "false"
			examWithBundle.ExtendedExamV2.IsFreeAccess = &freeAccessStr
		}

		// Add to results
		result = append(result, examWithBundle.AvailableExamV2)
	}

	// Count total exams
	totalDataQuery := `
    SELECT COUNT(*)
    FROM 
        available_exams
    WHERE 
        type = $1`

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, examType)
	if err != nil {
		log.Println("Error retrieving total exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return result, paginationInfo, nil
}

// GetPublicAvailableExamBundles returns a flat list of public exams, with bundle information if available
// This is similar to GetAvailableExamBundlesV2 but doesn't require an email
func (m *AvailableExamRepository) GetPublicAvailableExamBundles(page int, pageSize int, examType model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize

	// Query to get public exams with their bundle information
	query := `
    WITH bundle_items AS (
        SELECT 
            ebi.exam_id,
            eb.id AS bundle_id,
            eb.name AS bundle_name,
            eb.price AS bundle_price,
            eb.discount_percentage
        FROM 
            exam_bundle_items ebi
        INNER JOIN
            exam_bundles eb ON ebi.bundle_id = eb.id
        WHERE 
            eb.visibility = 'PUBLIC'
    )
    SELECT 
        ae.*,
        COALESCE(m.url, '') AS media_url,
        c.name AS category_name,
        c.id AS category_id,
        c.image_url AS category_image_url,
        bi.bundle_id,
        bi.bundle_name,
        bi.bundle_price,
        bi.discount_percentage,
        FALSE AS is_purchased,  -- Since no user is authenticated, always false
        NULL AS purchase_date,
        'false' AS is_free_access -- Default to false since we don't have user context
    FROM 
        available_exams ae
    LEFT JOIN
        bundle_items bi ON ae.id = bi.exam_id
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id
    WHERE 
        ae.type = $1
        AND ae.visibility = 'PUBLIC'  -- Only show public exams
    ORDER BY 
        ae.id
    LIMIT $2 OFFSET $3`

	// Create a struct to hold the exam data plus bundle info
	type ExamWithBundleData struct {
		model.AvailableExamV2
		BundleID           *string  `db:"bundle_id"`           // Nullable
		BundleName         *string  `db:"bundle_name"`         // Nullable
		BundlePrice        *float64 `db:"bundle_price"`        // Nullable
		DiscountPercentage *int     `db:"discount_percentage"` // Nullable
	}

	var examsWithBundleData []ExamWithBundleData
	err := m.Db.Select(&examsWithBundleData, query, examType, pageSize, offset)
	if err != nil {
		log.Println("Error querying public exams with bundle info:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Process exams
	var result []model.AvailableExamV2
	for _, examWithBundle := range examsWithBundleData {
		// Add to results - the base exam data is already populated
		result = append(result, examWithBundle.AvailableExamV2)
	}

	// Count total public exams of the given type
	totalDataQuery := `
    SELECT COUNT(*)
    FROM 
        available_exams
    WHERE 
        type = $1
        AND visibility = 'PUBLIC'`

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, examType)
	if err != nil {
		log.Println("Error retrieving total public exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return result, paginationInfo, nil
}

// GetAllPurchasedExamsIncludingBundles retrieves all purchased exams, including those from purchased bundles
// UpdatedAvailableExamV2 struct with ClientEmail field
type UpdatedAvailableExamV2 struct {
	model.AvailableExamV2
	ClientEmail *string `json:"client_email,omitempty" db:"client_email"`
}

func (m *AvailableExamRepository) GetAllPurchasedExamsIncludingBundles(email string, page int, pageSize int, examType ...model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters: page and pageSize must be greater than 0")
	}

	offset := (page - 1) * pageSize

	// Define a local struct with ClientEmail
	type ExtendedExamV2 struct {
		model.AvailableExamV2
		ClientEmail *string `db:"client_email"`
	}

	// Enhanced query to include both directly purchased exams and exams from purchased bundles
	baseQuery := `
    WITH purchased_bundles AS (
        SELECT 
            o.bundle_id,
            o.created_at AS purchase_date,
            o.client_email
        FROM 
            orders o
        WHERE 
            (o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1)
            AND o.bundle_id IS NOT NULL
            AND o.status = 'COMPLETED'
    ),
    directly_purchased_exams AS (
        SELECT 
            ae.id,
            MIN(o.created_at) AS purchase_date,
            o.client_email
        FROM 
            available_exams ae
        INNER JOIN 
            orders o ON ae.id = o.exam_id AND o.status = 'COMPLETED'
        WHERE
            o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1
        GROUP BY ae.id, o.client_email
    ),
    bundle_purchased_exams AS (
        SELECT 
            ae.id,
            MIN(pb.purchase_date) AS purchase_date,
            pb.client_email
        FROM 
            available_exams ae
        INNER JOIN
            exam_bundle_items ebi ON ae.id = ebi.exam_id
        INNER JOIN
            purchased_bundles pb ON ebi.bundle_id = pb.bundle_id
        GROUP BY ae.id, pb.client_email
    ),
    all_purchase_dates AS (
        SELECT id, purchase_date, client_email FROM directly_purchased_exams
        UNION ALL
        SELECT id, purchase_date, client_email FROM bundle_purchased_exams
    ),
    earliest_purchase_dates AS (
        SELECT 
            id,
            MIN(purchase_date) AS earliest_purchase_date,
            ARRAY_AGG(client_email) AS client_emails
        FROM 
            all_purchase_dates
        GROUP BY id
    )
    SELECT DISTINCT 
        ae.*,
        COALESCE(m.url, '') AS media_url,
        c.name AS category_name,
        c.id AS category_id,
        c.image_url AS category_image_url,
        TRUE AS is_purchased,
        epd.earliest_purchase_date AS purchase_date,
        CASE 
            WHEN $1 = ANY(epd.client_emails) THEN $1
            ELSE epd.client_emails[1]
        END AS client_email
    FROM 
        available_exams ae
    INNER JOIN
        earliest_purchase_dates epd ON ae.id = epd.id
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id`

	var query string
	var args []interface{}

	// Add type filter if provided
	if len(examType) > 0 && examType[0] != "" {
		query = baseQuery + " WHERE ae.type = $4 ORDER BY ae.id LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset, examType[0]}
	} else {
		query = baseQuery + " ORDER BY ae.id LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset}
	}

	var dataPool []ExtendedExamV2
	err := m.Db.Select(&dataPool, query, args...)
	if err != nil {
		log.Println("Error querying all purchased exams:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Convert to standard AvailableExamV2
	result := make([]model.AvailableExamV2, len(dataPool))
	for i, exam := range dataPool {
		// Just copy the base AvailableExamV2 fields without trying to set ClientEmail
		result[i] = exam.AvailableExamV2
	}

	// Base count query
	baseTotalDataQuery := `
    WITH purchased_bundles AS (
        SELECT 
            o.bundle_id
        FROM 
            orders o
        WHERE 
            (o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1)
            AND o.bundle_id IS NOT NULL
            AND o.status = 'COMPLETED'
    ),
    directly_purchased_exams AS (
        SELECT 
            ae.id,
            ae.type
        FROM 
            available_exams ae
        INNER JOIN 
            orders o ON ae.id = o.exam_id AND o.status = 'COMPLETED'
        WHERE
            o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1
    ),
    bundle_purchased_exams AS (
        SELECT 
            ae.id,
            ae.type
        FROM 
            available_exams ae
        INNER JOIN
            exam_bundle_items ebi ON ae.id = ebi.exam_id
        INNER JOIN
            purchased_bundles pb ON ebi.bundle_id = pb.bundle_id
    ),
    all_purchased_exams AS (
        SELECT id, type FROM directly_purchased_exams
        UNION
        SELECT id, type FROM bundle_purchased_exams
    )
    SELECT 
        COUNT(DISTINCT id) 
    FROM 
        all_purchased_exams`

	var totalDataQuery string
	var countArgs []interface{}

	// Add type filter to count query if provided
	if len(examType) > 0 && examType[0] != "" {
		totalDataQuery = baseTotalDataQuery + " WHERE type = $2"
		countArgs = []interface{}{email, examType[0]}
	} else {
		totalDataQuery = baseTotalDataQuery
		countArgs = []interface{}{email}
	}

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, countArgs...)
	if err != nil {
		log.Println("Error retrieving total purchased exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return result, paginationInfo, nil
}

func (m *AvailableExamRepository) GetAllPurchasedExamsByEmail(email string, page int, pageSize int, examType ...model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters: page and pageSize must be greater than 0")
	}

	offset := (page - 1) * pageSize

	// Enhanced base query to include category ID and image_url
	baseQuery := `
    SELECT DISTINCT 
        ae.*,
        COALESCE(m.url, '') AS media_url,
        c.name AS category_name,
        c.id AS category_id,
        c.image_url AS category_image_url,
        TRUE AS is_purchased,  -- Since we're filtering only for purchased exams, this is always true
        o.created_at AS purchase_date
    FROM 
        available_exams ae
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id
    INNER JOIN 
        orders o ON ae.id = o.exam_id AND o.status = 'COMPLETED'
    INNER JOIN 
        users u ON u.email = $1 AND u.id = o.client_id`

	var query string
	var args []interface{}

	// Add type filter if provided
	if len(examType) > 0 && examType[0] != "" {
		query = baseQuery + " WHERE ae.type = $4 ORDER BY ae.id LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset, examType[0]}
	} else {
		query = baseQuery + " ORDER BY ae.id LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset}
	}

	var dataPool []model.AvailableExamV2
	err := m.Db.Select(&dataPool, query, args...)
	if err != nil {
		log.Println("Error querying purchased exams:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Base count query
	baseTotalDataQuery := `
    SELECT 
        COUNT(DISTINCT ae.id) 
    FROM 
        available_exams ae
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id
    INNER JOIN 
        orders o ON ae.id = o.exam_id AND o.status = 'COMPLETED'
    INNER JOIN 
        users u ON u.email = $1 AND u.id = o.client_id`

	var totalDataQuery string
	var countArgs []interface{}

	// Add type filter to count query if provided
	if len(examType) > 0 && examType[0] != "" {
		totalDataQuery = baseTotalDataQuery + " WHERE ae.type = $2"
		countArgs = []interface{}{email, examType[0]}
	} else {
		totalDataQuery = baseTotalDataQuery
		countArgs = []interface{}{email}
	}

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, countArgs...)
	if err != nil {
		log.Println("Error retrieving total available exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *AvailableExamRepository) GetAllExamsTaken(email string, page int, pageSize int, examType ...model.ExamType) ([]model.ExamSessionData, AvailableExamPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, errors.New("invalid pagination parameters: page and pageSize must be greater than 0")
	}

	offset := (page - 1) * pageSize

	// Use a CTE to get the most recent completed session for each exam
	// Enhanced query to include category ID and image_url
	baseQuery := `
    WITH latest_sessions AS (
        SELECT 
            es.exam_id,
            es.session_id,
            es.status AS session_status,
            es.created_at,
            ROW_NUMBER() OVER (PARTITION BY es.exam_id ORDER BY es.created_at DESC) as rn
        FROM 
            exam_sessions es
        INNER JOIN 
            users u ON u.email = $1 AND u.id = es.user_id
        WHERE 
            es.status = 'COMPLETED'
    )
    SELECT 
        ae.*,
        COALESCE(m.url, '') AS media_url,
        c.name AS category_name,
        c.id AS category_id,
        c.image_url AS category_image_url,
        TRUE AS is_purchased,
        ls.created_at AS purchase_date,
        ls.session_id,
        ls.session_status,
        COALESCE(sc.score, 0) AS score
    FROM 
        available_exams ae
    INNER JOIN
        latest_sessions ls ON ae.id = ls.exam_id AND ls.rn = 1
    LEFT JOIN
        media m ON ae.id = m.available_exam_id
    LEFT JOIN 
        exam_categories ec ON ae.id = ec.exam_id
    LEFT JOIN 
        categories c ON ec.category_id = c.id
    LEFT JOIN
        exam_scores sc ON ls.session_id = sc.session_id`

	var query string
	var args []interface{}

	// Add type filter if provided
	if len(examType) > 0 && examType[0] != "" {
		query = baseQuery + " WHERE ae.type = $4 ORDER BY ls.created_at DESC LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset, examType[0]}
	} else {
		query = baseQuery + " ORDER BY ls.created_at DESC LIMIT $2 OFFSET $3"
		args = []interface{}{email, pageSize, offset}
	}

	var dataPool []model.ExamSessionData
	err := m.Db.Select(&dataPool, query, args...)
	if err != nil {
		log.Println("Error querying taken exams:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	// Count query with the same CTE approach
	baseTotalDataQuery := `
    WITH latest_sessions AS (
        SELECT 
            es.exam_id,
            ROW_NUMBER() OVER (PARTITION BY es.exam_id ORDER BY es.created_at DESC) as rn
        FROM 
            exam_sessions es
        INNER JOIN 
            users u ON u.email = $1 AND u.id = es.user_id
        WHERE 
            es.status = 'COMPLETED'
    )
    SELECT 
        COUNT(DISTINCT ae.id) 
    FROM 
        available_exams ae
    INNER JOIN
        latest_sessions ls ON ae.id = ls.exam_id AND ls.rn = 1`

	var totalDataQuery string
	var countArgs []interface{}

	// Add type filter to count query if provided
	if len(examType) > 0 && examType[0] != "" {
		totalDataQuery = baseTotalDataQuery + " WHERE ae.type = $2"
		countArgs = []interface{}{email, examType[0]}
	} else {
		totalDataQuery = baseTotalDataQuery
		countArgs = []interface{}{email}
	}

	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, countArgs...)
	if err != nil {
		log.Println("Error retrieving total taken exam count:", err)
		return nil, AvailableExamPaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *AvailableExamRepository) GetOneAvailableExam(id string) (model.AvailableExam, error) {
	query := "SELECT * FROM available_exams WHERE id = $1"

	var availableExam model.AvailableExam
	err := m.Db.Get(&availableExam, query, id)
	if err != nil {
		log.Println("Error fetching data:", err)
		return model.AvailableExam{}, err
	}

	return availableExam, nil
}

func (m *AvailableExamRepository) GetOneAvailableExamDaerah3T(examId string, userEmail string) (model.AvailableExamV2, bool, error) {
	// First, check if the user has free access to this exam via daerah 3T
	checkQuery := `
		SELECT EXISTS (
			SELECT 1
			FROM free_daerah_3t fd
			JOIN exam_categories ec ON fd.category_id = ec.category_id
			WHERE ec.exam_id = $1 
			AND fd.user_email = $2
			AND fd.is_active = true
			LIMIT 1
		) AS has_access
	`

	var hasAccess bool
	err := m.Db.Get(&hasAccess, checkQuery, examId, userEmail)
	if err != nil {
		log.Printf("Error checking daerah 3T access: %v", err)
		return model.AvailableExamV2{}, false, err
	}

	// If the user doesn't have free access, return empty result with hasAccess = false
	if !hasAccess {
		return model.AvailableExamV2{}, false, nil
	}

	// User has access, get the exam with baseline_price = 0
	query := `
		SELECT 
			e.id,
			e.name,
			e.subname,
			e.description,
			0 AS baseline_price,
			e.visibility,
			e.duration,
			e.type,
			e.subject,
			e.created_at,
			e.modified_at,
			COALESCE(m.url, '') AS media_url,
			c.name AS category_name,
			c.id AS category_id,
			c.image_url AS category_image_url,
			TRUE AS is_free_access,
			TRUE AS is_purchased
		FROM available_exams e
		LEFT JOIN media m ON e.id = m.available_exam_id
		LEFT JOIN exam_categories ec ON e.id = ec.exam_id
		LEFT JOIN categories c ON ec.category_id = c.id
		WHERE e.id = $1
		LIMIT 1
	`

	var availableExam model.AvailableExamV2
	err = m.Db.Get(&availableExam, query, examId)
	if err != nil {
		log.Printf("Error fetching free exam data: %v", err)
		return model.AvailableExamV2{}, false, err
	}

	return availableExam, true, nil
}

func (m *AvailableExamRepository) UpdateAvailableExam(id string, post model.UpdateAvailableExam) (*model.AvailableExam, error) {
	// Check if the entity exists
	existsQuery := "SELECT EXISTS(SELECT 1 FROM available_exams WHERE id = $1)"
	var exists bool
	err := m.Db.Get(&exists, existsQuery, id)
	if err != nil {
		log.Printf("Error checking if availableExam exists: %s\n", err)
		return nil, err
	}

	if !exists {
		return nil, fmt.Errorf("availableExam with ID %s does not exist", id)
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedName := lib.SanitizeOptionalString(post.Name, p)
	sanitizedSubname := lib.SanitizeOptionalString(post.Subname, p)
	sanitizedDescription := lib.SanitizeOptionalString(post.Description, p)
	sanitizedBaselinePrice := post.BaselinePrice
	sanitizedDuration := post.Duration
	sanitizedType := post.Type
	sanitizedSubject := post.Subject

	// Set default visibility if not provided
	visibility := model.VisibilityDraft
	if post.Visibility != nil {
		visibility = *post.Visibility
	}

	// Prepare update query
	updateQuery := `
		UPDATE available_exams 
		SET name = COALESCE($2, name),
			subname = COALESCE($3, subname),
			description = COALESCE($4, description),
			baseline_price = COALESCE($5, baseline_price),
			visibility = COALESCE($6, visibility),
			duration = COALESCE($7, duration),
			type = COALESCE($8, type),
			subject = COALESCE($8, subject)
		WHERE id = $1
		RETURNING *
	`

	// Create an AvailableExam object to hold the returned data
	var updatedExam model.AvailableExam

	// Execute the update statement and scan the result into the AvailableExam model
	err = m.Db.QueryRowx(updateQuery, id, sanitizedName, sanitizedSubname, sanitizedDescription, sanitizedBaselinePrice, visibility, sanitizedDuration, sanitizedType, sanitizedSubject).StructScan(&updatedExam)
	if err != nil {
		log.Printf("Error updating availableExam: %s\n", err)
		return nil, err
	}

	return &updatedExam, nil
}

func (m *AvailableExamRepository) GetAllAvailableExamsWithCache(ctx context.Context, page int, pageSize int) ([]model.AvailableExam, AvailableExamPaginationInfo, bool, error) {
	if page < 1 || pageSize < 1 {
		return nil, AvailableExamPaginationInfo{}, false, errors.New("invalid pagination parameters")
	}

	// Generate the cache key
	currentHour := time.Now().Format("2006010215") // Format as YYYYMMDDHH
	cacheKey := fmt.Sprintf("%s_page_%d_size_%d_hour_%s", "available_exams", page, pageSize, currentHour)

	// Try to fetch from cache first
	cachedData, err := m.Redis.Get(ctx, cacheKey).Result()
	if err == nil {
		log.Println("Returning data from cache for key:", cacheKey)

		// Unmarshal the cached data into the expected structure
		var cachedExams []model.AvailableExam
		var cachedPaginationInfo AvailableExamPaginationInfo

		err := lib.UnmarshalCachedData(cachedData, &cachedExams, &cachedPaginationInfo)
		if err != nil {
			return nil, AvailableExamPaginationInfo{}, false, err
		}

		// Return data from cache and indicate it was a cache hit
		return cachedExams, cachedPaginationInfo, true, nil
	} else if err != redis.Nil {
		// Some other error occurred (besides cache miss)
		log.Println("Error accessing cache:", err)
	}

	// Cache miss, proceed to fetch from the database
	log.Println("Cache miss for key:", cacheKey)

	offset := (page - 1) * pageSize
	query := "SELECT * FROM available_exams ORDER BY id LIMIT $1 OFFSET $2"

	var dataPool []model.AvailableExam
	err = m.Db.Select(&dataPool, query, pageSize, offset)
	if err != nil {
		log.Println("Error querying data:", err)
		return nil, AvailableExamPaginationInfo{}, false, err
	}

	// Count total number of available exams
	totalDataQuery := "SELECT COUNT(*) FROM available_exams"
	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery)
	if err != nil {
		log.Println("Error retrieving total available exam count:", err)
		return nil, AvailableExamPaginationInfo{}, false, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := AvailableExamPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	// Cache the result for 5 minutes
	cacheDuration := 5 * time.Minute
	cachedResult, err := lib.MarshalCachedData(dataPool, paginationInfo)
	if err == nil {
		m.Redis.Set(ctx, cacheKey, cachedResult, cacheDuration)
	} else {
		log.Println("Error marshaling data for cache:", err)
	}

	// Return data from the database and indicate it was not a cache hit
	return dataPool, paginationInfo, false, nil
}

// InsertExamQuestionAnswerHint inserts a new hint for an exam into the database
func (m *AvailableExamRepository) InsertExamQuestionAnswerHint(hint model.PostExamQuestionAnswerHint) (interface{}, error) {
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedExamId := p.Sanitize(hint.ExamId)

	// Generate ULID for ID
	ulidId := lib.GenerateULID()

	// Prepare the SQL statement for insertion
	query := `
        INSERT INTO exam_question_answer_hints (id, exam_id, data, passing_grade)
        VALUES ($1, $2, $3::jsonb, $4::jsonb)
    `

	// Execute the statement using hint.Data as the JSONB input
	_, err := m.Db.Exec(query, ulidId, sanitizedExamId, hint.Data, hint.PassingGrade)
	if err != nil {
		log.Println("Error inserting exam question answer hint:", err)
		return nil, err
	}

	return hint.Data, nil
}

// GetExamQuestionAnswerHints retrieves hints for a specific exam from the database
// fetchType can be either "LIMIT" (returns latest record) or "FULL" (returns all records)
func (m *AvailableExamRepository) GetExamQuestionAnswerHints(examId string, fetchType string) ([]model.ExamQuestionAnswerHint, error) {
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedExamId := p.Sanitize(examId)

	// Base query
	query := `
        SELECT id, exam_id, data, passing_grade
        FROM exam_question_answer_hints
        WHERE exam_id = $1
		ORDER BY id DESC
    `

	// Add LIMIT clause based on fetchType
	if fetchType == "LIMIT" {
		query += " LIMIT 1"
	}

	var hints []model.ExamQuestionAnswerHint
	err := m.Db.Select(&hints, query, sanitizedExamId)
	if err != nil {
		log.Println("Error fetching exam question answer hints:", err)
		return nil, err
	}

	return hints, nil
}

// GetExamQuestionAnswerHintsBySessionId retrieves hints for a specific exam session from the database
func (m *AvailableExamRepository) GetExamQuestionAnswerHintsBySessionId(sessionId string) ([]model.ExamQuestionAnswerHint, error) {
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedSessionId := p.Sanitize(sessionId)

	// Query for hints using session_id by joining with the exam_sessions table
	query := `
        SELECT h.id, h.exam_id, h.data, h.passing_grade
        FROM exam_question_answer_hints h
        JOIN exam_sessions s ON h.exam_id = s.exam_id
        WHERE s.session_id = $1 
		ORDER BY h.id DESC
		LIMIT 1 
    `

	var hints []model.ExamQuestionAnswerHint
	err := m.Db.Select(&hints, query, sanitizedSessionId)
	if err != nil {
		log.Println("Error fetching exam question answer hints:", err)
		return nil, err
	}

	return hints, nil
}

// QuestionData represents the structure of a single question in the data field
type QuestionData struct {
	ID       string `json:"id"`
	Question []struct {
		Contents []struct {
			Type    string `json:"type"`
			Content string `json:"content"`
		} `json:"contents"`
	} `json:"question"`
	Options struct {
		Values []struct {
			ID   string `json:"id"`
			Data []struct {
				Contents []struct {
					Type    string `json:"type"`
					Content string `json:"content"`
				} `json:"contents"`
			} `json:"data"`
			IsCorrect bool `json:"is_correct"`
		} `json:"values"`
		Shuffle bool `json:"shuffle"`
	} `json:"options"`
	Metadata []struct {
		Name  string `json:"name"`
		Level int    `json:"level"`
		Value string `json:"value"`
	} `json:"metadata"`
	Hints       []string `json:"hints"`
	Explanation []struct {
		Contents []struct {
			Type    string `json:"type"`
			Content string `json:"content"`
		} `json:"contents"`
	} `json:"explanation"`
}

func (m *AvailableExamRepository) GetExamQuestionAnswerHintsBySessionIdBySubject(sessionId string, subject string) ([]model.ExamQuestionAnswerHint, error) {
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	log.Printf("Starting hint retrieval - session: %s, subject: %s,", sessionId, subject)

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedSessionId := p.Sanitize(sessionId)
	sanitizedSubject := p.Sanitize(subject)

	log.Printf("Sanitized inputs: sessionId='%s', subject='%s'",
		sanitizedSessionId, sanitizedSubject)

	query := `
        SELECT h.id, h.exam_id, h.data, h.passing_grade
        FROM exam_question_answer_hints h
        JOIN exam_sessions s ON h.exam_id = s.exam_id
        WHERE s.session_id = $1 AND s.subject = $2
        ORDER BY h.id DESC
        LIMIT 1
    `

	var hints []model.ExamQuestionAnswerHint
	err := m.Db.Select(&hints, query, sanitizedSessionId, sanitizedSubject)
	if err != nil {
		log.Printf("Error fetching exam question answer hints: %v", err)
		return nil, fmt.Errorf("database query error: %w", err)
	}

	log.Println(hints)

	log.Printf("Retrieved %d hints from database", len(hints))

	return hints, nil
}
