package custom

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
)

// Configuration structures and loader
type SubjectInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Key  string `json:"key"`
}

type ExamType struct {
	ID       string        `json:"id"`
	Name     string        `json:"name"`
	Subjects []SubjectInfo `json:"subjects"`
}

type ExamConfiguration struct {
	ExamTypes map[string]ExamType `json:"examTypes"`
}

var (
	examConfig     *ExamConfiguration
	configMutex    sync.RWMutex
	configInitOnce sync.Once
)

// loadExamConfiguration loads the exam configuration from a file or environment
func loadExamConfiguration() (*ExamConfiguration, error) {
	configMutex.RLock()
	if examConfig != nil {
		defer configMutex.RUnlock()
		return examConfig, nil
	}
	configMutex.RUnlock()

	configMutex.Lock()
	defer configMutex.Unlock()

	// Check again in case another goroutine initialized it while we were waiting
	if examConfig != nil {
		return examConfig, nil
	}

	// Initialize only once
	var initErr error
	configInitOnce.Do(func() {
		// Try to load from environment or file
		configPath := os.Getenv("EXAM_CONFIG_PATH")
		if configPath == "" {
			// Use default config if no path provided
			examConfig = getDefaultExamConfig()
			return
		}

		// Load from file
		data, err := os.ReadFile(configPath)
		if err != nil {
			initErr = fmt.Errorf("failed to read exam config file: %w", err)
			// Fall back to default config
			examConfig = getDefaultExamConfig()
			return
		}

		// Parse the JSON
		config := &ExamConfiguration{}
		if err := json.Unmarshal(data, config); err != nil {
			initErr = fmt.Errorf("failed to parse exam config: %w", err)
			// Fall back to default config
			examConfig = getDefaultExamConfig()
			return
		}

		examConfig = config
	})

	return examConfig, initErr
}

// getDefaultExamConfig provides a default configuration if none is available
func getDefaultExamConfig() *ExamConfiguration {
	return &ExamConfiguration{
		ExamTypes: map[string]ExamType{
			"CPNS": {
				ID:   "cpns",
				Name: "CPNS",
				Subjects: []SubjectInfo{
					{ID: "twk", Name: "Tes Wawasan Kebangsaan", Key: "twk"},
					{ID: "tiu", Name: "Tes Intelegensia Umum", Key: "tiu"},
					{ID: "tkp", Name: "Tes Karakteristik Pribadi", Key: "tkp"},
				},
			},
			"LPDP": {
				ID:   "lpdp",
				Name: "LPDP",
				Subjects: []SubjectInfo{
					{ID: "kepribadian", Name: "Kepribadian", Key: "kepribadian"},
					{ID: "pemecahan_masalah", Name: "Pemecahan Masalah", Key: "pemecahan_masalah"},
					{ID: "penalaran_kuantitatif", Name: "Penalaran Kuantitatif", Key: "penalaran_kuantitatif"},
					{ID: "penalaran_verbal", Name: "Penalaran Verbal", Key: "penalaran_verbal"},
				},
			},
			"UTBK": {
				ID:   "utbk",
				Name: "UTBK",
				Subjects: []SubjectInfo{
					{ID: "literasi", Name: "Literasi", Key: "literasi"},
					{ID: "penalaran_matematika", Name: "Penalaran Matematika", Key: "penalaran_matematika"},
					{ID: "potensi_skolastik", Name: "Potensi Skolastik", Key: "potensi_skolastik"},
				},
			},
		},
	}
}

// GetExamTypeBySubject finds the exam type that contains the given subject
func GetExamTypeBySubject(subject string) (string, *ExamType, bool) {
	config, err := loadExamConfiguration()
	if err != nil {
		return "", nil, false
	}

	// First, check if the subject is actually an exam type key (direct match)
	if examType, exists := config.ExamTypes[strings.ToUpper(subject)]; exists {
		return strings.ToUpper(subject), &examType, true
	}

	// Then check all subjects in all exam types
	for examKey, examType := range config.ExamTypes {
		for _, subjectInfo := range examType.Subjects {
			// Case insensitive comparison
			if strings.EqualFold(subjectInfo.Name, subject) ||
				strings.EqualFold(subjectInfo.ID, subject) {
				return examKey, &examType, true
			}
		}
	}

	return "", nil, false
}

// GetExamTypeByExamID finds the exam type for a specific exam ID
func GetExamTypeByExamID(dbx *sqlx.DB, examID string) (string, error) {
	// First try to get it from available_exams
	query := `
		SELECT ae.category 
		FROM available_exams ae
		WHERE ae.id = $1
	`

	var category sql.NullString
	err := dbx.Get(&category, query, examID)

	// If we found a category and it's not null
	if err == nil && category.Valid {
		// Check if this category maps directly to a known exam type
		examType := strings.ToUpper(category.String)
		config, _ := loadExamConfiguration()
		if _, exists := config.ExamTypes[examType]; exists {
			return examType, nil
		}
	}

	// Fallback: Check exam_scores table to determine type from metadata
	metadataQuery := `
		SELECT 
			CASE
				WHEN es.metadata_scores->>'twk_score' IS NOT NULL OR 
					es.metadata_scores->>'tiu_score' IS NOT NULL OR 
					es.metadata_scores->>'tkp_score' IS NOT NULL 
				THEN 'CPNS'
				WHEN es.metadata_scores->>'kepribadian_score' IS NOT NULL OR 
					es.metadata_scores->>'pemecahan_masalah_score' IS NOT NULL 
				THEN 'LPDP'
				ELSE 'CPNS'
			END as exam_type
		FROM exam_scores es
		JOIN exam_sessions sess ON es.session_id = sess.session_id
		WHERE sess.exam_id = $1
		LIMIT 1
	`

	var examType string
	err = dbx.Get(&examType, metadataQuery, examID)
	if err != nil {
		if err == sql.ErrNoRows {
			// No matching record found, default to CPNS
			return "CPNS", nil
		}
		// For other errors, just log and return the default
		fmt.Printf("Error querying exam type: %v\n", err)
		return "CPNS", nil
	}

	return examType, nil
}

// RegisterExamConfigRoute adds a route to get the exam configuration
func RegisterExamConfigRoute(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	r.GET("/v0/exam-config", func(c *gin.Context) {
		config, err := loadExamConfiguration()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to load exam configuration",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Exam configuration retrieved successfully",
			"config":  config,
		})
	})

	// Add endpoint to get exam type by subject name
	r.GET("/v0/exam-config/subject-to-exam", func(c *gin.Context) {
		subject := c.Query("subject")
		if subject == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Subject parameter is required",
			})
			return
		}

		examKey, examType, found := GetExamTypeBySubject(subject)
		if !found {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "Subject not found in any exam configuration",
				"subject": subject,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"examType": examKey,
			"examInfo": examType,
			"subject":  subject,
		})
	})

	// Add endpoint to get exam configuration by exam type or subject
	r.GET("/v0/exam-config/:identifier", func(c *gin.Context) {
		identifier := c.Param("identifier")
		if identifier == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Identifier parameter is required",
			})
			return
		}

		examKey, examType, found := GetExamTypeBySubject(identifier)
		if !found {
			c.JSON(http.StatusNotFound, gin.H{
				"error":      "Exam type or subject not found",
				"identifier": identifier,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"examType": examKey,
			"config":   examType,
		})
	})

	// NEW ENDPOINT: Add endpoint to get exam configuration by exam ID
	r.GET("/v0/exam-config/exam/:examId", func(c *gin.Context) {
		examID := c.Param("examId")
		if examID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Exam ID parameter is required",
			})
			return
		}

		// Get the exam type for this exam ID
		examType, err := GetExamTypeByExamID(dbx, examID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to determine exam type: %v", err),
			})
			return
		}

		// Get the configuration for this exam type
		examKey, examTypeConfig, found := GetExamTypeBySubject(examType)
		if !found {
			c.JSON(http.StatusNotFound, gin.H{
				"error":    "Exam type configuration not found",
				"examId":   examID,
				"examType": examType,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"examId":   examID,
			"examType": examKey,
			"config":   examTypeConfig,
		})
	})
}
