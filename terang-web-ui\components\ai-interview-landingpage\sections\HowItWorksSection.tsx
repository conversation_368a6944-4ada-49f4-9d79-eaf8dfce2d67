// components/sections/HowItWorksSection.tsx
import React, { useState, useEffect } from 'react';
import { ArrowRight } from 'lucide-react';

const HowItWorksSection: React.FC = () => {
  const [isVisible, setIsVisible] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('[data-animate]');
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const steps = [
    { 
      step: "01", 
      title: "Daftar & Pilih", 
      desc: "Buat akun dan pilih jenis wawancara yang sesuai dengan tujuanmu",
      color: "from-blue-500 to-cyan-500",
    },
    { 
      step: "02", 
      title: "Simulasi AI", 
      desc: "Lakukan wawancara dengan AI interviewer yang realistis dan interaktif",
      color: "from-purple-500 to-pink-500",
    },
    { 
      step: "03", 
      title: "Analisis Hasil", 
      desc: "Dapatkan feedback detail dan skor performa yang komprehensif",
      color: "from-green-500 to-emerald-500",
    },
    { 
      step: "04", 
      title: "Improve", 
      desc: "Ikuti learning path yang dipersonalisasi untuk peningkatan optimal",
      color: "from-orange-500 to-red-500",
    }
  ];

  return (
    <section 
      id="how-it-works" 
      className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 relative"
      data-animate="true"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`text-center mb-20 ${isVisible['how-it-works'] ? 'animate-fade-in-up' : 'opacity-0'}`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full text-purple-800 text-sm font-medium mb-6">
            <img src="https://cdn.terang.ai/landingpage-assets/bitcoin-growth.svg" alt="Growth" className="w-4 h-4 mr-2" />
            Proses yang Mudah & Efektif
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Cara Kerja 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600"> Platform</span>
          </h2>
          <p className="text-xl text-gray-600">
            Empat langkah sederhana menuju kesuksesan wawancara impianmu
          </p>
        </div>

        <div className="grid md:grid-cols-4 gap-8">
          {steps.map((item, index) => (
            <div 
              key={index} 
              className="text-center group cursor-pointer relative"
              style={{ animationDelay: `${index * 300}ms` }}
            >
              <div className="relative mb-6">
                <div className={`w-20 h-20 bg-gradient-to-r ${item.color} text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all`}>
                  {item.step}
                </div>
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">
                {item.title}
              </h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                {item.desc}
              </p>
              
              {index < 3 && (
                <div className="hidden md:block absolute top-10 right-0 transform translate-x-1/2">
                  <ArrowRight className="w-6 h-6 text-gray-300 group-hover:text-purple-400 transition-colors" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;