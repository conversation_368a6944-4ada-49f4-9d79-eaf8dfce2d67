-- Remove the normalized tables and return to using only JSONB

-- Drop the trigger
DROP TRIGGER IF EXISTS exam_question_data_sync ON exam_question_answer_hints;
DROP FUNCTION IF EXISTS sync_question_data_trigger();

-- Drop the batch processing function
DROP FUNCTION IF EXISTS process_exam_data_in_batches();

-- Drop the stored procedure
DROP PROCEDURE IF EXISTS process_question_data(VARCHAR(26), JSONB);

-- Drop the tables in the correct order to respect foreign key constraints
DROP TABLE IF EXISTS question_explanations;
DROP TABLE IF EXISTS question_hints;
DROP TABLE IF EXISTS question_instructions;  -- Added this line
DROP TABLE IF EXISTS question_options;
DROP TABLE IF EXISTS question_metadata;
DROP TABLE IF EXISTS exam_questions;

-- Note: The original exam_question_answer_hints table with JSONB data is preserved