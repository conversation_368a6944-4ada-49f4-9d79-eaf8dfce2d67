CREATE TABLE interview_grading (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    overall_score DECIMAL(5,2),
    category_scores JSONB,
    communication_metrics JSONB,
    highlights JSONB,
    improvement_areas JSONB,
    question_stats JSO<PERSON><PERSON>,
    interviewer_assessment JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_interview_grading_session_id ON interview_grading(session_id);
