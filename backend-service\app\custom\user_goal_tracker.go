package custom

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"
)

// UserGoalTrackerInput represents the input data for the user goal tracker form
type UserGoalTrackerInput struct {
	Name                   string `json:"name" binding:"required"`
	Email                  string `json:"email" binding:"required,email"`
	VerbalReasoning        int    `json:"verbal_reasoning" binding:"required,min=0"`
	QuantitativeReasoning  int    `json:"quantitative_reasoning" binding:"required,min=0"`
	ProblemSolving         int    `json:"problem_solving" binding:"required,min=0"`
	PassedLpdpTbs          bool   `json:"passed_lpdp_tbs"`
	FeltHelped             bool   `json:"felt_helped"`
	HelpfulnessRating      int    `json:"helpfulness_rating" binding:"required,min=1,max=10"`
	MostHelpfulAspect      string `json:"most_helpful_aspect"`
	ImprovementSuggestions string `json:"improvement_suggestions"`
	ContactConsent         bool   `json:"contact_consent"`
	PhoneNumber            string `json:"phone_number"`
}

// UserGoalTrackerResult represents the result of a user goal tracker submission
type UserGoalTrackerResult struct {
	ID        int       `json:"id" db:"id"`
	Email     string    `json:"email" db:"email"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// RegisterUserGoalTrackerRoutes registers the routes for the user goal tracker
func RegisterUserGoalTrackerRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.POST("/user-goal-tracker", submitUserGoalTracker(dbx))
		v0.GET("/user-goal-tracker/check-submission", checkUserGoalTrackerSubmission(dbx))
	}
}

// submitUserGoalTracker handles the submission of the user goal tracker form
func submitUserGoalTracker(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a context with timeout for all database operations
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		var input UserGoalTrackerInput

		if err := c.ShouldBindJSON(&input); err != nil {
			log.Printf("[LPDP Goal Tracker] Invalid form data: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Invalid form data: " + err.Error(),
			})
			return
		}

		log.Printf("[LPDP Goal Tracker] Processing form submission for user: %s (%s)", input.Name, input.Email)

		// Start transaction
		tx, err := dbx.BeginTxx(ctx, nil)
		if err != nil {
			log.Printf("[LPDP Goal Tracker] Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to start database transaction",
			})
			return
		}

		// Ensure transaction is rolled back if not committed
		defer func() {
			if tx != nil {
				tx.Rollback()
			}
		}()

		// Check if user has already submitted the form using the optimized query
		var submissionResult struct {
			HasSubmitted    bool `db:"has_submitted"`
			SubmissionCount int  `db:"submission_count"`
		}

		err = tx.GetContext(ctx, &submissionResult, `
			SELECT
				EXISTS(SELECT 1 FROM user_goal_tracker WHERE email = $1 LIMIT 1) as has_submitted,
				(SELECT COUNT(*) FROM user_goal_tracker WHERE email = $1) as submission_count
		`, input.Email)

		if err != nil {
			log.Printf("[LPDP Goal Tracker] Error checking existing submission for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to check existing submission",
			})
			return
		}

		if submissionResult.HasSubmitted {
			log.Printf("[LPDP Goal Tracker] User %s has already submitted the form (%d submissions)",
				input.Email, submissionResult.SubmissionCount)
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": "You have already submitted this form",
				"count":   submissionResult.SubmissionCount,
			})
			return
		}

		log.Printf("[LPDP Goal Tracker] Inserting form data for user %s", input.Email)

		// Log form data summary
		log.Printf("[LPDP Goal Tracker] Form data summary - Verbal: %d, Quantitative: %d, Problem Solving: %d, Passed: %v, Felt Helped: %v, Rating: %d, Contact Consent: %v",
			input.VerbalReasoning,
			input.QuantitativeReasoning,
			input.ProblemSolving,
			input.PassedLpdpTbs,
			input.FeltHelped,
			input.HelpfulnessRating,
			input.ContactConsent)

		// Sanitize string inputs using bluemonday to prevent XSS
		p := bluemonday.UGCPolicy()
		sanitizedName := p.Sanitize(input.Name)
		sanitizedEmail := p.Sanitize(input.Email)
		sanitizedMostHelpfulAspect := p.Sanitize(input.MostHelpfulAspect)
		sanitizedImprovementSuggestions := p.Sanitize(input.ImprovementSuggestions)
		sanitizedPhoneNumber := p.Sanitize(input.PhoneNumber)

		log.Printf("[LPDP Goal Tracker] Sanitized input data for XSS prevention")

		// Prepare the insert query
		insertQuery := `
			INSERT INTO user_goal_tracker (
				name, email, verbal_reasoning, quantitative_reasoning, problem_solving,
				passed_lpdp_tbs, felt_helped, helpfulness_rating, most_helpful_aspect,
				improvement_suggestions, contact_consent, phone_number
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
			) RETURNING id, email, created_at`

		// Prepare the arguments with sanitized values
		args := []interface{}{
			sanitizedName,
			sanitizedEmail,
			input.VerbalReasoning,
			input.QuantitativeReasoning,
			input.ProblemSolving,
			input.PassedLpdpTbs,
			input.FeltHelped,
			input.HelpfulnessRating,
			sanitizedMostHelpfulAspect,
			sanitizedImprovementSuggestions,
			input.ContactConsent,
			sanitizedPhoneNumber,
		}

		// Insert the form data
		var result UserGoalTrackerResult
		err = tx.QueryRowxContext(ctx, insertQuery, args...).StructScan(&result)

		if err != nil {
			log.Printf("[LPDP Goal Tracker] Error inserting user goal tracker data for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to save form data",
			})
			return
		}

		// Commit the transaction
		if err := tx.Commit(); err != nil {
			log.Printf("[LPDP Goal Tracker] Error committing transaction for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to commit transaction",
			})
			return
		}

		// Set tx to nil to prevent rollback in defer function
		tx = nil

		log.Printf("[LPDP Goal Tracker] Form submitted successfully for user %s (ID: %d)", input.Email, result.ID)

		c.JSON(http.StatusCreated, gin.H{
			"success": true,
			"message": "Form submitted successfully",
			"data":    result,
		})
	}
}

// checkUserGoalTrackerSubmission checks if a user has already submitted the form
func checkUserGoalTrackerSubmission(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			log.Printf("[LPDP Goal Tracker] Submission check failed: Email is required")
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Email is required",
			})
			return
		}

		// Sanitize email input using bluemonday to prevent XSS
		p := bluemonday.UGCPolicy()
		sanitizedEmail := p.Sanitize(email)

		log.Printf("[LPDP Goal Tracker] Checking if user %s has already submitted the form", sanitizedEmail)

		// Create a context with timeout for database operation
		ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
		defer cancel()

		// Optimized query to get both submission status and count in one query
		query := `
			SELECT
				EXISTS(SELECT 1 FROM user_goal_tracker WHERE email = $1 LIMIT 1) as has_submitted,
				(SELECT COUNT(*) FROM user_goal_tracker WHERE email = $1) as submission_count
		`

		var result struct {
			HasSubmitted    bool `db:"has_submitted"`
			SubmissionCount int  `db:"submission_count"`
		}

		err := dbx.GetContext(ctx, &result, query, sanitizedEmail)
		if err != nil {
			log.Printf("[LPDP Goal Tracker] Error checking user goal tracker submission for %s: %v", sanitizedEmail, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to check submission status",
			})
			return
		}

		if result.HasSubmitted {
			log.Printf("[LPDP Goal Tracker] User %s has already submitted the form (%d submissions)", sanitizedEmail, result.SubmissionCount)
		} else {
			log.Printf("[LPDP Goal Tracker] User %s has not submitted the form yet", sanitizedEmail)
		}

		c.JSON(http.StatusOK, gin.H{
			"success":          true,
			"submitted":        result.HasSubmitted,
			"count":            result.SubmissionCount,
			"submission_count": result.SubmissionCount, // Added for consistency with eligibility endpoint
		})
	}
}
