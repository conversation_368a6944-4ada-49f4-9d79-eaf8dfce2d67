import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const { tingkat } = req.query;

  if (!tingkat) {
    return res
      .status(400)
      .json({ error: "Education level (tingkat) is required" });
  }

  try {
    const response = await fetch(
      `https://api-sscasn.bkn.go.id/2024/referensi/pendidikan?tingkat=${tingkat}&nama=null&limit=2500`,
      {
        headers: {
          Host: "api-sscasn.bkn.go.id",
          Referer: "https://sscasn.bkn.go.id",
          Origin: "https://sscasn.bkn.go.id",
        },
      },
    );

    if (!response.ok) {
      throw new Error(`API responded with status ${response.status}`);
    }

    const data = await response.json();

    res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching program studies:", error);
    res.status(500).json({ error: "Failed to fetch program studies" });
  }
}
