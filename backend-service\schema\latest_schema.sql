-- Consolidated Schema from Database Metadata
-- Generated on 2025-05-25 16:27:31
-- This file represents the complete database schema

-- Custom Types (ENUMs)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_game_status') THEN
        CREATE TYPE exam_game_status AS ENUM (
    'RUNNING',
    'PAUSED',
    'COMPLETED',
    'GAME_OVER'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_session_status') THEN
        CREATE TYPE exam_session_status AS ENUM (
    'ACTIVE',
    'COMPLETED',
    'ABANDONED'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_session_type') THEN
        CREATE TYPE exam_session_type AS ENUM (
    'PRACTICE',
    'EXAM'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'exam_type') THEN
        CREATE TYPE exam_type AS ENUM (
    'PRACTICE',
    'EXAM'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
        CREATE TYPE invoice_status AS ENUM (
    'PENDING',
    'PAID',
    'PARTIALLY_PAID',
    'CANCELLED',
    'OVERDUE'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'order_status') THEN
        CREATE TYPE order_status AS ENUM (
    'PENDING',
    'PAYMENT_PROCESSING',
    'PAYMENT_RECEIVED',
    'IN_PROGRESS',
    'REVISION_REQUESTED',
    'REVISIONS_COMPLETED',
    'COMPLETED',
    'CANCELLED'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_status') THEN
        CREATE TYPE payment_status AS ENUM (
    'PENDING',
    'PROCESSING',
    'PAID',
    'REFUNDED',
    'WITHDRAWN',
    'FAILED',
    'EXPIRED'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'referral_status') THEN
        CREATE TYPE referral_status AS ENUM (
    'ACTIVE',
    'EXPIRED',
    'USED'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'referral_type') THEN
        CREATE TYPE referral_type AS ENUM (
    'REGISTRATION',
    'PURCHASE',
    'SUBSCRIPTION'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_type') THEN
        CREATE TYPE subscription_type AS ENUM (
    'FREE',
    'LITE',
    'PLUS',
    'PREMIUM'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'timezone_offset') THEN
        CREATE TYPE timezone_offset AS ENUM (
    '+00:00',
    '+01:00',
    '+02:00',
    '+03:00',
    '+04:00',
    '+05:00',
    '+06:00',
    '+07:00',
    '+08:00',
    '+09:00',
    '+10:00',
    '+11:00',
    '+12:00',
    '-01:00',
    '-02:00',
    '-03:00',
    '-04:00',
    '-05:00',
    '-06:00',
    '-07:00',
    '-08:00',
    '-09:00',
    '-10:00',
    '-11:00',
    '-12:00'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM (
    'user',
    'admin',
    'auditor',
    'reader',
    'moderator',
    'superadmin',
    'contributor',
    'editor',
    'guest',
    'developer',
    'support'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_tier') THEN
        CREATE TYPE user_tier AS ENUM (
    'REGULAR',
    'VERIFIED',
    'ADVANCED',
    'PRO',
    'EXPERT',
    'ELITE',
    'PREMIUM',
    'MASTER',
    'VIP',
    'GOLD',
    'PLATINUM',
    'DIAMOND',
    'ULTIMATE',
    'LEGEND'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_token_status') THEN
        CREATE TYPE user_token_status AS ENUM (
    'AVAILABLE',
    'EXPIRED'
        );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visibility_status') THEN
        CREATE TYPE visibility_status AS ENUM (
    'PUBLIC',
    'DRAFT'
        );
    END IF;
END $$;

-- Tables
CREATE TABLE IF NOT EXISTS available_exams (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    subname character varying(255) NOT NULL,
    description bytea NOT NULL,
    baseline_price numeric(18,2),
    visibility visibility_status DEFAULT 'DRAFT'::visibility_status,
    duration interval,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_id character varying(26) NOT NULL,
    type exam_type NOT NULL DEFAULT 'EXAM'::exam_type,
    subject character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT fk_user_available_exams FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS categories (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    image_url character varying(1000),
    size_in_bytes integer,
    mime_type character varying(100),
    width integer,
    height integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS chat_ai_history (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    chat_id character varying(26) NOT NULL,
    messages jsonb NOT NULL DEFAULT '[]'::jsonb,
    from_page character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT chat_ai_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS exam_bundle_items (id character varying(26) NOT NULL,
    bundle_id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_bundle FOREIGN KEY (bundle_id) REFERENCES exam_bundles(id),
    CONSTRAINT fk_exam FOREIGN KEY (exam_id) REFERENCES available_exams(id));

CREATE TABLE IF NOT EXISTS exam_bundles (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    description bytea NOT NULL,
    price numeric(18,2) NOT NULL,
    discount_percentage integer,
    thumbnail_url character varying(1000),
    banner_url character varying(1000),
    visibility visibility_status DEFAULT 'DRAFT'::visibility_status,
    valid_from timestamp with time zone,
    valid_until timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_exam_bundles FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS exam_categories (id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    category_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT exam_categories_exam_id_fkey FOREIGN KEY (exam_id) REFERENCES available_exams(id),
    CONSTRAINT exam_categories_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id));

CREATE TABLE IF NOT EXISTS exam_gamification (
    id character varying(26) NOT NULL,
    exam_session_id character varying(26) NOT NULL,
    current_question_id character varying(26),
    current_lives integer NOT NULL DEFAULT 3,
    streak_count integer NOT NULL DEFAULT 0,
    highest_streak integer NOT NULL DEFAULT 0,
    hints_remaining jsonb DEFAULT '{"count": 3}'::jsonb,
    start_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    end_time timestamp with time zone,
    elapsed_time interval DEFAULT '00:00:00'::interval,
    pause_start_time timestamp with time zone,
    total_pause_time interval DEFAULT '00:00:00'::interval,
    question_times jsonb DEFAULT '{}'::jsonb,
    status exam_game_status DEFAULT 'RUNNING'::exam_game_status,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_exam_session_game FOREIGN KEY (exam_session_id) REFERENCES exam_sessions(session_id)
);

CREATE TABLE IF NOT EXISTS exam_question_answer_hints (
    id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    data jsonb,
    passing_grade jsonb,
    PRIMARY KEY (id),
    CONSTRAINT fk_exam_available_exams FOREIGN KEY (exam_id) REFERENCES available_exams(id)
);

CREATE TABLE IF NOT EXISTS exam_questions (
    id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    title text NOT NULL,
    PRIMARY KEY (id, exam_id),
    CONSTRAINT fk_questions_exam FOREIGN KEY (exam_id) REFERENCES available_exams(id)
);

CREATE TABLE IF NOT EXISTS exam_scores (
    id character varying(26) NOT NULL,
    session_id character varying(26) NOT NULL,
    total_questions integer NOT NULL,
    correct_answers integer NOT NULL,
    score numeric(5,2) NOT NULL,
    accuracy numeric(5,2),
    metadata_scores jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_session_exam_scores FOREIGN KEY (session_id) REFERENCES exam_sessions(session_id)
);

CREATE TABLE IF NOT EXISTS exam_sessions (id character varying(26) NOT NULL,
    session_id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    type exam_session_type NOT NULL DEFAULT 'EXAM'::exam_session_type,
    status exam_session_status DEFAULT 'ACTIVE'::exam_session_status,
    start_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    end_time timestamp with time zone,
    answers jsonb,
    flagged_questions jsonb,
    subject character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_exam_sessions FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_exam_exam_sessions FOREIGN KEY (exam_id) REFERENCES available_exams(id));

CREATE TABLE IF NOT EXISTS exam_tags (exam_id character varying(26) NOT NULL,
    tag_id character varying(26) NOT NULL,
    PRIMARY KEY (exam_id, tag_id),
    CONSTRAINT exam_tags_exam_id_fkey FOREIGN KEY (exam_id) REFERENCES available_exams(id),
    CONSTRAINT exam_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES tags(id));

CREATE TABLE IF NOT EXISTS exam_terms_of_services (id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    terms_of_service_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT exam_terms_of_services_exam_id_fkey FOREIGN KEY (exam_id) REFERENCES available_exams(id),
    CONSTRAINT exam_terms_of_services_terms_of_service_id_fkey FOREIGN KEY (terms_of_service_id) REFERENCES terms_of_services(id));

CREATE TABLE IF NOT EXISTS feedback_surveys (
    id integer NOT NULL DEFAULT nextval('feedback_surveys_id_seq'::regclass),
    email character varying(255) NOT NULL,
    nps character varying(2) NOT NULL,
    csat character varying(1) NOT NULL,
    ces character varying(1) NOT NULL,
    fitur_favorit jsonb NOT NULL,
    perbaikan character varying(20) NOT NULL,
    perbaikan_lainnya text,
    frekuensi_penggunaan character varying(20) NOT NULL,
    sumber_informasi character varying(20) NOT NULL,
    sumber_informasi_lainnya text,
    umpan_balik text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS free_daerah_3t (
    id character varying(26) NOT NULL,
    user_email character varying(255) NOT NULL,
    category_id character varying(26) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS interview_grading (
    id integer NOT NULL DEFAULT nextval('interview_grading_id_seq'::regclass),
    session_id character varying(255) NOT NULL,
    overall_score numeric(5,2),
    category_scores jsonb,
    communication_metrics jsonb,
    highlights jsonb,
    improvement_areas jsonb,
    question_stats jsonb,
    interviewer_assessment jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    session_id_fk integer,
    PRIMARY KEY (id),
    CONSTRAINT interview_grading_session_id_fk_fkey FOREIGN KEY (session_id_fk) REFERENCES interview_sessions(id)
);

CREATE TABLE IF NOT EXISTS interview_sessions (
    id integer NOT NULL DEFAULT nextval('interview_sessions_id_seq'::regclass),
    session_id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    user_email character varying(255) NOT NULL,
    user_name character varying(255) NOT NULL,
    interview_id character varying(255) NOT NULL,
    category character varying(255) NOT NULL,
    type character varying(50) NOT NULL,
    status character varying(50) NOT NULL DEFAULT 'in_progress'::character varying,
    recording_url character varying(255),
    transcript_url character varying(255),
    start_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    end_time timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    duration character varying(50) NOT NULL DEFAULT '01:00:00'::character varying,
    duration_seconds integer NOT NULL DEFAULT 3600,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS invoices (
    id character varying(26) NOT NULL,
    invoice_number character varying(50) NOT NULL,
    order_id character varying(26) NOT NULL,
    status invoice_status DEFAULT 'PENDING'::invoice_status,
    invoice_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    due_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    client_email character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT invoices_order_id_fkey FOREIGN KEY (order_id) REFERENCES orders(id)
);

CREATE TABLE IF NOT EXISTS kab_kotas (
    id smallint NOT NULL,
    nama_kab_kota character varying(32) DEFAULT NULL::character varying,
    provinsi_id smallint NOT NULL,
    latitude character varying(1) DEFAULT NULL::character varying,
    longitude character varying(1) DEFAULT NULL::character varying,
    created_at character varying(1) DEFAULT NULL::character varying,
    updated_at character varying(1) DEFAULT NULL::character varying,
    PRIMARY KEY (id),
    CONSTRAINT fk_kab_kota_provinsi FOREIGN KEY (provinsi_id) REFERENCES provinsis(id)
);

CREATE TABLE IF NOT EXISTS kecamatans (
    id integer NOT NULL,
    nama_kecamatan character varying(31) DEFAULT NULL::character varying,
    kab_kota_id smallint NOT NULL,
    latitude character varying(1) DEFAULT NULL::character varying,
    longitude character varying(1) DEFAULT NULL::character varying,
    created_at character varying(1) DEFAULT NULL::character varying,
    updated_at character varying(1) DEFAULT NULL::character varying,
    PRIMARY KEY (id),
    CONSTRAINT fk_kecamatan_kab_kota FOREIGN KEY (kab_kota_id) REFERENCES kab_kotas(id)
);

CREATE TABLE IF NOT EXISTS kelurahan_desas (
    id bigint NOT NULL,
    kecamatan_id integer NOT NULL,
    nama_kelurahan_desa character varying(37) DEFAULT NULL::character varying,
    latitude character varying(1) DEFAULT NULL::character varying,
    longitude character varying(1) DEFAULT NULL::character varying,
    created_at character varying(1) DEFAULT NULL::character varying,
    updated_at character varying(1) DEFAULT NULL::character varying,
    PRIMARY KEY (id),
    CONSTRAINT fk_kelurahan_desa_kecamatan FOREIGN KEY (kecamatan_id) REFERENCES kecamatans(id)
);

CREATE TABLE IF NOT EXISTS media (
    id character varying(26) NOT NULL,
    url character varying(1000) NOT NULL,
    available_exam_id character varying(26) NOT NULL,
    size_in_bytes integer,
    mime_type character varying(100),
    width integer,
    height integer,
    uploaded_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_available_exam_media FOREIGN KEY (available_exam_id) REFERENCES available_exams(id)
);

CREATE TABLE IF NOT EXISTS orders (id character varying(26) NOT NULL,
    client_id character varying(26),
    exam_id character varying(26) NOT NULL,
    quantity integer NOT NULL,
    status order_status NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    bundle_id character varying(26),
    client_email character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT orders_exam_id_fkey FOREIGN KEY (exam_id) REFERENCES available_exams(id),
    CONSTRAINT orders_bundle_id_fkey FOREIGN KEY (bundle_id) REFERENCES exam_bundles(id),
    CONSTRAINT orders_client_id_fkey FOREIGN KEY (client_id) REFERENCES users(id));

CREATE TABLE IF NOT EXISTS payments (
    id character varying(26) NOT NULL,
    invoice_id character varying(26),
    payment_method character varying(255) NOT NULL,
    amount numeric(18,2),
    status payment_status DEFAULT 'PENDING'::payment_status,
    payment_start_date timestamp with time zone,
    payment_end_date timestamp with time zone,
    transaction_id character varying(255),
    payment_link character varying(500),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    client_email character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT payments_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

CREATE TABLE IF NOT EXISTS promo_email_subscribers (
    id character varying(26) NOT NULL,
    email character varying(255) NOT NULL,
    partner_id character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS provinsis (
    id smallint NOT NULL,
    nama_provinsi character varying(26) DEFAULT NULL::character varying,
    latitude character varying(1) DEFAULT NULL::character varying,
    longitude character varying(1) DEFAULT NULL::character varying,
    created_at character varying(1) DEFAULT NULL::character varying,
    updated_at character varying(1) DEFAULT NULL::character varying,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS question_explanations (id integer NOT NULL DEFAULT nextval('question_explanations_id_seq'::regclass),
    question_id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    content text NOT NULL,
    display_order integer NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_explanations_question FOREIGN KEY (question_id) REFERENCES exam_questions(id));

CREATE TABLE IF NOT EXISTS question_hints (id integer NOT NULL DEFAULT nextval('question_hints_id_seq'::regclass),
    question_id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    content text NOT NULL,
    display_order integer NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_hints_question FOREIGN KEY (question_id) REFERENCES exam_questions(id));

CREATE TABLE IF NOT EXISTS question_instructions (id integer NOT NULL DEFAULT nextval('question_instructions_id_seq'::regclass),
    question_id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    content text NOT NULL,
    display_order integer NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_instructions_question FOREIGN KEY (question_id) REFERENCES exam_questions(id));

CREATE TABLE IF NOT EXISTS question_metadata (id integer NOT NULL DEFAULT nextval('question_metadata_id_seq'::regclass),
    question_id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    name character varying(100) NOT NULL,
    value text NOT NULL,
    level integer NOT NULL DEFAULT 0,
    PRIMARY KEY (id),
    CONSTRAINT fk_question_metadata_question FOREIGN KEY (question_id) REFERENCES exam_questions(id));

CREATE TABLE IF NOT EXISTS question_options (id character varying(1024) NOT NULL,
    question_id character varying(1024) NOT NULL,
    exam_id character varying(1024) NOT NULL,
    content text NOT NULL,
    is_correct boolean NOT NULL DEFAULT false,
    PRIMARY KEY (id, question_id, exam_id),
    CONSTRAINT fk_options_question FOREIGN KEY (question_id) REFERENCES exam_questions(id));

CREATE TABLE IF NOT EXISTS referral_analytics (
    id character varying(26) NOT NULL,
    referral_code_id character varying(26) NOT NULL,
    type character varying(20) NOT NULL,
    ip_address character varying(45),
    user_agent text,
    referer_url text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_referral_code_analytics FOREIGN KEY (referral_code_id) REFERENCES referral_codes(id)
);

CREATE TABLE IF NOT EXISTS referral_balances (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    available_balance numeric(10,2) NOT NULL DEFAULT 0.00,
    pending_balance numeric(10,2) NOT NULL DEFAULT 0.00,
    lifetime_earned numeric(10,2) NOT NULL DEFAULT 0.00,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_referral_balance FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS referral_codes (
    id character varying(26) NOT NULL,
    referrer_id character varying(26) NOT NULL,
    code character varying(50) NOT NULL,
    status referral_status DEFAULT 'ACTIVE'::referral_status,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    expires_at timestamp with time zone NOT NULL DEFAULT (CURRENT_TIMESTAMP + '365 days'::interval),
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_referrer FOREIGN KEY (referrer_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS referral_configurations (id character varying(26) NOT NULL,
    user_id character varying(26),
    type referral_type NOT NULL,
    config jsonb NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id));

CREATE TABLE IF NOT EXISTS referral_rewards (id character varying(26) NOT NULL,
    referral_use_id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    reward_type character varying(50) NOT NULL,
    amount numeric(10,2) NOT NULL,
    status payment_status DEFAULT 'PENDING'::payment_status,
    withdrawal_id character varying(26),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_referral_use FOREIGN KEY (referral_use_id) REFERENCES referral_uses(id),
    CONSTRAINT fk_withdrawal FOREIGN KEY (withdrawal_id) REFERENCES withdrawal_requests(id));

CREATE TABLE IF NOT EXISTS referral_uses (id character varying(26) NOT NULL,
    referral_code_id character varying(26) NOT NULL,
    referee_id character varying(26) NOT NULL,
    type referral_type NOT NULL,
    payment_id character varying(26),
    subscription_id character varying(26),
    referrer_reward_amount numeric(10,2) NOT NULL,
    referee_reward_amount numeric(10,2) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    is_redeemable boolean NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_referral_code FOREIGN KEY (referral_code_id) REFERENCES referral_codes(id),
    CONSTRAINT fk_referee FOREIGN KEY (referee_id) REFERENCES users(id),
    CONSTRAINT fk_payment FOREIGN KEY (payment_id) REFERENCES payments(id),
    CONSTRAINT fk_subscription FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id));

CREATE TABLE IF NOT EXISTS subscription_tiers (
    id character varying(26) NOT NULL,
    name subscription_type NOT NULL,
    price_monthly numeric(10,2) NOT NULL,
    original_price numeric(10,2),
    discount_percentage integer,
    billing_interval character varying(20) DEFAULT 'monthly'::character varying,
    features jsonb NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS tags (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS terms_of_services (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    content bytea NOT NULL,
    user_id character varying(26) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_tos FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_ai_token_usage_history (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    amount bigint NOT NULL,
    last_balance bigint NOT NULL,
    after_balance bigint NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_ai_token_usage_history_user FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_ai_tokens (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    token_balance bigint NOT NULL DEFAULT 0,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_ai_tokens_user FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_analytics (id character varying(26) NOT NULL,
    user_id character varying(26),
    event_type character varying(255) NOT NULL,
    event_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    session_id character varying(255),
    duration_seconds integer,
    path character varying(255),
    device_info jsonb DEFAULT '{}'::jsonb,
    location_info jsonb DEFAULT '{}'::jsonb,
    event_metadata jsonb DEFAULT '{}'::jsonb,
    status character varying(255),
    update_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_email character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id));

CREATE TABLE IF NOT EXISTS user_demographics (
    id integer NOT NULL DEFAULT nextval('user_demographics_id_seq'::regclass),
    birth_date date,
    last_occupation character varying(100),
    interests jsonb,
    main_purpose character varying(100),
    gender character varying(20),
    phone_number character varying(20),
    preferred_study_methods jsonb,
    weekly_study_time character varying(50),
    primary_devices jsonb,
    learning_style character varying(50),
    study_budget integer,
    email character varying(255) NOT NULL,
    target_jabatan character varying(255),
    target_institution character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    target_score integer,
    learning_challenges jsonb,
    province_name character varying(100),
    city_name character varying(100),
    district_name character varying(100),
    village_name character varying(100),
    education_level_name character varying(100),
    program_study_name character varying(150),
    target_university character varying(255),
    target_major character varying(255),
    province_id bigint,
    city_id bigint,
    district_id bigint,
    village_id bigint,
    education_level_id bigint,
    program_study_id bigint,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS user_demographics_backup (
    id integer,
    province_id character varying(50),
    city_id character varying(50),
    district_id character varying(50),
    village_id character varying(50),
    birth_date date,
    education_level_id character varying(50),
    program_study_id character varying(50),
    last_occupation character varying(100),
    interests jsonb,
    main_purpose character varying(100),
    gender character varying(20),
    phone_number character varying(20),
    preferred_study_methods jsonb,
    weekly_study_time character varying(50),
    primary_devices jsonb,
    learning_style character varying(50),
    study_budget integer,
    email character varying(255),
    target_jabatan character varying(255),
    target_institution character varying(255),
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    target_score integer,
    learning_challenges jsonb,
    province_name character varying(100),
    city_name character varying(100),
    district_name character varying(100),
    village_name character varying(100),
    education_level_name character varying(100),
    program_study_name character varying(150),
    target_university character varying(255),
    target_major character varying(255)
);

CREATE TABLE IF NOT EXISTS user_goal_tracker (
    id integer NOT NULL DEFAULT nextval('user_goal_tracker_id_seq'::regclass),
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    verbal_reasoning integer NOT NULL,
    quantitative_reasoning integer NOT NULL,
    problem_solving integer NOT NULL,
    passed_lpdp_tbs boolean NOT NULL,
    felt_helped boolean NOT NULL,
    helpfulness_rating integer NOT NULL,
    most_helpful_aspect text,
    improvement_suggestions text,
    contact_consent boolean NOT NULL DEFAULT false,
    phone_number character varying(20),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS user_logins (id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    login_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    ip_address character varying(45),
    user_agent text,
    location character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id));

CREATE TABLE IF NOT EXISTS user_profile_analytics (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    views integer DEFAULT 0,
    last_viewed timestamp with time zone,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_analytics FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS user_relationships (id character varying(26) NOT NULL,
    follower_id character varying(26) NOT NULL,
    followee_id character varying(26) NOT NULL,
    followed_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_follower FOREIGN KEY (follower_id) REFERENCES users(id),
    CONSTRAINT fk_followee FOREIGN KEY (followee_id) REFERENCES users(id));

CREATE TABLE IF NOT EXISTS user_subscriptions (id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    tier_id character varying(26) NOT NULL,
    start_date timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date timestamp with time zone,
    is_active boolean DEFAULT true,
    auto_renewal boolean DEFAULT false,
    payment_status payment_status DEFAULT 'PENDING'::payment_status,
    last_payment_date timestamp with time zone,
    next_payment_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    transaction_id character varying(255),
    PRIMARY KEY (id),
    CONSTRAINT fk_user_subscriptions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_user_subscriptions_tier FOREIGN KEY (tier_id) REFERENCES subscription_tiers(id));

CREATE TABLE IF NOT EXISTS user_verification_requests (
    id bigint NOT NULL DEFAULT nextval('user_verification_requests_id_seq'::regclass),
    email character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    status user_token_status DEFAULT 'AVAILABLE'::user_token_status,
    expires timestamp with time zone NOT NULL DEFAULT (CURRENT_TIMESTAMP + '00:10:00'::interval),
    created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_verification_requests FOREIGN KEY (email) REFERENCES users(email)
);

CREATE TABLE IF NOT EXISTS users (
    id character varying(26) NOT NULL,
    username character varying(255),
    email character varying(255) NOT NULL,
    password character varying(255),
    first_name character varying(255),
    last_name character varying(255),
    picture character varying(1000),
    banner character varying(1000),
    last_login timestamp with time zone,
    role user_role DEFAULT 'user'::user_role,
    tier user_tier DEFAULT 'REGULAR'::user_tier,
    personal_website character varying(255),
    phone_number character varying(20),
    address character varying(500),
    date_of_birth date,
    country character varying(255),
    bio text,
    preferences jsonb,
    social_links jsonb,
    is_verified boolean DEFAULT false,
    is_banned boolean DEFAULT false,
    timezone timezone_offset DEFAULT '+00:00'::timezone_offset,
    consent_marketing boolean DEFAULT true,
    two_factor_enabled boolean DEFAULT false,
    two_factor_secret character varying(255),
    last_modified_by character varying(26),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id character varying(26) NOT NULL,
    user_id character varying(26) NOT NULL,
    amount numeric(10,2) NOT NULL,
    status character varying(20) NOT NULL DEFAULT 'PENDING'::character varying,
    payment_method character varying(50) NOT NULL,
    payment_details jsonb NOT NULL,
    processed_at timestamp with time zone,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_withdrawal FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Indices
CREATE UNIQUE INDEX categories_name_key ON public.categories USING btree (name);
CREATE UNIQUE INDEX chat_ai_history_user_id_chat_id_key ON public.chat_ai_history USING btree (user_id, chat_id);
CREATE UNIQUE INDEX exam_gamification_exam_session_id_key ON public.exam_gamification USING btree (exam_session_id);
CREATE UNIQUE INDEX exam_sessions_session_id_key ON public.exam_sessions USING btree (session_id);
CREATE INDEX idx_ai_token_usage_history_user_id ON public.user_ai_token_usage_history USING btree (user_id);
CREATE INDEX idx_available_exams_baseline_price_name ON public.available_exams USING btree (baseline_price, name, created_at);
CREATE INDEX idx_available_exams_user_id ON public.available_exams USING btree (user_id);
CREATE INDEX idx_categories_id ON public.categories USING btree (id);
CREATE INDEX idx_chat_ai_history_user_chat ON public.chat_ai_history USING btree (user_id, chat_id);
CREATE INDEX idx_email ON public.users USING btree (email);
CREATE INDEX idx_exam_bundle_items_bundle_id ON public.exam_bundle_items USING btree (bundle_id);
CREATE INDEX idx_exam_bundle_items_exam_id ON public.exam_bundle_items USING btree (exam_id);
CREATE INDEX idx_exam_bundles_metadata ON public.exam_bundles USING gin (metadata);
CREATE INDEX idx_exam_bundles_price ON public.exam_bundles USING btree (price);
CREATE INDEX idx_exam_bundles_user_id ON public.exam_bundles USING btree (user_id);
CREATE INDEX idx_exam_bundles_valid_dates ON public.exam_bundles USING btree (valid_from, valid_until);
CREATE INDEX idx_exam_bundles_visibility ON public.exam_bundles USING btree (visibility);
CREATE INDEX idx_exam_categories_category_id ON public.exam_categories USING btree (category_id);
CREATE INDEX idx_exam_categories_exam_id ON public.exam_categories USING btree (exam_id);
CREATE INDEX idx_exam_gamification_question_times_gin ON public.exam_gamification USING gin (question_times);
CREATE INDEX idx_exam_gamification_session_id ON public.exam_gamification USING btree (exam_session_id);
CREATE INDEX idx_exam_qah_exam_id ON public.exam_question_answer_hints USING btree (exam_id);
CREATE INDEX idx_exam_question_answer_hints_exam_id ON public.exam_question_answer_hints USING btree (exam_id);
CREATE INDEX idx_exam_questions_exam_id ON public.exam_questions USING btree (exam_id);
CREATE INDEX idx_exam_scores_session_id ON public.exam_scores USING btree (session_id);
CREATE INDEX idx_exam_sessions_answers ON public.exam_sessions USING gin (answers);
CREATE INDEX idx_exam_sessions_answers_gin ON public.exam_sessions USING gin (answers);
CREATE INDEX idx_exam_sessions_session_id ON public.exam_sessions USING btree (session_id);
CREATE INDEX idx_exam_sessions_user_status_time ON public.exam_sessions USING btree (user_id, status, start_time DESC);
CREATE INDEX idx_exam_tags_exam_id ON public.exam_tags USING btree (exam_id);
CREATE INDEX idx_exam_tags_tag_id ON public.exam_tags USING btree (tag_id);
CREATE INDEX idx_free_daerah_3t_category_id ON public.free_daerah_3t USING btree (category_id);
CREATE INDEX idx_free_daerah_3t_user_email ON public.free_daerah_3t USING btree (user_email);
CREATE INDEX idx_gamification_exam_session ON public.exam_gamification USING btree (exam_session_id);
CREATE INDEX idx_gamification_hints ON public.exam_gamification USING gin (hints_remaining);
CREATE INDEX idx_gamification_question_times ON public.exam_gamification USING gin (question_times);
CREATE INDEX idx_interview_grading_session_id ON public.interview_grading USING btree (session_id);
CREATE INDEX idx_interview_sessions_status ON public.interview_sessions USING btree (status);
CREATE INDEX idx_interview_sessions_user_id ON public.interview_sessions USING btree (user_id);
CREATE INDEX idx_invoices_client_email ON public.invoices USING btree (client_email);
CREATE INDEX idx_media_id ON public.media USING btree (id);
CREATE INDEX idx_metadata_exam_type ON public.exam_scores USING gin (((metadata_scores -> 'exam_type'::text)));
CREATE INDEX idx_metadata_scores_subject ON public.exam_scores USING gin (metadata_scores);
CREATE INDEX idx_orders_bundle_id ON public.orders USING btree (bundle_id);
CREATE INDEX idx_payments_client_email ON public.payments USING btree (client_email);
CREATE INDEX idx_promo_email_subscribers_email ON public.promo_email_subscribers USING btree (email);
CREATE INDEX idx_promo_email_subscribers_partner ON public.promo_email_subscribers USING btree (partner_id);
CREATE INDEX idx_question_explanations_question_id ON public.question_explanations USING btree (question_id, exam_id);
CREATE INDEX idx_question_hints_question_id ON public.question_hints USING btree (question_id, exam_id);
CREATE INDEX idx_question_instructions_question_id ON public.question_instructions USING btree (question_id, exam_id);
CREATE INDEX idx_question_metadata_name_value ON public.question_metadata USING btree (name, value);
CREATE INDEX idx_question_metadata_question_id ON public.question_metadata USING btree (question_id, exam_id);
CREATE INDEX idx_question_options_is_correct ON public.question_options USING btree (is_correct);
CREATE INDEX idx_question_options_question_id ON public.question_options USING btree (question_id, exam_id);
CREATE INDEX idx_referral_analytics_code ON public.referral_analytics USING btree (referral_code_id);
CREATE INDEX idx_referral_analytics_created ON public.referral_analytics USING btree (created_at);
CREATE INDEX idx_referral_analytics_type ON public.referral_analytics USING btree (type);
CREATE INDEX idx_referral_balances_user ON public.referral_balances USING btree (user_id);
CREATE INDEX idx_referral_codes_expires ON public.referral_codes USING btree (expires_at);
CREATE INDEX idx_referral_codes_referrer ON public.referral_codes USING btree (referrer_id);
CREATE INDEX idx_referral_codes_status ON public.referral_codes USING btree (status);
CREATE INDEX idx_referral_configs_active ON public.referral_configurations USING btree (is_active);
CREATE INDEX idx_referral_configs_type ON public.referral_configurations USING btree (type);
CREATE INDEX idx_referral_configs_user ON public.referral_configurations USING btree (user_id);
CREATE INDEX idx_referral_rewards_use ON public.referral_rewards USING btree (referral_use_id);
CREATE INDEX idx_referral_rewards_user ON public.referral_rewards USING btree (user_id);
CREATE INDEX idx_referral_uses_code ON public.referral_uses USING btree (referral_code_id);
CREATE INDEX idx_referral_uses_payment ON public.referral_uses USING btree (payment_id);
CREATE INDEX idx_referral_uses_redeemable ON public.referral_uses USING btree (is_redeemable);
CREATE INDEX idx_referral_uses_referee ON public.referral_uses USING btree (referee_id);
CREATE INDEX idx_referral_uses_subscription ON public.referral_uses USING btree (subscription_id);
CREATE INDEX idx_subscription_tiers_name ON public.subscription_tiers USING btree (name);
CREATE INDEX idx_subscription_tiers_price ON public.subscription_tiers USING btree (price_monthly);
CREATE INDEX idx_user_analytics_email ON public.user_analytics USING btree (user_email);
CREATE INDEX idx_user_analytics_event_type ON public.user_analytics USING btree (event_type);
CREATE INDEX idx_user_analytics_metadata ON public.user_analytics USING gin (event_metadata);
CREATE INDEX idx_user_analytics_session ON public.user_analytics USING btree (session_id);
CREATE INDEX idx_user_analytics_timestamp ON public.user_analytics USING btree (event_timestamp);
CREATE INDEX idx_user_analytics_update_timestamp ON public.user_analytics USING btree (update_timestamp);
CREATE INDEX idx_user_analytics_user_id ON public.user_analytics USING btree (user_id);
CREATE INDEX idx_user_demographics_city_name ON public.user_demographics USING btree (city_name);
CREATE INDEX idx_user_demographics_province_name ON public.user_demographics USING btree (province_name);
CREATE INDEX idx_user_demographics_target_major ON public.user_demographics USING btree (target_major);
CREATE INDEX idx_user_demographics_target_university ON public.user_demographics USING btree (target_university);
CREATE INDEX idx_user_goal_tracker_email ON public.user_goal_tracker USING btree (email);
CREATE INDEX idx_user_subscriptions_is_active ON public.user_subscriptions USING btree (is_active);
CREATE INDEX idx_user_subscriptions_tier_id ON public.user_subscriptions USING btree (tier_id);
CREATE INDEX idx_user_subscriptions_transaction_id ON public.user_subscriptions USING btree (transaction_id);
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions USING btree (user_id);
CREATE INDEX idx_users_email_username ON public.users USING btree (email, username);
CREATE INDEX idx_withdrawal_requests_created ON public.withdrawal_requests USING btree (created_at);
CREATE INDEX idx_withdrawal_requests_status ON public.withdrawal_requests USING btree (status);
CREATE INDEX idx_withdrawal_requests_user ON public.withdrawal_requests USING btree (user_id);
CREATE UNIQUE INDEX interview_grading_session_id_key ON public.interview_grading USING btree (session_id);
CREATE UNIQUE INDEX interview_sessions_session_id_key ON public.interview_sessions USING btree (session_id);
CREATE UNIQUE INDEX invoices_invoice_number_key ON public.invoices USING btree (invoice_number);
CREATE UNIQUE INDEX referral_balances_user_id_key ON public.referral_balances USING btree (user_id);
CREATE UNIQUE INDEX referral_codes_code_key ON public.referral_codes USING btree (code);
CREATE UNIQUE INDEX tags_name_key ON public.tags USING btree (name);
CREATE UNIQUE INDEX unique_email_partner ON public.promo_email_subscribers USING btree (email, partner_id);
CREATE UNIQUE INDEX unique_session_id ON public.interview_grading USING btree (session_id);
CREATE UNIQUE INDEX unique_user_goal_tracker_email ON public.user_goal_tracker USING btree (email);
CREATE UNIQUE INDEX user_ai_tokens_user_id_key ON public.user_ai_tokens USING btree (user_id);
CREATE UNIQUE INDEX user_relationships_follower_id_followee_id_key ON public.user_relationships USING btree (follower_id, followee_id);
CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);
CREATE UNIQUE INDEX users_username_key ON public.users USING btree (username);

-- Functions
CREATE OR REPLACE FUNCTION public.check_exam_session_type()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM exam_sessions 
        WHERE session_id = NEW.exam_session_id 
        AND type IN ('PRACTICE', 'EXAM')
    ) THEN
        RAISE EXCEPTION 'Invalid exam session type. Must be either PRACTICE or EXAM';
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_due_date()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.due_date := NEW.invoice_date + INTERVAL '1 day';
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_modified_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.modified_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_ulid()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Timestamp part: 48 bits = 10 characters base32
    timestamp_chars text;
    -- Randomness part: 80 bits = 16 characters base32
    random_chars text;
    -- Crockford's Base32 alphabet
    alphabet text := '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    now_ms bigint;
    result text := '';
    i integer;
BEGIN
    -- Get current timestamp in milliseconds
    now_ms := (extract(epoch from clock_timestamp()) * 1000)::bigint;
    
    -- Generate timestamp part (10 characters)
    FOR i IN 1..10 LOOP
        timestamp_chars := substr(alphabet, (now_ms % 32)::integer + 1, 1) || timestamp_chars;
        now_ms := now_ms / 32;
    END LOOP;
    
    -- Generate random part (16 characters)
    FOR i IN 1..16 LOOP
        random_chars := random_chars || substr(alphabet, (random() * 32)::integer + 1, 1);
    END LOOP;
    
    RETURN timestamp_chars || random_chars;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.insert_free_daerah_3t_with_ulid()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id := generate_ulid();
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.process_withdrawal()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Check if sufficient balance exists
    IF NOT EXISTS (
        SELECT 1 
        FROM referral_balances 
        WHERE user_id = NEW.user_id 
        AND available_balance >= NEW.amount
    ) THEN
        RAISE EXCEPTION 'Insufficient balance for withdrawal';
    END IF;

    -- Update both available and pending balance
    UPDATE referral_balances
    SET 
        available_balance = available_balance - NEW.amount,
        pending_balance = pending_balance + NEW.amount,
        modified_at = CURRENT_TIMESTAMP
    WHERE user_id = NEW.user_id;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_withdrawal_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.status = 'REJECTED' AND OLD.status = 'PENDING' THEN
        UPDATE referral_balances
        SET available_balance = available_balance + NEW.amount
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE PROCEDURE public.process_question_data(IN p_exam_id character varying, IN question_data jsonb)
 LANGUAGE plpgsql
AS $procedure$
DECLARE
    q JSONB;
    m JSONB;
    o JSONB;
    i JSONB;
    h JSONB;
    e JSONB;
    instruction_idx INT;
    hint_idx INT;
    exp_idx INT;
    instruction_content TEXT;
    hint_content TEXT;
    q_id VARCHAR(1024);
    option_id VARCHAR(1024);
BEGIN
    -- Loop through each question
    FOR q IN SELECT jsonb_array_elements(question_data)
    LOOP
        -- Use the ID exactly as it appears in the data
        q_id := q->>'id';
        
        -- Insert question with upsert
        INSERT INTO exam_questions(id, exam_id, title)
        VALUES (
            q_id,
            p_exam_id,
            COALESCE((q->'question'->0->'contents'->0->>'content'), 
                     (q->'question'->0->>'content'), 
                     (q->>'question'), 
                     'No title')
        ) ON CONFLICT (id, exam_id) DO UPDATE SET
            title = COALESCE((q->'question'->0->'contents'->0->>'content'), 
                            (q->'question'->0->>'content'), 
                            (q->>'question'), 
                            'No title');
        
        -- Clear existing metadata before inserting new
        DELETE FROM question_metadata 
        WHERE question_metadata.question_id = q_id AND question_metadata.exam_id = p_exam_id;
        
        -- Insert metadata with null check
        IF q ? 'metadata' AND jsonb_typeof(q->'metadata') = 'array' THEN
            FOR m IN SELECT jsonb_array_elements(q->'metadata')
            LOOP
                -- Skip metadata with null values
                IF m->>'name' IS NOT NULL AND m->>'value' IS NOT NULL THEN
                    INSERT INTO question_metadata(question_id, exam_id, name, value, level)
                    VALUES (
                        q_id,
                        p_exam_id,
                        m->>'name',
                        m->>'value',
                        COALESCE((m->>'level')::INT, 0)
                    );
                END IF;
            END LOOP;
        END IF;
        
        -- Clear existing options before inserting new
        DELETE FROM question_options 
        WHERE question_options.question_id = q_id AND question_options.exam_id = p_exam_id;
        
        -- Insert options if they exist
        IF q ? 'options' AND q->'options' ? 'values' AND jsonb_typeof(q->'options'->'values') = 'array' THEN
            FOR o IN SELECT jsonb_array_elements(q->'options'->'values')
            LOOP
                -- Use the option ID exactly as it appears in the data
                option_id := o->>'id';
                
                -- Only insert if we have an ID
                IF option_id IS NOT NULL THEN
                    -- Use ON CONFLICT to handle duplicate keys
                    INSERT INTO question_options(id, question_id, exam_id, content, is_correct)
                    VALUES (
                        option_id,
                        q_id,
                        p_exam_id,
                        COALESCE((o->'data'->0->'contents'->0->>'content'), 
                                 (o->'data'->0->>'content'),
                                 (o->>'content'),
                                 'No content'),
                        COALESCE((o->>'is_correct')::BOOLEAN, FALSE)
                    )
                    ON CONFLICT (id, question_id, exam_id) DO UPDATE SET
                        content = COALESCE((o->'data'->0->'contents'->0->>'content'), 
                                          (o->'data'->0->>'content'),
                                          (o->>'content'),
                                          'No content'),
                        is_correct = COALESCE((o->>'is_correct')::BOOLEAN, FALSE);
                END IF;
            END LOOP;
        END IF;
        
        -- Clear existing instructions before inserting new
        DELETE FROM question_instructions 
        WHERE question_instructions.question_id = q_id AND question_instructions.exam_id = p_exam_id;
        
        -- Insert instructions with content check, handling different formats
        IF q ? 'instruction' THEN
            instruction_idx := 1;
            
            IF jsonb_typeof(q->'instruction') = 'array' THEN
                FOR i IN SELECT jsonb_array_elements(q->'instruction')
                LOOP
                    instruction_content := NULL;
                    
                    -- Check different possible structures for instructions
                    IF jsonb_typeof(i) = 'string' THEN
                        -- Direct string in array
                        instruction_content := i#>>'{}';
                    ELSIF i ? 'contents' AND jsonb_array_length(i->'contents') > 0 THEN
                        -- Complex structure with contents array
                        instruction_content := i->'contents'->0->>'content';
                    ELSIF i ? 'content' THEN
                        -- Simple object with content field
                        instruction_content := i->>'content';
                    END IF;
                    
                    IF instruction_content IS NOT NULL THEN
                        INSERT INTO question_instructions(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            instruction_content,
                            instruction_idx
                        );
                        instruction_idx := instruction_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'instruction') = 'string' THEN
                -- Single string instruction
                INSERT INTO question_instructions(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'instruction',
                    instruction_idx
                );
            END IF;
        END IF;
        
        -- Clear existing hints before inserting new
        DELETE FROM question_hints 
        WHERE question_hints.question_id = q_id AND question_hints.exam_id = p_exam_id;
        
        -- Insert hints with content check, handling different formats
        IF q ? 'hints' THEN
            hint_idx := 1;
            
            IF jsonb_typeof(q->'hints') = 'array' THEN
                FOR h IN SELECT jsonb_array_elements(q->'hints')
                LOOP
                    hint_content := NULL;
                    
                    -- Check different possible structures for hints
                    IF jsonb_typeof(h) = 'string' THEN
                        -- Direct string in array
                        hint_content := h#>>'{}';
                    ELSIF h ? 'contents' AND jsonb_array_length(h->'contents') > 0 THEN
                        -- Complex structure with contents array
                        hint_content := h->'contents'->0->>'content';
                    ELSIF h ? 'content' THEN
                        -- Simple object with content field
                        hint_content := h->>'content';
                    END IF;
                    
                    IF hint_content IS NOT NULL THEN
                        INSERT INTO question_hints(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            hint_content,
                            hint_idx
                        );
                        hint_idx := hint_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'hints') = 'string' THEN
                -- Single string hint
                INSERT INTO question_hints(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'hints',
                    hint_idx
                );
            END IF;
        END IF;
        
        -- Clear existing explanations before inserting new
        DELETE FROM question_explanations 
        WHERE question_explanations.question_id = q_id AND question_explanations.exam_id = p_exam_id;
        
        -- Insert explanations with content check, handling different formats
        IF q ? 'explanation' THEN
            exp_idx := 1;
            
            IF jsonb_typeof(q->'explanation') = 'array' THEN
                FOR e IN SELECT jsonb_array_elements(q->'explanation')
                LOOP
                    IF jsonb_typeof(e) = 'string' THEN
                        -- Direct string in array
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e#>>'{}',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    ELSIF e ? 'contents' AND jsonb_array_length(e->'contents') > 0 AND e->'contents'->0->>'content' IS NOT NULL THEN
                        -- Complex structure with contents array
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e->'contents'->0->>'content',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    ELSIF e ? 'content' AND e->>'content' IS NOT NULL THEN
                        -- Simple object with content field
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e->>'content',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'explanation') = 'string' THEN
                -- Single string explanation
                INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'explanation',
                    exp_idx
                );
            END IF;
        END IF;
    END LOOP;
EXCEPTION WHEN OTHERS THEN
    -- Log error details
    RAISE WARNING 'Error in process_question_data: %, exam_id: %', SQLERRM, p_exam_id;
END;
$procedure$
;

CREATE OR REPLACE FUNCTION public.convert_to_bigint(target_table_name text, target_column_name text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = target_table_name AND column_name = target_column_name
    ) THEN
        RAISE NOTICE 'Column % does not exist in table %. Skipping conversion.', target_column_name, target_table_name;
        RETURN;
    END IF;

    -- Check if the new column already exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = target_table_name AND column_name = target_column_name || '_new'
    ) THEN
        -- Add the new column
        EXECUTE format('ALTER TABLE %I ADD COLUMN %I BIGINT', target_table_name, target_column_name || '_new');
        -- Copy data from the old column to the new column
        EXECUTE format('UPDATE %I SET %I = %I::BIGINT WHERE %I IS NOT NULL', target_table_name, target_column_name || '_new', target_column_name, target_column_name);
        -- Drop the old column
        EXECUTE format('ALTER TABLE %I DROP COLUMN %I', target_table_name, target_column_name);
        -- Rename the new column to the original column name
        EXECUTE format('ALTER TABLE %I RENAME COLUMN %I TO %I', target_table_name, target_column_name || '_new', target_column_name);
        RAISE NOTICE 'Column % in table % converted to BIGINT.', target_column_name, target_table_name;
    ELSE
        RAISE NOTICE 'Column %_new already exists in table %. Skipping conversion.', target_column_name, target_table_name;
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_question_data_trigger()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Skip processing if data is NULL or empty array
    IF NEW.data IS NULL OR NEW.data = '[]'::jsonb THEN
        RETURN NEW;
    END IF;
    
    BEGIN
        -- Call the procedure to normalize the data
        CALL process_question_data(NEW.exam_id, NEW.data);
    EXCEPTION WHEN OTHERS THEN
        -- Log the error but don't fail the transaction
        RAISE WARNING 'Error normalizing question data for exam_id %: %', NEW.exam_id, SQLERRM;
    END;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.process_exam_data_in_batches()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    rec RECORD;
    batch_counter INT := 0;
    exam_cursor CURSOR FOR SELECT exam_id, data FROM exam_question_answer_hints;
BEGIN
    OPEN exam_cursor;
    
    LOOP
        -- Fetch next record
        FETCH exam_cursor INTO rec;
        EXIT WHEN NOT FOUND;
        
        -- Process this batch in a separate transaction
        BEGIN
            RAISE NOTICE 'Processing exam_id: %', rec.exam_id;
            CALL process_question_data(rec.exam_id, rec.data);
            batch_counter := batch_counter + 1;
            RAISE NOTICE 'Processed exam: %, total: %', rec.exam_id, batch_counter;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error processing exam_id %: %', rec.exam_id, SQLERRM;
        END;
    END LOOP;
    
    CLOSE exam_cursor;
    
    RAISE NOTICE 'Finished processing % exams', batch_counter;
END;
$function$
;

-- Triggers
CREATE TRIGGER ensure_exam_session BEFORE INSERT OR UPDATE ON public.exam_gamification FOR EACH ROW EXECUTE FUNCTION check_exam_session_type();

CREATE TRIGGER update_user_demographics_modtime BEFORE UPDATE ON public.user_demographics FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER user_modified_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER available_exams_modified_at BEFORE UPDATE ON public.available_exams FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER terms_of_service_modified_at BEFORE UPDATE ON public.terms_of_services FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER category_modified_at BEFORE UPDATE ON public.categories FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER tag_modified_at BEFORE UPDATE ON public.tags FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER order_modified_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER payment_modified_at BEFORE UPDATE ON public.payments FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER invoice_modified_at BEFORE UPDATE ON public.invoices FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER set_due_date_trigger BEFORE INSERT ON public.invoices FOR EACH ROW EXECUTE FUNCTION set_due_date();

CREATE TRIGGER update_media_modified_at BEFORE UPDATE ON public.media FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_sessions_modified_at BEFORE UPDATE ON public.exam_sessions FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_scores_modified_at BEFORE UPDATE ON public.exam_scores FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_gamification_modified_at BEFORE UPDATE ON public.exam_gamification FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER subscription_tiers_modified_at BEFORE UPDATE ON public.subscription_tiers FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER user_subscriptions_modified_at BEFORE UPDATE ON public.user_subscriptions FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER update_chat_ai_history_modified_at BEFORE UPDATE ON public.chat_ai_history FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER update_user_ai_tokens_modified_at BEFORE UPDATE ON public.user_ai_tokens FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_codes_modified_at BEFORE UPDATE ON public.referral_codes FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_analytics_modified_at BEFORE UPDATE ON public.referral_analytics FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_uses_modified_at BEFORE UPDATE ON public.referral_uses FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_rewards_modified_at BEFORE UPDATE ON public.referral_rewards FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_balances_modified_at BEFORE UPDATE ON public.referral_balances FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER withdrawal_requests_modified_at BEFORE UPDATE ON public.withdrawal_requests FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER reduce_balance_on_withdrawal AFTER INSERT ON public.withdrawal_requests FOR EACH ROW WHEN (((new.status)::text = 'PENDING'::text)) EXECUTE FUNCTION process_withdrawal();

CREATE TRIGGER handle_withdrawal_status_updates AFTER UPDATE ON public.withdrawal_requests FOR EACH ROW EXECUTE FUNCTION handle_withdrawal_status_change();

CREATE TRIGGER referral_configs_modified_at BEFORE UPDATE ON public.referral_configurations FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_bundles_modified_at BEFORE UPDATE ON public.exam_bundles FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_bundle_items_modified_at BEFORE UPDATE ON public.exam_bundle_items FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER set_free_daerah_3t_ulid BEFORE INSERT ON public.free_daerah_3t FOR EACH ROW EXECUTE FUNCTION insert_free_daerah_3t_with_ulid();

CREATE TRIGGER free_daerah_3t_modified_at BEFORE UPDATE ON public.free_daerah_3t FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_question_data_sync AFTER INSERT OR UPDATE ON public.exam_question_answer_hints FOR EACH ROW EXECUTE FUNCTION sync_question_data_trigger();

