BEGIN;

-- Convert province_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN province_id_old VARCHAR(50);
UPDATE user_demographics SET province_id_old = province_id::VARCHAR(50) WHERE province_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN province_id;
ALTER TABLE user_demographics RENAME COLUMN province_id_old TO province_id;

-- Convert city_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN city_id_old VARCHAR(50);
UPDATE user_demographics SET city_id_old = city_id::VARCHAR(50) WHERE city_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN city_id;
ALTER TABLE user_demographics RENAME COLUMN city_id_old TO city_id;

-- Convert district_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN district_id_old VARCHAR(50);
UPDATE user_demographics SET district_id_old = district_id::VARCHAR(50) WHERE district_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN district_id;
ALTER TABLE user_demographics RENAME COLUMN district_id_old TO district_id;

-- Convert village_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN village_id_old VARCHAR(50);
UPDATE user_demographics SET village_id_old = village_id::VARCHAR(50) WHERE village_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN village_id;
ALTER TABLE user_demographics RENAME COLUMN village_id_old TO village_id;

-- Convert education_level_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN education_level_id_old VARCHAR(50);
UPDATE user_demographics SET education_level_id_old = education_level_id::VARCHAR(50) WHERE education_level_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN education_level_id;
ALTER TABLE user_demographics RENAME COLUMN education_level_id_old TO education_level_id;

-- Convert program_study_id back to VARCHAR
ALTER TABLE user_demographics ADD COLUMN program_study_id_old VARCHAR(50);
UPDATE user_demographics SET program_study_id_old = program_study_id::VARCHAR(50) WHERE program_study_id IS NOT NULL;
ALTER TABLE user_demographics DROP COLUMN program_study_id;
ALTER TABLE user_demographics RENAME COLUMN program_study_id_old TO program_study_id;

COMMIT;