package users

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/terang-ai/backend-service/lib"
)

// UserAnalytic represents the user_analytics table structure
type UserAnalytic struct {
	ID              string    `db:"id" json:"id"`
	UserEmail       string    `db:"user_email" json:"userEmail"`
	EventType       string    `db:"event_type" json:"eventType"`
	EventTimestamp  time.Time `db:"event_timestamp" json:"eventTimestamp"`
	SessionID       string    `db:"session_id" json:"sessionId"`
	DurationSeconds int       `db:"duration_seconds" json:"durationSeconds"`
	Path            string    `db:"path" json:"path"`
	DeviceInfo      string    `db:"device_info" json:"deviceInfo"`       // JSONB stored as string
	LocationInfo    string    `db:"location_info" json:"locationInfo"`   // JSONB stored as string
	EventMetadata   string    `db:"event_metadata" json:"eventMetadata"` // JSONB stored as string
	Status          string    `db:"status" json:"status"`
}

// TrackEventRequest represents the payload for tracking events
type TrackEventRequest struct {
	UserEmail       string `json:"userEmail" binding:"required"`
	EventType       string `json:"eventType" binding:"required"`
	SessionID       string `json:"sessionId"`
	DurationSeconds int    `json:"durationSeconds"`
	Path            string `json:"path"`
	DeviceInfo      string `json:"deviceInfo"`
	LocationInfo    string `json:"locationInfo"`
	EventMetadata   string `json:"eventMetadata"`
	Status          string `json:"status"`
}

// LastEvent holds the data for a user's last event
type LastEvent struct {
	Path           string    `db:"path"`
	EventTimestamp time.Time `db:"event_timestamp"`
}

// TrackEventResult represents the result of a track event operation
type TrackEventResult struct {
	Event UserAnalytic
	Error error
}

// EventCache provides in-memory caching for recent user events
type EventCache struct {
	events map[string]LastEvent // key is userEmail
	mutex  sync.RWMutex
	ttl    time.Duration
}

type UserActivityPoint struct {
	Date  time.Time `json:"date"`
	Count int       `json:"count"`
}

// NewEventCache creates a new event cache with the specified TTL
func NewEventCache(ttl time.Duration) *EventCache {
	cache := &EventCache{
		events: make(map[string]LastEvent),
		ttl:    ttl,
	}

	// Start background cleaner
	go cache.cleaner()
	return cache
}

// cleaner removes expired entries from the cache
func (c *EventCache) cleaner() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()
		for userEmail, event := range c.events {
			if now.Sub(event.EventTimestamp) > c.ttl {
				delete(c.events, userEmail)
			}
		}
		c.mutex.Unlock()
	}
}

// Get retrieves a user's last event from the cache
func (c *EventCache) Get(userEmail string) (LastEvent, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	event, found := c.events[userEmail]
	if !found {
		return LastEvent{}, false
	}

	// Check if the entry is still valid
	if time.Since(event.EventTimestamp) > c.ttl {
		return LastEvent{}, false
	}

	return event, true
}

// Set stores a user's last event in the cache
func (c *EventCache) Set(userEmail string, event LastEvent) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.events[userEmail] = event
}

// Global event cache instance
var eventCache = NewEventCache(5 * time.Minute)

// RegisterUserAnalyticsRoutes registers the user analytics tracking routes
func RegisterUserAnalyticsRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.POST("/analytics", trackEvent(dbx))
		v0.GET("/analytics", getAllEvents(dbx))
		v0.GET("/analytics/:id", getOneEvent(dbx))
		v0.GET("/users/:userEmail/analytics", getUserEvents(dbx))
		v0.GET("/users/:userEmail/analytics/latest", getLatestUserEvent(dbx))
		v0.GET("/analytics/summary", getAnalyticsSummary(dbx))
		v0.GET("/analytics/user-activity", getUserActivity(dbx))
	}
}

// trackEvent records a new analytics event with improved concurrency
func trackEvent(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var eventReq TrackEventRequest
		if err := c.BindJSON(&eventReq); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Create a context with timeout for DB operations
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		// Create a channel to receive the result
		resultCh := make(chan TrackEventResult, 1)

		// Start goroutine to handle the event tracking
		go func() {
			// First check if we have the last event in cache
			lastEvent, found := eventCache.Get(eventReq.UserEmail)

			// If not in cache, get from database
			if !found {
				lastEventQuery := `
					SELECT path, event_timestamp 
					FROM user_analytics 
					WHERE user_email = $1 
					ORDER BY event_timestamp DESC 
					LIMIT 1
				`

				err := dbx.GetContext(ctx, &lastEvent, lastEventQuery, eventReq.UserEmail)
				if err != nil && err != sql.ErrNoRows {
					resultCh <- TrackEventResult{Error: err}
					return
				}

				// If found in DB, update the cache
				if err != sql.ErrNoRows {
					eventCache.Set(eventReq.UserEmail, lastEvent)
				}
			}

			// Determine if we should create a new record or update existing one
			shouldCreateNew := !found ||
				lastEvent.Path != eventReq.Path ||
				time.Since(lastEvent.EventTimestamp) > time.Minute

			if shouldCreateNew {
				// Create a new event record
				id := lib.GenerateULID()
				insertQuery := `
					INSERT INTO user_analytics (
						id, user_email, event_type, session_id, duration_seconds,
						path, device_info, location_info, event_metadata, status
					)
					VALUES ($1, $2, $3, $4, $5, $6, $7::jsonb, $8::jsonb, $9::jsonb, $10)
					RETURNING id, user_email, event_type, event_timestamp, session_id,
							  duration_seconds, path, device_info, location_info,
							  event_metadata, status
				`

				var result UserAnalytic
				err := dbx.GetContext(ctx, &result, insertQuery,
					id, eventReq.UserEmail, eventReq.EventType, eventReq.SessionID, eventReq.DurationSeconds,
					eventReq.Path, eventReq.DeviceInfo, eventReq.LocationInfo, eventReq.EventMetadata, eventReq.Status)

				if err != nil {
					resultCh <- TrackEventResult{Error: err}
					return
				}

				// Update the cache with this new event
				eventCache.Set(eventReq.UserEmail, LastEvent{
					Path:           result.Path,
					EventTimestamp: result.EventTimestamp,
				})

				resultCh <- TrackEventResult{Event: result}
				return
			}

			// Update existing record if it's the same path and within 1 minute
			updateQuery := `
				WITH latest_event AS (
					SELECT id 
					FROM user_analytics 
					WHERE user_email = $1 
					AND path = $2
					AND event_timestamp >= NOW() - INTERVAL '1 minute'
					ORDER BY event_timestamp DESC 
					LIMIT 1
				)
				UPDATE user_analytics 
				SET duration_seconds = duration_seconds + $3,
					event_metadata = CASE 
						WHEN $4::jsonb IS NOT NULL THEN $4::jsonb 
						ELSE event_metadata 
					END,
					status = CASE 
						WHEN $5 != '' THEN $5 
						ELSE status 
					END,
					device_info = CASE 
						WHEN $6::jsonb IS NOT NULL THEN $6::jsonb 
						ELSE device_info 
					END,
					location_info = CASE 
						WHEN $7::jsonb IS NOT NULL THEN $7::jsonb 
						ELSE location_info 
					END,
					session_id = $8,
					update_timestamp = CURRENT_TIMESTAMP
				WHERE id = (SELECT id FROM latest_event)
				RETURNING id, user_email, event_type, event_timestamp, session_id,
						  duration_seconds, path, device_info, location_info,
						  event_metadata, status
			`

			var result UserAnalytic
			err := dbx.GetContext(ctx, &result, updateQuery,
				eventReq.UserEmail,
				eventReq.Path,
				eventReq.DurationSeconds,
				eventReq.EventMetadata,
				eventReq.Status,
				eventReq.DeviceInfo,
				eventReq.LocationInfo,
				eventReq.SessionID)

			if err != nil {
				resultCh <- TrackEventResult{Error: err}
				return
			}

			// Update the cache with the result timestamp
			eventCache.Set(eventReq.UserEmail, LastEvent{
				Path:           result.Path,
				EventTimestamp: result.EventTimestamp,
			})

			resultCh <- TrackEventResult{Event: result}
		}()

		// Wait for the result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusRequestTimeout, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				log.Printf("Error tracking event for user %s: %v", eventReq.UserEmail, result.Error)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to track event"})
				return
			}

			if shouldCreateNew(result.Event.Path, eventReq.Path, result.Event.EventTimestamp) {
				c.JSON(http.StatusCreated, gin.H{"event": result.Event})
			} else {
				c.JSON(http.StatusOK, gin.H{"event": result.Event})
			}
		}
	}
}

// Helper function to check if we should create a new event
func shouldCreateNew(lastPath, currentPath string, lastTimestamp time.Time) bool {
	return lastPath != currentPath || time.Since(lastTimestamp) > time.Minute
}

// getUserActivity retrieves user activity metrics with improved concurrency
// Improved getUserActivity function that properly reconciles with users table
func getUserActivity(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(c.Request.Context(), 300*time.Second)
		defer cancel()

		// Create a struct to hold all metrics
		metrics := struct {
			Daily         int                 `json:"daily"`
			Weekly        int                 `json:"weekly"`
			Monthly       int                 `json:"monthly"`
			YearToDate    int                 `json:"yearToDate"`
			TotalUsers    int                 `json:"totalUsers"`
			ActiveUsers   []UserActivityPoint `json:"activeUsers"`
			RetentionRate float64             `json:"retentionRate"`
			GrowthRate    float64             `json:"growthRate"`
		}{}

		// Use a WaitGroup to synchronize goroutines
		var wg sync.WaitGroup
		var mu sync.Mutex
		errCh := make(chan error, 5)

		// 0. Fetch total registered users first - this is our source of truth
		wg.Add(1)
		go func() {
			defer wg.Done()

			totalQuery := `SELECT COUNT(*) FROM users WHERE is_banned = false`

			var total int
			err := dbx.GetContext(ctx, &total, totalQuery)
			if err != nil {
				errCh <- fmt.Errorf("failed to fetch total users: %w", err)
				return
			}

			mu.Lock()
			metrics.TotalUsers = total
			mu.Unlock()
		}()

		// 1. Fetch period metrics with improved query that prevents duplication
		wg.Add(1)
		go func() {
			defer wg.Done()

			// This query ensures we're only counting actual users and
			// de-duplicating based on both user_id and user_email
			periodQuery := `
                WITH valid_user_analytics AS (
                    SELECT 
                        COALESCE(ua.user_id, u.id) AS actual_user_id,
                        MAX(ua.event_timestamp) AS latest_event
                    FROM user_analytics ua
                    LEFT JOIN users u ON 
                        (ua.user_id IS NOT NULL AND ua.user_id = u.id) OR
                        (ua.user_id IS NULL AND ua.user_email = u.email)
                    WHERE 
                        u.id IS NOT NULL AND
                        ua.event_timestamp >= LEAST(NOW() - INTERVAL '365 days', DATE_TRUNC('year', NOW()))
                    GROUP BY COALESCE(ua.user_id, u.id)
                )
                SELECT 
                    COUNT(DISTINCT CASE WHEN latest_event >= NOW() - INTERVAL '24 hours' THEN actual_user_id END) as daily,
                    COUNT(DISTINCT CASE WHEN latest_event >= NOW() - INTERVAL '7 days' THEN actual_user_id END) as weekly,
                    COUNT(DISTINCT CASE WHEN latest_event >= NOW() - INTERVAL '30 days' THEN actual_user_id END) as monthly,
                    COUNT(DISTINCT CASE WHEN latest_event >= DATE_TRUNC('year', NOW()) THEN actual_user_id END) as year_to_date
                FROM valid_user_analytics
            `

			var result struct {
				Daily      int `db:"daily"`
				Weekly     int `db:"weekly"`
				Monthly    int `db:"monthly"`
				YearToDate int `db:"year_to_date"`
			}

			err := dbx.GetContext(ctx, &result, periodQuery)
			if err != nil {
				errCh <- fmt.Errorf("failed to fetch period metrics: %w", err)
				return
			}

			mu.Lock()
			metrics.Daily = result.Daily
			metrics.Weekly = result.Weekly
			metrics.Monthly = result.Monthly
			metrics.YearToDate = result.YearToDate
			mu.Unlock()
		}()

		// 2. Fetch retention rate with similar deduplication approach
		wg.Add(1)
		go func() {
			defer wg.Done()

			retentionQuery := `
                WITH valid_users AS (
                    SELECT 
                        COALESCE(ua.user_id, u.id) AS actual_user_id,
                        MIN(ua.event_timestamp) as first_visit,
                        MAX(ua.event_timestamp) as last_visit
                    FROM user_analytics ua
                    LEFT JOIN users u ON 
                        (ua.user_id IS NOT NULL AND ua.user_id = u.id) OR
                        (ua.user_id IS NULL AND ua.user_email = u.email)
                    WHERE 
                        u.id IS NOT NULL AND
                        ua.event_timestamp >= NOW() - INTERVAL '30 days'
                    GROUP BY COALESCE(ua.user_id, u.id)
                ),
                all_users AS (
                    SELECT actual_user_id FROM valid_users
                ),
                retained_users AS (
                    SELECT actual_user_id 
                    FROM valid_users
                    WHERE last_visit > first_visit + INTERVAL '7 days'
                )
                SELECT 
                    COALESCE(
                        COUNT(r.actual_user_id)::FLOAT / NULLIF(COUNT(a.actual_user_id), 0) * 100,
                        0
                    ) as retention_rate
                FROM all_users a
                LEFT JOIN retained_users r ON a.actual_user_id = r.actual_user_id
            `

			var result struct {
				RetentionRate float64 `db:"retention_rate"`
			}

			err := dbx.GetContext(ctx, &result, retentionQuery)
			if err != nil {
				errCh <- fmt.Errorf("failed to fetch retention rate: %w", err)
				return
			}

			mu.Lock()
			metrics.RetentionRate = result.RetentionRate
			mu.Unlock()
		}()

		// 3. Fetch growth rate with deduplication
		wg.Add(1)
		go func() {
			defer wg.Done()

			growthQuery := `
                WITH current_period AS (
                    SELECT 
                        COALESCE(ua.user_id, u.id) AS actual_user_id
                    FROM user_analytics ua
                    LEFT JOIN users u ON 
                        (ua.user_id IS NOT NULL AND ua.user_id = u.id) OR
                        (ua.user_id IS NULL AND ua.user_email = u.email)
                    WHERE 
                        u.id IS NOT NULL AND
                        ua.event_timestamp >= NOW() - INTERVAL '30 days'
                    GROUP BY COALESCE(ua.user_id, u.id)
                ),
                previous_period AS (
                    SELECT 
                        COALESCE(ua.user_id, u.id) AS actual_user_id
                    FROM user_analytics ua
                    LEFT JOIN users u ON 
                        (ua.user_id IS NOT NULL AND ua.user_id = u.id) OR
                        (ua.user_id IS NULL AND ua.user_email = u.email)
                    WHERE 
                        u.id IS NOT NULL AND
                        ua.event_timestamp >= NOW() - INTERVAL '60 days' AND
                        ua.event_timestamp < NOW() - INTERVAL '30 days'
                    GROUP BY COALESCE(ua.user_id, u.id)
                )
                SELECT 
                    CASE 
                        WHEN prev_count = 0 THEN 100
                        ELSE ((curr_count - prev_count)::FLOAT / prev_count * 100)
                    END as growth_rate
                FROM (
                    SELECT 
                        (SELECT COUNT(*) FROM current_period) as curr_count,
                        (SELECT COUNT(*) FROM previous_period) as prev_count
                ) counts
            `

			var result struct {
				GrowthRate float64 `db:"growth_rate"`
			}

			err := dbx.GetContext(ctx, &result, growthQuery)
			if err != nil {
				errCh <- fmt.Errorf("failed to fetch growth rate: %w", err)
				return
			}

			mu.Lock()
			metrics.GrowthRate = result.GrowthRate
			mu.Unlock()
		}()

		// 4. Fetch active users trend with deduplication
		wg.Add(1)
		go func() {
			defer wg.Done()

			activeUsersQuery := `
                WITH daily_users AS (
                    SELECT 
                        DATE_TRUNC('day', ua.event_timestamp) as date,
                        COALESCE(ua.user_id, u.id) AS actual_user_id
                    FROM user_analytics ua
                    LEFT JOIN users u ON 
                        (ua.user_id IS NOT NULL AND ua.user_id = u.id) OR
                        (ua.user_id IS NULL AND ua.user_email = u.email)
                    WHERE 
                        u.id IS NOT NULL AND
                        ua.event_timestamp >= NOW() - INTERVAL '30 days'
                )
                SELECT 
                    date,
                    COUNT(DISTINCT actual_user_id) as count
                FROM daily_users
                GROUP BY date
                ORDER BY date ASC
            `

			var activeUsers []UserActivityPoint
			err := dbx.SelectContext(ctx, &activeUsers, activeUsersQuery)
			if err != nil {
				errCh <- fmt.Errorf("failed to fetch active users trend: %w", err)
				// Continue with an empty array instead of failing
				activeUsers = []UserActivityPoint{}
			}

			mu.Lock()
			metrics.ActiveUsers = activeUsers
			mu.Unlock()
		}()

		// Wait for all goroutines to complete
		wg.Wait()
		close(errCh)

		// Check for errors
		for err := range errCh {
			log.Printf("Error in getUserActivity: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch some metrics"})
			return
		}

		// Validate metrics against total users as a sanity check
		if metrics.Monthly > metrics.TotalUsers {
			log.Printf("Warning: Monthly active users (%d) exceeds total users (%d), capping value",
				metrics.Monthly, metrics.TotalUsers)
			metrics.Monthly = metrics.TotalUsers
		}

		if metrics.YearToDate > metrics.TotalUsers {
			log.Printf("Warning: Year-to-date active users (%d) exceeds total users (%d), capping value",
				metrics.YearToDate, metrics.TotalUsers)
			metrics.YearToDate = metrics.TotalUsers
		}

		c.JSON(http.StatusOK, gin.H{
			"metrics": metrics,
		})
	}
}

// Additional functions (kept unchanged for brevity)
func getAllEvents(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit := c.DefaultQuery("limit", "100")
		offset := c.DefaultQuery("offset", "0")
		eventType := c.Query("eventType")

		// Add context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 300*time.Second)
		defer cancel()

		var events []UserAnalytic
		query := `
			SELECT id, user_email, event_type, event_timestamp, session_id,
				   duration_seconds, path, device_info, location_info,
				   event_metadata, status
			FROM user_analytics
			WHERE ($1 = '' OR event_type = $1)
			ORDER BY event_timestamp DESC
			LIMIT $2 OFFSET $3
		`

		// Use SelectContext instead of Select
		err := dbx.SelectContext(ctx, &events, query, eventType, limit, offset)
		if err != nil {
			log.Printf("Error fetching events: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch events"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"events": events})
	}
}

// getOneEvent retrieves a specific analytics record
func getOneEvent(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")

		// Add context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		var event UserAnalytic
		query := `
			SELECT id, user_email, event_type, event_timestamp, session_id,
				   duration_seconds, path, device_info, location_info,
				   event_metadata, status
			FROM user_analytics
			WHERE id = $1
		`

		// Use GetContext instead of Get
		err := dbx.GetContext(ctx, &event, query, id)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Event record not found"})
				return
			}
			log.Printf("Error fetching event %s: %v", id, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch event"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"event": event})
	}
}

// getUserEvents retrieves analytics history for a specific user
func getUserEvents(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userEmail := c.Param("userEmail")
		limit := c.DefaultQuery("limit", "50")
		offset := c.DefaultQuery("offset", "0")
		eventType := c.Query("eventType")

		// Add context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 300*time.Second)
		defer cancel()

		var events []UserAnalytic
		query := `
			SELECT id, user_email, event_type, event_timestamp, session_id,
				   duration_seconds, path, device_info, location_info,
				   event_metadata, status
			FROM user_analytics
			WHERE user_email = $1 
			AND ($2 = '' OR event_type = $2)
			ORDER BY event_timestamp DESC
			LIMIT $3 OFFSET $4
		`

		// Use SelectContext instead of Select
		err := dbx.SelectContext(ctx, &events, query, userEmail, eventType, limit, offset)
		if err != nil {
			log.Printf("Error fetching events for user %s: %v", userEmail, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user events"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"events": events})
	}
}

// getLatestUserEvent gets the most recent event for a user
func getLatestUserEvent(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userEmail := c.Param("userEmail")
		eventType := c.Query("eventType")

		// Check cache first for faster response
		if cachedEvent, found := eventCache.Get(userEmail); found {
			// If there's a cached entry, we can use it to provide immediate response
			// Note: This is a simplified implementation as we're only caching path and timestamp
			// For a full implementation, you would cache the entire event or use a quick lookup

			// But we'll still need to get the full event from database
			ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
			defer cancel()

			var event UserAnalytic
			query := `
				SELECT id, user_email, event_type, event_timestamp, session_id,
					   duration_seconds, path, device_info, location_info,
					   event_metadata, status
				FROM user_analytics
				WHERE user_email = $1 AND path = $2
				AND ($3 = '' OR event_type = $3)
				ORDER BY event_timestamp DESC
				LIMIT 1
			`

			err := dbx.GetContext(ctx, &event, query, userEmail, cachedEvent.Path, eventType)
			if err == nil {
				// Cache hit and DB lookup successful
				c.JSON(http.StatusOK, gin.H{
					"event":  event,
					"source": "cache",
				})
				return
			}
		}

		// Cache miss or DB lookup failed, do the regular lookup
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		var event UserAnalytic
		query := `
			SELECT id, user_email, event_type, event_timestamp, session_id,
				   duration_seconds, path, device_info, location_info,
				   event_metadata, status
			FROM user_analytics
			WHERE user_email = $1
			AND ($2 = '' OR event_type = $2)
			ORDER BY event_timestamp DESC
			LIMIT 1
		`

		// Use GetContext instead of Get
		err := dbx.GetContext(ctx, &event, query, userEmail, eventType)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "No event records found for user"})
				return
			}
			log.Printf("Error fetching latest event for user %s: %v", userEmail, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch latest event"})
			return
		}

		// Update cache with this event
		eventCache.Set(userEmail, LastEvent{
			Path:           event.Path,
			EventTimestamp: event.EventTimestamp,
		})

		c.JSON(http.StatusOK, gin.H{"event": event})
	}
}

// getAnalyticsSummary provides summary statistics of events
// getAnalyticsSummary provides summary statistics of events with improved performance
func getAnalyticsSummary(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		timeframe := c.DefaultQuery("timeframe", "24h") // 24h, 7d, 30d
		eventType := c.Query("eventType")

		// Add context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 300*time.Second)
		defer cancel()

		// Check if we have results in cache (pseudocode - implement actual caching)
		// var cachedSummary interface{}
		// if found, err := cache.Get(cacheKey, &cachedSummary); err == nil {
		//     c.JSON(http.StatusOK, cachedSummary)
		//     return
		// }

		// Determine the interval parameter and prepare query arguments
		var interval string
		switch timeframe {
		case "7d":
			interval = "7 days"
		case "30d":
			interval = "30 days"
		default:
			interval = "24 hours"
		}

		// Use parameterized queries for better security and query planning
		var args []interface{}
		var whereClause string

		// Start with the interval condition that will use an index
		whereClause = "event_timestamp >= NOW() - $1::INTERVAL"
		args = append(args, interval)

		// Add event type filter if provided
		if eventType != "" {
			whereClause += " AND event_type = $2"
			args = append(args, eventType)
		}

		// Define the optimized query using simple aggregate functions
		// This avoids complex subqueries and unnecessary filtering
		query := fmt.Sprintf(`
            SELECT 
                COUNT(*) as total_events,
                COUNT(DISTINCT user_email) as unique_users,
                COUNT(DISTINCT session_id) as unique_sessions,
                COALESCE(AVG(duration_seconds), 0) as avg_duration,
                COUNT(*) FILTER (WHERE status = 'error') as error_count
            FROM user_analytics
            WHERE %s
        `, whereClause)

		var summary struct {
			TotalEvents    int     `db:"total_events" json:"totalEvents"`
			UniqueUsers    int     `db:"unique_users" json:"uniqueUsers"`
			UniqueSessions int     `db:"unique_sessions" json:"uniqueSessions"`
			AvgDuration    float64 `db:"avg_duration" json:"avgDuration"`
			ErrorCount     int     `db:"error_count" json:"errorCount"`
		}

		// Execute the query with proper context and parameters
		err := dbx.GetContext(ctx, &summary, query, args...)
		if err != nil {
			log.Printf("Error fetching analytics summary: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics summary"})
			return
		}

		// Prepare response
		response := gin.H{
			"summary":   summary,
			"timeframe": timeframe,
			"eventType": eventType,
		}

		// Store in cache for 5 minutes (pseudocode - implement actual caching)
		// cache.Set(cacheKey, response, 5*time.Minute)

		c.JSON(http.StatusOK, response)
	}
}
