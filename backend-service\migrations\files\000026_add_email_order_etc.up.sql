-- Add client_email column to orders table
ALTER TABLE orders ADD COLUMN client_email VARCHAR(255);
CREATE INDEX idx_orders_client_email ON orders(client_email);
COMMENT ON COLUMN orders.client_email IS 'Email address for guest users who make purchases without an account';

-- Add client_email column to invoices table
ALTER TABLE invoices ADD COLUMN client_email VARCHAR(255);
CREATE INDEX idx_invoices_client_email ON invoices(client_email);
COMMENT ON COLUMN invoices.client_email IS 'Email address copied from order for guest purchases';

-- Add client_email column to payments table
ALTER TABLE payments ADD COLUMN client_email VARCHAR(255);
CREATE INDEX idx_payments_client_email ON payments(client_email);
COMMENT ON COLUMN payments.client_email IS 'Email address copied from invoice for guest purchases';