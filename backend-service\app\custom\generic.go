package custom

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"
	"github.com/redis/go-redis/v9"
)

var sanitizer *bluemonday.Policy

// Password-related request structures
type VerifyPasswordRequest struct {
	Password string `json:"password" binding:"required"`
}

type UpdatePasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required"`
}

func init() {
	// Initialize the sanitizer with a strict policy
	sanitizer = bluemonday.StrictPolicy()
}

func RegisterRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	// V0 Routes
	v0Group := r.Group("/v0")
	{
		surveysGroup := v0Group.Group("/surveys")
		{
			surveysGroup.POST("", submitUserDemographics(dbx))
			surveysGroup.POST("/feedback", submitFeedbackSurvey(dbx))
			surveysGroup.GET("/check-email", checkEmailRecords(dbx))                  // New route under /v0/surveys
			surveysGroup.GET("/check-email/feedback", checkFeedbackEmailRecords(dbx)) // New route under /v0/surveys
		}

		examsGroup := v0Group.Group("/exams")
		{
			examsGroup.GET("/sessions", getExamSessions(dbx))
			examsGroup.GET("/leaderboard", getLeaderboard(dbx))
			examsGroup.GET("/leaderboard-targets", getTargetAggregates(dbx))
			examsGroup.GET("/categories", getCategories(dbx))
			examsGroup.GET("/exam-progress", getExamProgress(dbx))
			examsGroup.GET("/order-history", getOrderHistory(dbx))
		}

		paymentsGroup := v0Group.Group("/payments")
		{
			paymentsGroup.GET("/details", getOrderDetail(dbx))
		}

		usersGroup := v0Group.Group("/users")
		{
			usersGroup.POST("/:userId/verify-password", verifyPassword(dbx))
			usersGroup.PUT("/:userId/password", updatePassword(dbx))
		}

	}
}

func sanitizeInput(input string) string {
	return sanitizer.Sanitize(input)
}

func sanitizeMap(data map[string]interface{}) {
	for key, value := range data {
		switch v := value.(type) {
		case string:
			data[key] = sanitizeInput(v)
		case []interface{}:
			for i, item := range v {
				if str, ok := item.(string); ok {
					v[i] = sanitizeInput(str)
				}
			}
		}
	}
}

func submitUserDemographics(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Println("Starting submitUserDemographics function")

		var userData map[string]interface{}
		if err := c.BindJSON(&userData); err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(400, gin.H{"error": "Invalid request payload"})
			return
		}

		// Sanitize input
		sanitizeMap(userData)

		log.Printf("Received sanitized user data: %+v", userData)

		// Convert arrays to JSON strings for JSONB fields
		jsonbFields := []string{"interests", "preferredStudyMethods", "primaryDevices", "learningChallenges"}
		for _, field := range jsonbFields {
			if arr, ok := userData[field].([]interface{}); ok {
				jsonArr, err := json.Marshal(arr)
				if err != nil {
					log.Printf("Error marshaling JSONB field %s: %v", field, err)
					c.JSON(500, gin.H{"error": "Failed to process JSONB field"})
					return
				}
				userData[field] = string(jsonArr) // Store as string for JSONB fields
			}
		}

		// Convert ID fields to int64/BIGINT
		idFields := []string{"provinceId", "cityId", "districtId", "villageId", "educationLevelId", "programStudyId"}
		for _, field := range idFields {
			if val, exists := userData[field]; exists && val != nil {
				// Convert to int64 based on type
				switch v := val.(type) {
				case string:
					if v == "" {
						userData[field] = nil // Convert empty string to nil
					} else {
						int64Val, err := strconv.ParseInt(v, 10, 64)
						if err != nil {
							log.Printf("Error converting %s to int64: %v", field, err)
							c.JSON(400, gin.H{"error": fmt.Sprintf("Invalid %s value", field)})
							return
						}
						userData[field] = int64Val
					}
				case float64:
					// JSON numbers are parsed as float64, convert to int64
					userData[field] = int64(v)
				case int:
					// Convert int to int64
					userData[field] = int64(v)
				case nil:
					// Keep as nil
				default:
					// Log unexpected types
					log.Printf("Warning: Unexpected type for %s: %T", field, v)
					userData[field] = nil
				}
			}
		}

		// Column name mapping including both ID and name fields
		columnMapping := map[string]string{
			// ID fields (original)
			"provinceId":       "province_id",
			"cityId":           "city_id",
			"districtId":       "district_id",
			"villageId":        "village_id",
			"educationLevelId": "education_level_id",
			"programStudyId":   "program_study_id",

			// Name fields (new)
			"provinceName":       "province_name",
			"cityName":           "city_name",
			"districtName":       "district_name",
			"villageName":        "village_name",
			"educationLevelName": "education_level_name",
			"programStudyName":   "program_study_name",

			// Other fields (unchanged)
			"birthDate":             "birth_date",
			"lastOccupation":        "last_occupation",
			"interests":             "interests",
			"mainPurpose":           "main_purpose",
			"gender":                "gender",
			"phoneNumber":           "phone_number",
			"preferredStudyMethods": "preferred_study_methods",
			"weeklyStudyTime":       "weekly_study_time",
			"primaryDevices":        "primary_devices",
			"learningStyle":         "learning_style",
			"studyBudget":           "study_budget",
			"email":                 "email",
			"targetJabatan":         "target_jabatan",
			"targetInstitution":     "target_institution",
			"targetScore":           "target_score",
			"learningChallenges":    "learning_challenges",

			"targetUniversity": "target_university",
			"targetMajor":      "target_major",
		}

		// Build the query dynamically
		columns := make([]string, 0, len(userData))
		values := make([]interface{}, 0, len(userData))
		placeholders := make([]string, 0, len(userData))
		returningColumns := []string{"id", "created_at"}

		i := 1
		for key, value := range userData {
			if mappedName, exists := columnMapping[key]; exists {
				// Skip nil values to let database use default or NULL
				if value != nil {
					columns = append(columns, mappedName)
					values = append(values, value)
					placeholders = append(placeholders, fmt.Sprintf("$%d", i))
					i++
				}
			} else {
				log.Printf("Warning: Unmapped field %s", key)
			}
		}

		// Ensure we have at least one column to insert
		if len(columns) == 0 {
			log.Printf("Error: No valid columns to insert")
			c.JSON(400, gin.H{"error": "No valid data to insert"})
			return
		}

		query := "INSERT INTO user_demographics (" +
			strings.Join(columns, ", ") +
			") VALUES (" +
			strings.Join(placeholders, ", ") +
			") RETURNING " + strings.Join(returningColumns, ", ")

		log.Printf("Generated SQL query: %s", query)
		log.Printf("Query values: %+v", values)

		var id int
		var createdAt time.Time
		err := dbx.QueryRow(query, values...).Scan(&id, &createdAt)
		if err != nil {
			log.Printf("Error executing SQL query: %v", err)
			c.JSON(500, gin.H{"error": "Failed to insert user demographics"})
			return
		}

		log.Println("User demographics submitted successfully")
		c.JSON(200, gin.H{
			"message":   "User demographics submitted successfully",
			"id":        id,
			"timestamp": createdAt.Format(time.RFC3339),
		})
	}
}

func checkEmailRecords(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			c.JSON(400, gin.H{"error": "Email parameter is required"})
			return
		}

		// Sanitize the email input
		email = sanitizeInput(email)

		var count int
		query := "SELECT COUNT(*) FROM user_demographics WHERE email = $1"
		err := dbx.Get(&count, query, email)
		if err != nil {
			log.Printf("Error checking email records: %v", err)
			c.JSON(500, gin.H{"error": "Failed to check email records"})
			return
		}

		response := gin.H{
			"email": email,
			"count": count,
		}

		if count == 0 {
			response["message"] = "No records found for this email"
		} else {
			response["message"] = fmt.Sprintf("Found %d record(s) for this email", count)
		}

		c.JSON(200, response)
	}
}

type FeedbackSurvey struct {
	NPS                    string   `json:"nps"`
	CSAT                   string   `json:"csat"`
	CES                    string   `json:"ces"`
	FiturFavorit           []string `json:"fiturFavorit"`
	Perbaikan              string   `json:"perbaikan"`
	PerbaikanLainnya       *string  `json:"perbaikanLainnya,omitempty"`
	FrekuensiPenggunaan    string   `json:"frekuensiPenggunaan"`
	SumberInformasi        string   `json:"sumberInformasi"`
	SumberInformasiLainnya *string  `json:"sumberInformasiLainnya,omitempty"`
	UmpanBalik             string   `json:"umpanBalik"`
	UserEmail              string   `json:"userEmail"`
}

func submitFeedbackSurvey(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Println("Starting submitFeedbackSurvey function")

		var data FeedbackSurvey
		if err := c.BindJSON(&data); err != nil {
			log.Printf("Error binding JSON: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
			return
		}

		// Sanitize input
		data.NPS = sanitizeInput(data.NPS)
		data.CSAT = sanitizeInput(data.CSAT)
		data.CES = sanitizeInput(data.CES)
		data.Perbaikan = sanitizeInput(data.Perbaikan)
		data.FrekuensiPenggunaan = sanitizeInput(data.FrekuensiPenggunaan)
		data.SumberInformasi = sanitizeInput(data.SumberInformasi)
		data.UmpanBalik = sanitizeInput(data.UmpanBalik)
		data.UserEmail = sanitizeInput(data.UserEmail)

		// Sanitize FiturFavorit array
		for i, fitur := range data.FiturFavorit {
			data.FiturFavorit[i] = sanitizeInput(fitur)
		}

		if data.PerbaikanLainnya != nil {
			sanitized := sanitizeInput(*data.PerbaikanLainnya)
			data.PerbaikanLainnya = &sanitized
		}

		if data.SumberInformasiLainnya != nil {
			sanitized := sanitizeInput(*data.SumberInformasiLainnya)
			data.SumberInformasiLainnya = &sanitized
		}

		log.Printf("Received sanitized feedback survey data: %+v", data)

		// Create a JSON array string for FiturFavorit
		fiturFavoritJSON := fmt.Sprintf("[%s]", strings.Join(quoteStrings(data.FiturFavorit), ","))

		// Insert data into database
		_, err := dbx.Exec(`
			INSERT INTO feedback_surveys (
				email, nps, csat, ces, fitur_favorit, perbaikan, perbaikan_lainnya, 
				frekuensi_penggunaan, sumber_informasi, sumber_informasi_lainnya, umpan_balik
			) VALUES ($1, $2, $3, $4::jsonb, $5, $6, $7, $8, $9, $10, $11)`,
			data.UserEmail, data.NPS, data.CSAT, data.CES, fiturFavoritJSON, data.Perbaikan, data.PerbaikanLainnya,
			data.FrekuensiPenggunaan, data.SumberInformasi, data.SumberInformasiLainnya, data.UmpanBalik,
		)
		if err != nil {
			log.Printf("Error executing SQL query: %v", err)
			log.Printf("Attempted to insert fitur_favorit as: %s", fiturFavoritJSON)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert feedback survey data"})
			return
		}

		log.Println("Feedback survey submitted successfully")
		c.JSON(http.StatusCreated, gin.H{"message": "Feedback survey submitted successfully"})
	}
}

// quoteStrings takes a slice of strings and returns a new slice with each string quoted
func quoteStrings(strings []string) []string {
	quoted := make([]string, len(strings))
	for i, s := range strings {
		quoted[i] = fmt.Sprintf("%q", s)
	}
	return quoted
}

func checkFeedbackEmailRecords(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			c.JSON(400, gin.H{"error": "Email parameter is required"})
			return
		}

		// Sanitize the email input
		email = sanitizeInput(email)

		var count int
		query := "SELECT COUNT(*) FROM feedback_surveys WHERE email = $1"
		err := dbx.Get(&count, query, email)
		if err != nil {
			log.Printf("Error checking email records: %v", err)
			c.JSON(500, gin.H{"error": "Failed to check email records"})
			return
		}

		response := gin.H{
			"email": email,
			"count": count,
		}

		if count == 0 {
			response["message"] = "No records found for this email"
		} else {
			response["message"] = fmt.Sprintf("Found %d record(s) for this email", count)
		}

		c.JSON(200, response)
	}
}

// SubjectScore holds score information for a specific subject
type SubjectScore struct {
	Score   float64 `json:"score"`   // Score for this subject
	Correct int     `json:"correct"` // Number of correct answers
	Total   int     `json:"total"`   // Total number of questions
}

type ExamSessionData struct {
	SessionID       *string                 `db:"session_id" json:"sessionId"`
	UniqueSessionID *string                 `db:"unique_session_id" json:"uniqueSessionId"`
	UserID          *string                 `db:"user_id" json:"userId"`
	Username        *string                 `db:"username" json:"username"`
	Email           *string                 `db:"email" json:"email"`
	ExamID          *string                 `db:"exam_id" json:"examId"`
	ExamName        *string                 `db:"exam_name" json:"examName"`
	ExamSubname     *string                 `db:"exam_subname" json:"examSubname"`
	SessionType     *string                 `db:"session_type" json:"sessionType"`
	SessionStatus   *string                 `db:"session_status" json:"sessionStatus"`
	StartTime       *string                 `db:"start_time" json:"startTime"`
	EndTime         *string                 `db:"end_time" json:"endTime,omitempty"`
	TotalQuestions  *int                    `db:"total_questions" json:"totalQuestions"`
	CorrectAnswers  *int                    `db:"correct_answers" json:"correctAnswers"`
	Score           *float64                `db:"score" json:"score"`
	Accuracy        *float64                `db:"accuracy" json:"accuracy"`
	Categories      *string                 `db:"categories" json:"categories"`
	Tags            *string                 `db:"tags" json:"tags"`
	ExamType        *string                 `db:"exam_type" json:"examType"`
	Subject         *string                 `db:"subject" json:"subject"`
	MetadataScores  *string                 `db:"-" json:"-"`      // Used internally for processing but not exposed in JSON
	SubjectScores   map[string]SubjectScore `json:"subjectScores"` // Dynamic map of subject scores
}

func getExamSessions(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			c.JSON(400, gin.H{"error": "Email parameter is required"})
			return
		}

		// Sanitize the email input
		email = sanitizeInput(email)

		// Load exam configuration for later use
		config, err := loadExamConfiguration()
		if err != nil {
			log.Printf("Error loading exam configuration: %v", err)
			c.JSON(500, gin.H{"error": "Failed to load exam configuration"})
			return
		}

		query := `
		SELECT 
			es.id AS session_id,
			es.session_id AS unique_session_id,
			es.user_id,
			u.username,
			u.email,
			es.exam_id,
			es.subject,
			ae.name AS exam_name,
			ae.subname AS exam_subname,
			es.type AS session_type,
			es.status AS session_status,
			es.start_time,
			es.end_time,
			esc.total_questions,
			esc.correct_answers,
			esc.score,
			esc.accuracy,
			esc.metadata_scores,
			COALESCE(esc.metadata_scores->>'exam_type', 'Nothing bro') AS exam_type,
			array_to_string(array_agg(DISTINCT c.name), ', ') AS categories,
			array_to_string(array_agg(DISTINCT t.name), ', ') AS tags
		FROM 
			exam_sessions es
		JOIN 
			users u ON es.user_id = u.id
		JOIN 
			available_exams ae ON es.exam_id = ae.id
		LEFT JOIN 
			exam_scores esc ON es.session_id = esc.session_id
		LEFT JOIN 
			exam_categories ec ON ae.id = ec.exam_id
		LEFT JOIN 
			categories c ON ec.category_id = c.id
		LEFT JOIN 
			exam_tags et ON ae.id = et.exam_id
		LEFT JOIN 
			tags t ON et.tag_id = t.id
		WHERE 
			u.email = $1
		GROUP BY 
			es.id, es.session_id, es.user_id, u.username, u.email, es.exam_id, 
			ae.name, ae.subname, es.type, es.status, es.start_time, es.end_time, 
			esc.total_questions, esc.correct_answers, esc.score, esc.accuracy, esc.metadata_scores
		ORDER BY 
			es.start_time DESC`

		rows, err := dbx.Queryx(query, email)
		if err != nil {
			log.Printf("Error fetching exam sessions: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch exam sessions"})
			return
		}
		defer rows.Close()

		var sessions []ExamSessionData
		for rows.Next() {
			// First scan into a map to capture the metadata_scores
			result := make(map[string]interface{})
			if err := rows.MapScan(result); err != nil {
				log.Printf("Error scanning row: %v", err)
				continue
			}

			// Create a session object and populate the standard fields
			var session ExamSessionData
			session.SubjectScores = make(map[string]SubjectScore)

			// Extract standard fields from the result map
			extractSessionData(&session, result)

			// Process the metadata_scores field to extract subject scores
			if metadataBytes, ok := result["metadata_scores"].([]byte); ok {
				// Convert []byte to string for handling in JSON
				// metadataStr := string(metadataBytes)

				// Parse the metadata JSON
				var metadata map[string]interface{}
				if err := json.Unmarshal(metadataBytes, &metadata); err != nil {
					log.Printf("Error unmarshaling metadata: %v", err)
				} else {
					// Process the metadata to extract subject scores
					processMetadataScores(&session, metadata, config)
				}
			}

			sessions = append(sessions, session)
		}

		if len(sessions) == 0 {
			c.JSON(200, gin.H{"message": "No exam sessions found for this email", "sessions": []ExamSessionData{}})
			return
		}

		c.JSON(200, gin.H{"message": "Exam sessions retrieved successfully", "sessions": sessions})
	}
}

func extractSessionData(session *ExamSessionData, result map[string]interface{}) {
	// Helper function to safely extract string pointer fields
	extractStringPtr := func(key string) *string {
		if v, ok := result[key]; ok && v != nil {
			str := safeStringConversion(v)
			return &str
		}
		return nil
	}

	// Helper function to safely extract int pointer fields
	extractIntPtr := func(key string) *int {
		if v, ok := result[key]; ok && v != nil {
			switch val := v.(type) {
			case int64:
				i := int(val)
				return &i
			case float64:
				i := int(val)
				return &i
			case []byte:
				if i, err := strconv.Atoi(string(val)); err == nil {
					return &i
				}
			case string:
				if i, err := strconv.Atoi(val); err == nil {
					return &i
				}
			}
		}
		return nil
	}

	// Helper function to safely extract float64 pointer fields
	extractFloat64Ptr := func(key string) *float64 {
		if v, ok := result[key]; ok && v != nil {
			switch val := v.(type) {
			case float64:
				return &val
			case int64:
				f := float64(val)
				return &f
			case []byte:
				if f, err := strconv.ParseFloat(string(val), 64); err == nil {
					return &f
				}
			case string:
				if f, err := strconv.ParseFloat(val, 64); err == nil {
					return &f
				}
			}
		}
		return nil
	}

	// Extract all fields from the result map
	session.SessionID = extractStringPtr("session_id")
	session.UniqueSessionID = extractStringPtr("unique_session_id")
	session.UserID = extractStringPtr("user_id")
	session.Username = extractStringPtr("username")
	session.Email = extractStringPtr("email")
	session.ExamID = extractStringPtr("exam_id")
	session.ExamName = extractStringPtr("exam_name")
	session.ExamSubname = extractStringPtr("exam_subname")
	session.SessionType = extractStringPtr("session_type")
	session.SessionStatus = extractStringPtr("session_status")
	session.StartTime = extractStringPtr("start_time")
	session.EndTime = extractStringPtr("end_time")
	session.TotalQuestions = extractIntPtr("total_questions")
	session.CorrectAnswers = extractIntPtr("correct_answers")
	session.Score = extractFloat64Ptr("score")
	session.Accuracy = extractFloat64Ptr("accuracy")
	session.Categories = extractStringPtr("categories")
	session.Tags = extractStringPtr("tags")
	session.ExamType = extractStringPtr("exam_type")
	session.Subject = extractStringPtr("subject")
}

// processMetadataScores extracts subject-specific scores from metadata
func processMetadataScores(session *ExamSessionData, metadata map[string]interface{}, config *ExamConfiguration) {
	// Determine the exam type from metadata or fallback to default
	examTypeVal, hasExamType := metadata["exam_type"]
	examType := ""

	if hasExamType {
		if et, ok := examTypeVal.(string); ok {
			examType = strings.ToUpper(et)
		}
	}

	// If we have an exam type, process the corresponding subjects
	if examTypeConfig, exists := config.ExamTypes[examType]; exists {
		// For each subject in this exam type, extract the score data
		for _, subject := range examTypeConfig.Subjects {
			key := subject.Key
			subjectScore := SubjectScore{}

			// Extract score
			scoreKey := key + "_score"
			if scoreVal, ok := metadata[scoreKey]; ok {
				subjectScore.Score = parseFloat64(scoreVal)
			}

			// Extract correct answers
			if correctVal, ok := metadata[key]; ok {
				subjectScore.Correct = parseInt(correctVal)
			}

			// Extract total questions
			totalKey := key + "_total"
			if totalVal, ok := metadata[totalKey]; ok {
				subjectScore.Total = parseInt(totalVal)
			}

			// Add this subject score to the map
			session.SubjectScores[key] = subjectScore
		}
	} else {
		// If exam type doesn't match any config, try generic extraction
		// This handles cases where we don't have a specific exam type or as a fallback
		for _, examTypeConfig := range config.ExamTypes {
			for _, subject := range examTypeConfig.Subjects {
				key := subject.Key
				scoreKey := key + "_score"

				// Only add this subject if it has a score in the metadata
				if scoreVal, ok := metadata[scoreKey]; ok {
					subjectScore := SubjectScore{
						Score:   parseFloat64(scoreVal),
						Correct: parseInt(metadata[key]),
						Total:   parseInt(metadata[key+"_total"]),
					}
					session.SubjectScores[key] = subjectScore
				}
			}
		}

		// Backward compatibility: check for legacy TWK/TIU/TKP fields
		legacyFields := []string{"twk", "tiu", "tkp"}
		for _, field := range legacyFields {
			scoreKey := field + "_score"
			if scoreVal, ok := metadata[scoreKey]; ok && !hasSubjectScore(session, field) {
				subjectScore := SubjectScore{
					Score:   parseFloat64(scoreVal),
					Correct: parseInt(metadata[field]),
					Total:   parseInt(metadata[field+"_total"]),
				}
				session.SubjectScores[field] = subjectScore
			}
		}
	}
}

// hasSubjectScore checks if a subject score already exists
func hasSubjectScore(session *ExamSessionData, key string) bool {
	_, exists := session.SubjectScores[key]
	return exists
}

// parseFloat64 safely converts a value to float64
func parseFloat64(v interface{}) float64 {
	if v == nil {
		return 0
	}

	switch val := v.(type) {
	case float64:
		return val
	case int64:
		return float64(val)
	case int:
		return float64(val)
	case string:
		if f, err := strconv.ParseFloat(val, 64); err == nil {
			return f
		}
	case []byte:
		if f, err := strconv.ParseFloat(string(val), 64); err == nil {
			return f
		}
	}
	return 0
}

// parseInt safely converts a value to int
func parseInt(v interface{}) int {
	if v == nil {
		return 0
	}

	switch val := v.(type) {
	case int:
		return val
	case int64:
		return int(val)
	case float64:
		return int(val)
	case string:
		if i, err := strconv.Atoi(val); err == nil {
			return i
		}
	case []byte:
		if i, err := strconv.Atoi(string(val)); err == nil {
			return i
		}
	}
	return 0
}

func safeStringConversion(v interface{}) string {
	if v == nil {
		return ""
	}

	// Debug the incoming value
	log.Printf("safeStringConversion: converting %T with value %v", v, v)

	switch val := v.(type) {
	case string:
		return val
	case []byte:
		return string(val)
	case int64:
		return strconv.FormatInt(val, 10)
	case float64:
		return strconv.FormatFloat(val, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(val)
	case time.Time:
		return val.Format(time.RFC3339)
	case nil:
		return ""
	case sql.NullString:
		if val.Valid {
			return val.String
		}
		return ""
	default:
		// For other types, try JSON marshaling
		data, err := json.Marshal(v)
		if err != nil {
			log.Printf("Error converting to string: %v", err)
			return fmt.Sprintf("%v", v)
		}
		return string(data)
	}
}

func getLeaderboard(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		examID := c.Query("examId")
		subject := c.Query("subject")
		sessionType := c.Query("type")
		categoryID := c.Query("categoryId")

		if examID == "" && subject == "" {
			c.JSON(400, gin.H{"error": "Either examId or subject parameter is required"})
			return
		}

		// Sanitize inputs
		examID = sanitizeInput(examID)
		subject = sanitizeInput(subject)
		sessionType = sanitizeInput(sessionType)
		categoryID = sanitizeInput(categoryID)

		if sessionType != "" && sessionType != "PRACTICE" && sessionType != "EXAM" {
			c.JSON(400, gin.H{"error": "Invalid session type. Must be either PRACTICE or EXAM"})
			return
		}

		// Load the exam configuration
		config, err := loadExamConfiguration()
		if err != nil {
			log.Printf("Error loading exam configuration: %v", err)
			c.JSON(500, gin.H{"error": "Failed to load exam configuration"})
			return
		}

		// Create a pre-filtered table for exams by category
		baseQuery := `
        WITH filtered_exams AS (
            SELECT DISTINCT ae.id
            FROM available_exams ae
            WHERE ($1 = '' OR ae.id = $1)
            AND ($4 = '' OR EXISTS (
                SELECT 1 FROM exam_categories ec 
                WHERE ec.exam_id = ae.id AND ec.category_id = $4
            ))
        ),
        user_best_attempt AS (
            SELECT DISTINCT ON (exs.user_id) 
                exs.user_id,
                u.username,
                u.email,
                es.score as total_score,
                es.correct_answers as total_correct,
                es.total_questions,
                es.created_at AS completed_at,
                exs.exam_id,
                exs.subject,
                ae.name AS exam_name,
                exs.type as session_type,
                es.metadata_scores,
                exs.start_time,
                exs.end_time,
                EXTRACT(EPOCH FROM (exs.end_time - exs.start_time))::integer as elapsed_seconds,
                ud.target_jabatan,
                ud.target_institution,
                ud.target_university,
                ud.target_major,
                ARRAY_AGG(DISTINCT c.id) AS category_ids,
                ARRAY_AGG(DISTINCT c.name) AS category_names
            FROM 
                exam_sessions exs
                JOIN filtered_exams fe ON exs.exam_id = fe.id
                JOIN exam_scores es ON es.session_id = exs.session_id
                JOIN users u ON exs.user_id = u.id
                JOIN available_exams ae ON exs.exam_id = ae.id
                LEFT JOIN user_demographics ud ON LOWER(u.email) = LOWER(ud.email)
                LEFT JOIN exam_categories ec ON exs.exam_id = ec.exam_id
                LEFT JOIN categories c ON ec.category_id = c.id
            WHERE 
                ($1 != '' OR ($2 != '' AND exs.subject = $2)) -- Apply subject filter only if examId is empty
                AND exs.status = 'COMPLETED'
                AND ($3 = '' OR exs.type::text = $3) -- Fix: Cast type to text instead of casting empty string to enum
            GROUP BY
                exs.user_id,
                u.username,
                u.email,
                es.score,
                es.correct_answers,
                es.total_questions,
                es.created_at,
                exs.exam_id,
                exs.subject,
                ae.name,
                exs.type,
                es.metadata_scores,
                exs.start_time,
                exs.end_time,
                elapsed_seconds,
                ud.target_jabatan,
                ud.target_institution,
                ud.target_university,
                ud.target_major
            ORDER BY 
                exs.user_id,
                es.score DESC,
                es.created_at ASC
        )`

		// Build dynamic fields for ranked_scores with fixed table alias
		var subjectFields []string
		for _, examType := range config.ExamTypes {
			for _, subject := range examType.Subjects {
				key := subject.Key
				// Score field with table alias to avoid ambiguity
				subjectFields = append(subjectFields,
					fmt.Sprintf("COALESCE((r.metadata_scores->>'%s_score')::float, 0) as %s_score", key, key))
				// Correct answers field
				subjectFields = append(subjectFields,
					fmt.Sprintf("COALESCE((r.metadata_scores->>'%s')::int, 0) as %s_correct", key, key))
				// Total questions field
				subjectFields = append(subjectFields,
					fmt.Sprintf("COALESCE((r.metadata_scores->>'%s_total')::int, 0) as %s_total", key, key))
			}
		}

		// Build ranked_scores CTE with table alias
		rankedScoresQuery := fmt.Sprintf(`
        ranked_scores AS (
            SELECT 
                r.user_id,
                r.username,
                r.email,
                r.total_score,
                r.total_correct,
                r.total_questions,
                r.completed_at,
                r.exam_id,
                r.subject,
                r.exam_name,
                r.session_type,
                %s,
                r.start_time,
                r.end_time,
                r.elapsed_seconds,
                r.target_jabatan,
                r.target_institution,
                r.target_university,
                r.target_major,
                r.category_ids,
                r.category_names,
                r.metadata_scores,
                ROW_NUMBER() OVER (ORDER BY r.total_score DESC, r.completed_at ASC) AS rank
            FROM 
                user_best_attempt r
        )`, strings.Join(subjectFields, ",\n"))

		// Select fields for final query
		var selectFields []string
		selectFields = append(selectFields,
			"rank", "user_id", "username", "total_score", "total_correct", "total_questions",
			"completed_at::text", "exam_id", "exam_name", "subject", "session_type")

		// Add dynamic subject fields to select
		for _, examType := range config.ExamTypes {
			for _, subject := range examType.Subjects {
				key := subject.Key
				selectFields = append(selectFields, fmt.Sprintf("%s_score", key))
				selectFields = append(selectFields, fmt.Sprintf("%s_correct", key))
				selectFields = append(selectFields, fmt.Sprintf("%s_total", key))
			}
		}

		// Add remaining standard fields
		selectFields = append(selectFields,
			"start_time::text", "end_time::text", "elapsed_seconds",
			`CONCAT(
				FLOOR(elapsed_seconds/3600)::text, ' hours ',
				FLOOR((elapsed_seconds%3600)/60)::text, ' minutes ',
				(elapsed_seconds%60)::text, ' seconds'
			) as elapsed_time_formatted`,
			"COALESCE(target_jabatan, '') as target_jabatan",
			"COALESCE(target_institution, '') as target_institution",
			"COALESCE(target_university, '') as target_university",
			"COALESCE(target_major, '') as target_major",
			"category_ids",
			"category_names",
			"metadata_scores::text as metadata_scores_json")

		// Build final query
		finalQuery := fmt.Sprintf(`
        %s,
        %s
        SELECT 
            %s
        FROM 
            ranked_scores
        ORDER BY 
            rank ASC`, baseQuery, rankedScoresQuery, strings.Join(selectFields, ",\n"))

		// Log query parameters for debugging
		log.Printf("Leaderboard query params: examId=%s, subject=%s, type=%s, categoryId=%s",
			examID, subject, sessionType, categoryID)

		// Execute the query with the added categoryId parameter
		rows, err := dbx.Queryx(finalQuery, examID, subject, sessionType, categoryID)
		if err != nil {
			log.Printf("Error fetching leaderboard: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch leaderboard"})
			return
		}
		defer rows.Close()

		// Define a dynamic type that includes metadata_scores and categories
		type LeaderboardEntry struct {
			Rank                 int                               `db:"rank" json:"rank"`
			UserID               string                            `db:"user_id" json:"userId"`
			Username             string                            `db:"username" json:"username"`
			TotalScore           float64                           `db:"total_score" json:"totalScore"`
			TotalCorrect         int                               `db:"total_correct" json:"totalCorrect"`
			TotalQuestions       int                               `db:"total_questions" json:"totalQuestions"`
			CompletedAt          string                            `db:"completed_at" json:"completedAt"`
			ExamID               string                            `db:"exam_id" json:"examId"`
			ExamName             string                            `db:"exam_name" json:"examName"`
			Subject              string                            `db:"subject" json:"subject"`
			SessionType          string                            `db:"session_type" json:"sessionType"`
			StartTime            string                            `db:"start_time" json:"startTime"`
			EndTime              string                            `db:"end_time" json:"endTime"`
			ElapsedSeconds       int                               `db:"elapsed_seconds" json:"elapsedSeconds"`
			ElapsedTimeFormatted string                            `db:"elapsed_time_formatted" json:"elapsedTimeFormatted"`
			TargetJabatan        string                            `db:"target_jabatan" json:"targetJabatan"`
			TargetInstitution    string                            `db:"target_institution" json:"targetInstitution"`
			TargetUniversity     string                            `db:"target_university" json:"targetUniversity"`
			TargetMajor          string                            `db:"target_major" json:"targetMajor"`
			CategoryIDs          []string                          `db:"category_ids" json:"categoryIds"`
			CategoryNames        []string                          `db:"category_names" json:"categoryNames"`
			MetadataScoresJSON   *string                           `db:"metadata_scores_json" json:"-"`
			SubjectScores        map[string]map[string]interface{} `json:"subjectScores"`
		}

		var leaderboard []LeaderboardEntry
		for rows.Next() {
			var entry LeaderboardEntry
			entry.SubjectScores = make(map[string]map[string]interface{})

			// Create a map to scan all columns
			result := make(map[string]interface{})
			if err := rows.MapScan(result); err != nil {
				log.Printf("Error scanning row: %v", err)
				continue
			}

			// Transfer standard fields
			if v, ok := result["rank"]; ok {
				if val, ok := v.(int64); ok {
					entry.Rank = int(val)
				}
			}
			if v, ok := result["user_id"]; ok && v != nil {
				entry.UserID = safeStringConversion(v)
			}
			if v, ok := result["username"]; ok && v != nil {
				entry.Username = safeStringConversion(v)
			}
			if v, ok := result["total_score"]; ok {
				if val, ok := v.(float64); ok {
					entry.TotalScore = val
				}
			}
			if v, ok := result["total_correct"]; ok {
				if val, ok := v.(int64); ok {
					entry.TotalCorrect = int(val)
				}
			}
			if v, ok := result["total_questions"]; ok {
				if val, ok := v.(int64); ok {
					entry.TotalQuestions = int(val)
				}
			}
			if v, ok := result["completed_at"]; ok && v != nil {
				entry.CompletedAt = safeStringConversion(v)
			}
			if v, ok := result["exam_id"]; ok && v != nil {
				entry.ExamID = safeStringConversion(v)
			}
			if v, ok := result["exam_name"]; ok && v != nil {
				entry.ExamName = safeStringConversion(v)
			}
			if v, ok := result["subject"]; ok && v != nil {
				entry.Subject = safeStringConversion(v)
			}
			if v, ok := result["session_type"]; ok && v != nil {
				entry.SessionType = safeStringConversion(v)
			}
			if v, ok := result["start_time"]; ok && v != nil {
				entry.StartTime = safeStringConversion(v)
			}
			if v, ok := result["end_time"]; ok && v != nil {
				entry.EndTime = safeStringConversion(v)
			}
			if v, ok := result["elapsed_seconds"]; ok {
				if val, ok := v.(int64); ok {
					entry.ElapsedSeconds = int(val)
				}
			}
			if v, ok := result["elapsed_time_formatted"]; ok && v != nil {
				entry.ElapsedTimeFormatted = safeStringConversion(v)
			}
			if v, ok := result["target_jabatan"]; ok && v != nil {
				entry.TargetJabatan = safeStringConversion(v)
			}
			if v, ok := result["target_institution"]; ok && v != nil {
				entry.TargetInstitution = safeStringConversion(v)
			}
			if v, ok := result["target_university"]; ok && v != nil {
				entry.TargetUniversity = safeStringConversion(v)
			}
			if v, ok := result["target_major"]; ok && v != nil {
				entry.TargetMajor = safeStringConversion(v)
			}

			// Process category IDs
			if v, ok := result["category_ids"]; ok && v != nil {
				// Convert the array to []string
				var categoryIDs []string
				switch val := v.(type) {
				case []byte:
					// This is often the case with PostgreSQL arrays
					// Format is typically {id1,id2,id3}
					str := string(val)
					str = strings.Trim(str, "{}")
					if str != "" {
						categoryIDs = strings.Split(str, ",")
					}
				case []interface{}:
					// Convert each element to string
					for _, id := range val {
						categoryIDs = append(categoryIDs, safeStringConversion(id))
					}
				case string:
					// May already be a string representation
					str := strings.Trim(val, "{}")
					if str != "" {
						categoryIDs = strings.Split(str, ",")
					}
				}
				entry.CategoryIDs = categoryIDs
			}

			// Process category names
			if v, ok := result["category_names"]; ok && v != nil {
				// Convert the array to []string
				var categoryNames []string
				switch val := v.(type) {
				case []byte:
					// This is often the case with PostgreSQL arrays
					// Format is typically {name1,name2,name3}
					str := string(val)
					str = strings.Trim(str, "{}")
					if str != "" {
						categoryNames = strings.Split(str, ",")
					}
				case []interface{}:
					// Convert each element to string
					for _, name := range val {
						categoryNames = append(categoryNames, safeStringConversion(name))
					}
				case string:
					// May already be a string representation
					str := strings.Trim(val, "{}")
					if str != "" {
						categoryNames = strings.Split(str, ",")
					}
				}
				entry.CategoryNames = categoryNames
			}

			if v, ok := result["metadata_scores_json"]; ok && v != nil {
				metadataStr := safeStringConversion(v)
				entry.MetadataScoresJSON = &metadataStr

				// Parse metadata to extract subject scores
				var metadata map[string]interface{}
				if err := json.Unmarshal([]byte(metadataStr), &metadata); err != nil {
					log.Printf("Error unmarshaling metadata: %v", err)
				} else {
					// Check for exam_type first, fallback to trial_type for backwards compatibility
					examTypeVal, hasExamType := metadata["exam_type"]

					var examType string
					if hasExamType {
						examType, _ = examTypeVal.(string)
					}

					// Find the exam type configuration based on exam_type
					examTypeConfig, exists := config.ExamTypes[strings.ToUpper(examType)]
					if exists {
						// Process each subject for this exam type
						for _, subject := range examTypeConfig.Subjects {
							key := subject.Key
							subjectData := make(map[string]interface{})

							// Extract score
							if v, ok := result[key+"_score"]; ok {
								subjectData["score"] = v
							}

							// Extract correct answers
							if v, ok := result[key+"_correct"]; ok {
								subjectData["correct"] = v
							}

							// Extract total questions
							if v, ok := result[key+"_total"]; ok {
								subjectData["total"] = v
							}

							entry.SubjectScores[key] = subjectData
						}
					}
				}
			}

			leaderboard = append(leaderboard, entry)
		}

		if len(leaderboard) == 0 {
			message := "No leaderboard entries found"
			if examID != "" {
				message += " for this exam"
			} else {
				message += " for this subject"
			}
			if sessionType != "" {
				message += fmt.Sprintf(" (%s sessions)", sessionType)
			}
			if categoryID != "" {
				message += " with the selected category"
			}
			c.JSON(200, gin.H{
				"message":     message,
				"leaderboard": []LeaderboardEntry{},
			})
			return
		}

		responseType := "exam"
		if examID == "" {
			responseType = "subject"
		}

		// Also fetch all categories for frontend categorization
		categoriesQuery := `
		SELECT 
			c.id as value,
			c.name as label,
			c.image_url as "imageUrl"
		FROM 
			categories c
		ORDER BY 
			c.name ASC`

		type CategoryOption struct {
			Value    string  `json:"value" db:"value"`
			Label    string  `json:"label" db:"label"`
			ImageURL *string `json:"imageUrl" db:"imageUrl"`
		}

		var categories []CategoryOption
		err = dbx.Select(&categories, categoriesQuery)
		if err != nil {
			log.Printf("Error fetching categories: %v", err)
			// We'll continue even if categories fail since the main leaderboard data was fetched
		}

		c.JSON(200, gin.H{
			"message":     "Leaderboard retrieved successfully",
			"leaderboard": leaderboard,
			"type":        responseType,
			"categories":  categories,
		})
	}
}

func getTargetAggregates(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Query for positions
		positionsQuery := `
		WITH targets AS (
			SELECT NULLIF(TRIM(target_jabatan), '') as target_jabatan
			FROM user_demographics
			WHERE 
				target_jabatan IS NOT NULL 
				AND TRIM(target_jabatan) != ''
		)
		SELECT 
			target_jabatan as value,
			target_jabatan as label,
			COUNT(*) as count
		FROM targets
		GROUP BY target_jabatan
		ORDER BY COUNT(*) DESC, target_jabatan`

		// Query for institutions
		institutionsQuery := `
		WITH targets AS (
			SELECT NULLIF(TRIM(target_institution), '') as target_institution
			FROM user_demographics
			WHERE 
				target_institution IS NOT NULL 
				AND TRIM(target_institution) != ''
		)
		SELECT 
			target_institution as value,
			target_institution as label,
			COUNT(*) as count
		FROM targets
		GROUP BY target_institution
		ORDER BY COUNT(*) DESC, target_institution`

		// Query for target universities
		universitiesQuery := `
		WITH targets AS (
			SELECT NULLIF(TRIM(target_university), '') as target_university
			FROM user_demographics
			WHERE 
				target_university IS NOT NULL 
				AND TRIM(target_university) != ''
		)
		SELECT 
			target_university as value,
			target_university as label,
			COUNT(*) as count
		FROM targets
		GROUP BY target_university
		ORDER BY COUNT(*) DESC, target_university`

		// Query for target majors
		majorsQuery := `
		WITH targets AS (
			SELECT NULLIF(TRIM(target_major), '') as target_major
			FROM user_demographics
			WHERE 
				target_major IS NOT NULL 
				AND TRIM(target_major) != ''
		)
		SELECT 
			target_major as value,
			target_major as label,
			COUNT(*) as count
		FROM targets
		GROUP BY target_major
		ORDER BY COUNT(*) DESC, target_major`

		// Query for categories
		categoriesQuery := `
		SELECT 
			c.id as value,
			c.name as label,
			c.image_url as "imageUrl",
			COUNT(ec.exam_id) as count
		FROM 
			categories c
		LEFT JOIN 
			exam_categories ec ON c.id = ec.category_id
		GROUP BY 
			c.id, c.name, c.image_url
		ORDER BY 
			c.name ASC`

		type TargetOption struct {
			Value    string  `json:"value" db:"value"`
			Label    string  `json:"label" db:"label"`
			Count    int     `json:"count" db:"count"`
			ImageURL *string `json:"imageUrl,omitempty" db:"imageUrl"`
		}

		type TargetAggregates struct {
			Positions    []TargetOption `json:"positions"`
			Institutions []TargetOption `json:"institutions"`
			Universities []TargetOption `json:"universities"`
			Majors       []TargetOption `json:"majors"`
			Categories   []TargetOption `json:"categories"`
		}

		// Get positions
		var positions []TargetOption
		err := dbx.Select(&positions, positionsQuery)
		if err != nil {
			log.Printf("Error fetching positions: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch target positions"})
			return
		}

		// Get institutions
		var institutions []TargetOption
		err = dbx.Select(&institutions, institutionsQuery)
		if err != nil {
			log.Printf("Error fetching institutions: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch target institutions"})
			return
		}

		// Get universities
		var universities []TargetOption
		err = dbx.Select(&universities, universitiesQuery)
		if err != nil {
			log.Printf("Error fetching universities: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch target universities"})
			return
		}

		// Get majors
		var majors []TargetOption
		err = dbx.Select(&majors, majorsQuery)
		if err != nil {
			log.Printf("Error fetching majors: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch target majors"})
			return
		}

		// Get categories
		var categories []TargetOption
		err = dbx.Select(&categories, categoriesQuery)
		if err != nil {
			log.Printf("Error fetching categories: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch categories"})
			return
		}

		// Combine results
		result := TargetAggregates{
			Positions:    positions,
			Institutions: institutions,
			Universities: universities,
			Majors:       majors,
			Categories:   categories,
		}

		c.JSON(200, gin.H{
			"message": "Target aggregates retrieved successfully",
			"data":    result,
		})
	}
}

// Add a new route to get just categories
func getCategories(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Query to get all categories
		categoriesQuery := `
		SELECT 
			c.id as value,
			c.name as label,
			c.image_url as "imageUrl",
			COUNT(ec.exam_id) as count
		FROM 
			categories c
		LEFT JOIN 
			exam_categories ec ON c.id = ec.category_id
		GROUP BY 
			c.id, c.name, c.image_url
		ORDER BY 
			c.name ASC`

		type CategoryOption struct {
			Value    string  `json:"value" db:"value"`
			Label    string  `json:"label" db:"label"`
			ImageURL *string `json:"imageUrl,omitempty" db:"imageUrl"`
			Count    int     `json:"count" db:"count"`
		}

		var categories []CategoryOption
		err := dbx.Select(&categories, categoriesQuery)
		if err != nil {
			log.Printf("Error fetching categories: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch categories"})
			return
		}

		c.JSON(200, gin.H{
			"message": "Categories retrieved successfully",
			"data":    categories,
		})
	}
}

type ExamProgress struct {
	SessionID string    `db:"session_id" json:"sessionId"`
	StartTime time.Time `db:"start_time" json:"startTime"`
	TWKScore  float64   `db:"twk_score" json:"twkScore"`
	TIUScore  float64   `db:"tiu_score" json:"tiuScore"`
	TKPScore  float64   `db:"tkp_score" json:"tkpScore"`
}

func getExamProgress(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			c.JSON(400, gin.H{"error": "Email is required"})
			return
		}

		// Sanitize the email
		email = sanitizeInput(email)

		// Load the exam configuration
		config, err := loadExamConfiguration()
		if err != nil {
			log.Printf("Error loading exam configuration: %v", err)
			c.JSON(500, gin.H{"error": "Failed to load exam configuration"})
			return
		}

		// Dynamic exam progress type that includes metadata
		type ExamProgress struct {
			SessionID      string             `db:"session_id" json:"sessionId"`
			StartTime      time.Time          `db:"start_time" json:"startTime"`
			MetadataScores *string            `db:"metadata_scores" json:"-"`
			SubjectScores  map[string]float64 `json:"subjectScores"`
		}

		// Basic query to get session data
		query := `
        SELECT 
            es.id AS session_id,
            es.start_time,
            esc.metadata_scores
        FROM 
            exam_sessions es
        JOIN 
            exam_scores esc ON es.session_id = esc.session_id
        JOIN
            users u ON es.user_id = u.id
        WHERE 
            u.email = $1
        ORDER BY 
            es.start_time ASC`

		var progress []ExamProgress
		if err := dbx.Select(&progress, query, email); err != nil {
			log.Printf("Error fetching exam progress: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch exam progress"})
			return
		}

		// Process each progress entry to extract subject scores
		for i := range progress {
			progress[i].SubjectScores = make(map[string]float64)

			if progress[i].MetadataScores != nil {
				// Parse metadata
				var metadata map[string]interface{}
				if err := json.Unmarshal([]byte(*progress[i].MetadataScores), &metadata); err != nil {
					log.Printf("Error unmarshaling metadata: %v", err)
					continue
				}

				// Get exam type to find corresponding exam configuration
				// Check for exam_type first, fallback to trial_type for backwards compatibility
				examTypeVal, hasExamType := metadata["exam_type"]

				var examType string
				if hasExamType {
					examType, _ = examTypeVal.(string)
				}
				examTypeConfig, exists := config.ExamTypes[strings.ToUpper(examType)]

				if exists {
					// Extract scores for each subject
					for _, subject := range examTypeConfig.Subjects {
						key := subject.Key + "_score"
						if score, ok := metadata[key]; ok {
							if scoreFloat, ok := score.(float64); ok {
								progress[i].SubjectScores[subject.Key] = scoreFloat
							} else if scoreStr, ok := score.(string); ok {
								if scoreFloat, err := strconv.ParseFloat(scoreStr, 64); err == nil {
									progress[i].SubjectScores[subject.Key] = scoreFloat
								}
							}
						}
					}
				} else {
					// If exam_type doesn't match any config, try to extract common score fields
					for _, examTypeConfig := range config.ExamTypes {
						for _, subject := range examTypeConfig.Subjects {
							key := subject.Key + "_score"
							if score, ok := metadata[key]; ok {
								if scoreFloat, ok := score.(float64); ok {
									progress[i].SubjectScores[subject.Key] = scoreFloat
								} else if scoreStr, ok := score.(string); ok {
									if scoreFloat, err := strconv.ParseFloat(scoreStr, 64); err == nil {
										progress[i].SubjectScores[subject.Key] = scoreFloat
									}
								}
							}
						}
					}
				}
			}
		}

		c.JSON(200, gin.H{"progress": progress})
	}
}

type OrderHistory struct {
	OrderID       string     `db:"order_id" json:"orderId"`
	OrderDate     time.Time  `db:"order_date" json:"orderDate"`
	Username      string     `db:"username" json:"username"`
	ExamName      string     `db:"exam_name" json:"examName"`
	Quantity      int        `db:"quantity" json:"quantity"`
	OrderStatus   string     `db:"order_status" json:"orderStatus"`
	InvoiceNumber *string    `db:"invoice_number" json:"invoiceNumber"`
	InvoiceStatus *string    `db:"invoice_status" json:"invoiceStatus"`
	DueDate       *time.Time `db:"due_date" json:"dueDate"`
	PaymentAmount *float64   `db:"payment_amount" json:"paymentAmount"`
	PaymentStatus *string    `db:"payment_status" json:"paymentStatus"`
	PaymentMethod *string    `db:"payment_method" json:"paymentMethod"`
}

func getOrderHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Email is required"})
			return
		}

		query := `
		SELECT 
			o.id AS order_id,
			o.created_at AS order_date,
			u.username,
			ae.name AS exam_name,
			o.quantity,
			o.status AS order_status,
			i.invoice_number,
			i.status AS invoice_status,
			i.due_date,
			p.amount AS payment_amount,
			p.status AS payment_status,
			p.payment_method
		FROM 
			orders o
		JOIN 
			users u ON o.client_id = u.id
		JOIN 
			available_exams ae ON o.exam_id = ae.id
		LEFT JOIN 
			invoices i ON o.id = i.order_id
		LEFT JOIN 
			payments p ON i.id = p.invoice_id
		WHERE 
			u.email = $1
		ORDER BY 
			o.created_at DESC;
		`

		var orderHistory []OrderHistory
		err := dbx.Select(&orderHistory, query, email)
		if err != nil {
			log.Printf("Error fetching order history: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order history"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"orders": orderHistory})
	}
}

type OrderDetail struct {
	OrderID          string     `db:"order_id" json:"orderId"`
	OrderDate        time.Time  `db:"order_date" json:"orderDate"`
	Username         string     `db:"username" json:"username"`
	Email            string     `db:"email" json:"email"`
	ExamName         string     `db:"exam_name" json:"examName"`
	ExamID           string     `db:"exam_id" json:"examId"`
	Quantity         string     `db:"quantity" json:"quantity"`
	OrderStatus      string     `db:"order_status" json:"orderStatus"`
	InvoiceID        *string    `db:"invoice_id" json:"invoiceId"`
	InvoiceNumber    *string    `db:"invoice_number" json:"invoiceNumber"`
	InvoiceStatus    *string    `db:"invoice_status" json:"invoiceStatus"`
	DueDate          *time.Time `db:"due_date" json:"dueDate"`
	PaymentID        *string    `db:"payment_id" json:"paymentId"`
	PaymentAmount    *float64   `db:"amount" json:"paymentAmount"`
	PaymentStatus    *string    `db:"payment_status" json:"paymentStatus"`
	PaymentMethod    *string    `db:"payment_method" json:"paymentMethod"`
	TransactionID    *string    `db:"transaction_id" json:"transactionId"`
	PaymentStartDate *time.Time `db:"payment_start_date" json:"paymentStartDate"`
	PaymentEndDate   *time.Time `db:"payment_end_date" json:"paymentEndDate"`
}

func getOrderDetail(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		orderID := c.Query("orderId")
		if orderID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
			return
		}

		// Sanitize the orderID input
		orderID = sanitizeInput(orderID)

		query := `
        SELECT 
            o.id AS order_id,
            o.created_at AS order_date,
            u.username,
            u.email,
            ae.name AS exam_name,
            ae.id AS exam_id,
            o.quantity,
            o.status::text AS order_status,
            i.id AS invoice_id,
            i.invoice_number,
            i.status::text AS invoice_status,
            i.due_date,
            p.id AS payment_id,
            p.amount,
            p.status::text AS payment_status,
            p.payment_method,
            p.transaction_id,
            p.payment_start_date,
            p.payment_end_date
        FROM 
            orders o
        JOIN 
            users u ON o.client_id = u.id
        JOIN 
            available_exams ae ON o.exam_id = ae.id
        LEFT JOIN 
            invoices i ON o.id = i.order_id
        LEFT JOIN 
            payments p ON i.id = p.invoice_id
        WHERE 
            o.id = $1`

		var orderDetail OrderDetail
		err := dbx.Get(&orderDetail, query, orderID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			} else {
				log.Printf("Error fetching order detail: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order detail"})
			}
			return
		}

		c.JSON(http.StatusOK, gin.H{"order": orderDetail})
	}
}

type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// verifyPassword handles the password verification endpoint
func verifyPassword(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("userId")
		if userId == "" {
			c.JSON(http.StatusBadRequest, gin.H{"message": "User ID is required"})
			return
		}

		var req VerifyPasswordRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid request payload"})
			return
		}

		// Get current password from database
		var currentPassword string
		err := dbx.Get(&currentPassword, "SELECT password FROM users WHERE id = $1", userId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"message": "User not found"})
				return
			}
			log.Printf("Database error while fetching user: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to verify password"})
			return
		}

		// Simple plain text comparison
		if currentPassword != req.Password {
			c.JSON(http.StatusBadRequest, gin.H{"message": "Current password is incorrect"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Password verified successfully",
		})
	}
}

// updatePassword handles the password update endpoint
func updatePassword(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("userId")
		if userId == "" {
			c.JSON(http.StatusBadRequest, gin.H{"message": "User ID is required"})
			return
		}

		var req UpdatePasswordRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid request payload"})
			return
		}

		// Update password in database with plain text
		result, err := dbx.Exec(
			"UPDATE users SET password = $1, modified_at = CURRENT_TIMESTAMP WHERE id = $2",
			req.NewPassword,
			userId,
		)
		if err != nil {
			log.Printf("Error updating password: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to update password"})
			return
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil || rowsAffected == 0 {
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to update password"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Password updated successfully",
		})
	}
}
