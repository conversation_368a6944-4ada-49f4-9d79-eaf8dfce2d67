package repository

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"

	lib "github.com/terang-ai/backend-service/lib"
	model "github.com/terang-ai/backend-service/model/exams"
)

type ExamSessionsRepository struct {
	Db    *sqlx.DB
	Redis *redis.Client
}

// ExamSessionsPaginationInfo represents pagination metadata.
type ExamSessionsPaginationInfo struct {
	TotalData   int `json:"total_data"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
}

func NewExamSessionsRepository(db *sqlx.DB, redis *redis.Client) ExamsRepositoryInterface {
	return &ExamSessionsRepository{Db: db, Redis: redis}
}

func (m *ExamSessionsRepository) InsertExamSessions(post model.PostExamSessions) (*model.ExamSessions, error) {
	log.Println("InsertExamSessions called with post:", post)

	// Check if the database connection is nil
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Prepare the SQL statement for insertion
	query := `
        INSERT INTO exam_sessions(
            id, session_id, user_id, exam_id, type, status, start_time, end_time, answers, flagged_questions, subject
        )
        VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9::jsonb, $10::jsonb, $11
        )
        RETURNING *
    `

	// Generate ULID for ID and SessionID
	ulidId := lib.GenerateULID()
	sessionId := lib.GenerateULID()

	// Sanitize the subject to prevent SQL injection
	sanitizedSubject := strings.TrimSpace(post.Subject)

	log.Println("HELOOOOOOOOOO")
	log.Println(sanitizedSubject)
	log.Println("HELOOOOOOOOOO")

	// Create an ExamSessions object to hold the returned data
	var examSession model.ExamSessions

	// Execute the statement with parameters
	err := m.Db.QueryRowx(
		query,
		ulidId,
		sessionId,
		post.UserId,
		post.ExamId,
		post.Type,
		post.Status,
		post.StartTime,
		post.EndTime,
		post.Answers,
		post.FlaggedQuestions,
		sanitizedSubject, // Include the subject here
	).StructScan(&examSession)
	if err != nil {
		log.Println("Error executing statement:", err)
		return nil, err
	}

	return &examSession, nil
}

func (m *ExamSessionsRepository) DeleteExamSessions(id string) (bool, error) {
	// Prepare statement to check if the entity exists
	existsQuery := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE id = $1)", "exam_sessions")
	existsStmt, err := m.Db.Preparex(existsQuery)
	if err != nil {
		log.Printf("Error preparing %s existence check statement: %s\n", "exam_sessions", err)
		return false, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.Get(&exists, id)
	if err != nil {
		log.Printf("Error checking if %s exists: %s\n", "exam_sessions", err)
		return false, err
	}

	if !exists {
		return false, fmt.Errorf("%s with ID %s does not exist", "exam_sessions", id)
	}

	// Prepare statement to delete the entity
	deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE id = $1", "exam_sessions")
	deleteStmt, err := m.Db.Preparex(deleteQuery)
	if err != nil {
		log.Printf("Error preparing %s deletion statement: %s\n", "exam_sessions", err)
		return false, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.Exec(id)
	if err != nil {
		log.Printf("Error deleting %s: %s\n", "exam_sessions", err)
		return false, err
	}

	return true, nil
}

func (m *ExamSessionsRepository) GetAllExamSessionsByUserId(userId string, page int, pageSize int) ([]model.ExamSessions, ExamSessionsPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, ExamSessionsPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize

	// Query to select all exam sessions by userId with pagination
	query := "SELECT * FROM exam_sessions WHERE user_id = $1 ORDER BY id LIMIT $2 OFFSET $3"

	var dataPool []model.ExamSessions
	err := m.Db.Select(&dataPool, query, userId, pageSize, offset)
	if err != nil {
		log.Println("Error querying data:", err)
		return nil, ExamSessionsPaginationInfo{}, err
	}

	// Count total number of exam sessions for the user
	totalDataQuery := "SELECT COUNT(*) FROM exam_sessions WHERE user_id = $1"
	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery, userId)
	if err != nil {
		log.Println("Error retrieving total exam sessions count:", err)
		return nil, ExamSessionsPaginationInfo{}, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := ExamSessionsPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *ExamSessionsRepository) UpdateExamSessions(sessionId string, post model.UpdateExamSessions) (*model.ExamSessions, error) {
	// Check if the entity exists
	existsQuery := "SELECT EXISTS(SELECT 1 FROM exam_sessions WHERE session_id = $1)"
	var exists bool
	err := m.Db.Get(&exists, existsQuery, sessionId)
	if err != nil {
		log.Printf("Error checking if exam session exists: %s\n", err)
		return nil, err
	}

	if !exists {
		return nil, fmt.Errorf("exam session with ID %s does not exist", sessionId)
	}

	// Prepare update query
	updateQuery := `
		UPDATE exam_sessions 
		SET status = COALESCE($2, status),
			end_time = COALESCE($3, end_time),
			answers = COALESCE($4::jsonb, answers),
			flagged_questions = COALESCE($5::jsonb, flagged_questions)
		WHERE session_id = $1
		RETURNING *
	`

	// Create an ExamSessions object to hold the returned data
	var updatedSession model.ExamSessions

	// Execute the update statement
	err = m.Db.QueryRowx(updateQuery, sessionId, post.Status, post.EndTime, post.Answers, post.FlaggedQuestions).StructScan(&updatedSession)
	if err != nil {
		log.Printf("Error updating exam session: %s\n", err)
		return nil, err
	}

	return &updatedSession, nil
}

func (m *ExamSessionsRepository) InsertExamScores(post model.PostExamScores) (*model.ExamScores, error) {
	// Check if the database connection is nil
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}

	// Prepare the SQL statement for insertion
	query := `
        INSERT INTO exam_scores(id, session_id, total_questions, correct_answers, score, metadata_scores)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
    `

	// Generate ULID for ID
	ulidId := lib.GenerateULID()

	// Create an ExamScores object to hold the returned data
	var examScore model.ExamScores

	// Execute the statement with parameters
	err := m.Db.QueryRowx(query, ulidId, post.SessionId, post.TotalQuestions, post.CorrectAnswers, post.Score, post.MetadataScores).StructScan(&examScore)
	if err != nil {
		log.Println("Error executing statement:", err)
		return nil, err
	}

	return &examScore, nil
}

func (m *ExamSessionsRepository) UpdateExamScores(id string, post model.UpdateExamScores) (*model.ExamScores, error) {
	// Check if the entity exists
	existsQuery := "SELECT EXISTS(SELECT 1 FROM exam_scores WHERE id = $1)"
	var exists bool
	err := m.Db.Get(&exists, existsQuery, id)
	if err != nil {
		log.Printf("Error checking if exam score exists: %s\n", err)
		return nil, err
	}

	if !exists {
		return nil, fmt.Errorf("exam score with ID %s does not exist", id)
	}

	// Prepare update query
	updateQuery := `
		UPDATE exam_scores 
		SET correct_answers = COALESCE($2, correct_answers),
			score = COALESCE($3, score)
		WHERE id = $1
		RETURNING *
	`

	// Create an ExamScores object to hold the returned data
	var updatedScore model.ExamScores

	// Execute the update statement
	err = m.Db.QueryRowx(updateQuery, id, post.CorrectAnswers, post.Score).StructScan(&updatedScore)
	if err != nil {
		log.Printf("Error updating exam score: %s\n", err)
		return nil, err
	}

	return &updatedScore, nil
}

func (m *ExamSessionsRepository) DeleteExamScores(id string) (bool, error) {
	// Prepare statement to check if the entity exists
	existsQuery := "SELECT EXISTS(SELECT 1 FROM exam_scores WHERE id = $1)"
	existsStmt, err := m.Db.Preparex(existsQuery)
	if err != nil {
		log.Printf("Error preparing exam_scores existence check statement: %s\n", err)
		return false, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.Get(&exists, id)
	if err != nil {
		log.Printf("Error checking if exam score exists: %s\n", err)
		return false, err
	}

	if !exists {
		return false, fmt.Errorf("exam score with ID %s does not exist", id)
	}

	// Prepare statement to delete the entity
	deleteQuery := "DELETE FROM exam_scores WHERE id = $1"
	deleteStmt, err := m.Db.Preparex(deleteQuery)
	if err != nil {
		log.Printf("Error preparing exam_scores deletion statement: %s\n", err)
		return false, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.Exec(id)
	if err != nil {
		log.Printf("Error deleting exam score: %s\n", err)
		return false, err
	}

	return true, nil
}

func (m *ExamSessionsRepository) GetExamScoresBySessionId(sessionId string) (*model.ExamScores, error) {
	// Prepare the SQL query
	query := "SELECT * FROM exam_scores WHERE session_id = $1"

	// Create an ExamScores object to hold the returned data
	var examScore model.ExamScores

	// Execute the query
	err := m.Db.Get(&examScore, query, sessionId)
	if err != nil {
		log.Printf("Error retrieving exam score: %s\n", err)
		return nil, err
	}

	return &examScore, nil
}

func (m *ExamSessionsRepository) GetExamSessionBySessionId(sessionId string) (*model.ExamSessionsWithDuration, error) {
	query := `
        SELECT 
            es.id, es.session_id, es.user_id, es.exam_id, es.type, es.status, 
            es.start_time, es.end_time, es.answers, es.flagged_questions, 
            es.created_at, es.modified_at,
            ae.duration::text as exam_duration,
            CASE 
                WHEN es.end_time IS NOT NULL THEN 
                    (es.end_time - es.start_time)::text
                ELSE 
                    (LEAST(NOW() - es.start_time, ae.duration))::text
            END AS elapsed_duration,
            CASE 
                WHEN es.end_time IS NOT NULL THEN 
                    (ae.duration - (es.end_time - es.start_time))::text
                ELSE 
                    (GREATEST(ae.duration - (NOW() - es.start_time), '0 seconds'::interval))::text
            END AS remaining_duration
        FROM 
            exam_sessions es
        JOIN 
            available_exams ae ON es.exam_id = ae.id
        WHERE 
            es.session_id = $1`

	var examSession model.ExamSessionsWithDuration

	err := m.Db.Get(&examSession, query, sessionId)
	if err != nil {
		return nil, fmt.Errorf("error retrieving exam session: %w", err)
	}

	return &examSession, nil
}

// Helper function to parse duration string to time.Duration
func ParseDuration(durationStr string) (time.Duration, error) {
	parts := strings.Fields(durationStr)
	if len(parts) != 3 {
		return 0, fmt.Errorf("invalid duration format: %s", durationStr)
	}

	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, err
	}
	minutes, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, err
	}
	seconds, err := strconv.Atoi(parts[2])
	if err != nil {
		return 0, err
	}

	return time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second, nil
}

func (m *ExamSessionsRepository) GetLastExamSessionsByExamIdUserId(examId, userId string) (*model.ExamSessionsWithDuration, error) {
	query := `
		SELECT 
			es.id, es.session_id, es.user_id, es.exam_id, es.type, es.status, 
			es.start_time, es.end_time, es.answers, es.flagged_questions, 
			es.created_at, es.modified_at,
			ae.duration::text as exam_duration,
			CASE 
				WHEN es.end_time IS NOT NULL THEN 
					(es.end_time - es.start_time)::text
				ELSE 
					(LEAST(NOW() - es.start_time, ae.duration))::text
			END AS elapsed_duration,
			CASE 
				WHEN es.end_time IS NOT NULL THEN 
					(ae.duration - (es.end_time - es.start_time))::text
				ELSE 
					(GREATEST(ae.duration - (NOW() - es.start_time), '0 seconds'::interval))::text
			END AS remaining_duration
		FROM 
			exam_sessions es
		JOIN 
			available_exams ae ON es.exam_id = ae.id
		WHERE 
			es.exam_id = $1 AND es.user_id = $2 
			AND es.type = 'EXAM'
		ORDER BY es.start_time DESC 
		LIMIT 1
	`

	var examSession model.ExamSessionsWithDuration

	err := m.Db.Get(&examSession, query, examId, userId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("error retrieving last exam session: %w", err)
	}

	return &examSession, nil
}

func (m *ExamSessionsRepository) GetLastTrialSessionsByExamIdUserId(examId, userId, subject string) (*model.ExamSessionsWithDuration, error) {
	query := `
		SELECT 
			es.id, es.session_id, es.user_id, es.exam_id, es.type, es.status, 
			es.start_time, es.end_time, es.answers, es.flagged_questions, 
			es.subject,  -- Include subject here
			es.created_at, es.modified_at,
			ae.duration::text as exam_duration,
			CASE 
				WHEN es.end_time IS NOT NULL THEN 
					(es.end_time - es.start_time)::text
				ELSE 
					(LEAST(NOW() - es.start_time, ae.duration))::text
			END AS elapsed_duration,
			CASE 
				WHEN es.end_time IS NOT NULL THEN 
					(ae.duration - (es.end_time - es.start_time))::text
				ELSE 
					(GREATEST(ae.duration - (NOW() - es.start_time), '0 seconds'::interval))::text
			END AS remaining_duration
		FROM 
			exam_sessions es
		JOIN 
			available_exams ae ON es.exam_id = ae.id
		WHERE 
			es.exam_id = $1 AND es.user_id = $2 
			AND es.type = 'PRACTICE'
			AND es.subject = $3  -- Filter by subject
		ORDER BY es.start_time DESC 
		LIMIT 1
	`

	var examSession model.ExamSessionsWithDuration

	err := m.Db.Get(&examSession, query, examId, userId, subject)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("error retrieving last trial session: %w", err)
	}

	return &examSession, nil
}
