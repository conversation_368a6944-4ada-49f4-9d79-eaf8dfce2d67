-- Drop all triggers first
DROP TRIGGER IF EXISTS user_subscriptions_modified_at ON user_subscriptions;
DROP TRIGGER IF EXISTS subscription_tiers_modified_at ON subscription_tiers;
DROP TRIGGER IF EXISTS exam_gamification_modified_at ON exam_gamification;
DROP TRIGGER IF EXISTS exam_scores_modified_at ON exam_scores;
DROP TRIGGER IF EXISTS exam_sessions_modified_at ON exam_sessions;
DROP TRIGGER IF EXISTS update_media_modified_at ON media;
DROP TRIGGER IF EXISTS set_due_date_trigger ON invoices;
DROP TRIGGER IF EXISTS invoice_modified_at ON invoices;
DROP TRIGGER IF EXISTS payment_modified_at ON payments;
DROP TRIGGER IF EXISTS order_modified_at ON orders;
DROP TRIGGER IF EXISTS tag_modified_at ON tags;
DROP TRIGGER IF EXISTS category_modified_at ON categories;
DROP TRIGGER IF EXISTS terms_of_service_modified_at ON terms_of_services;
DROP TRIGGER IF EXISTS available_exams_modified_at ON available_exams;
DROP TRIGGER IF EXISTS user_modified_at ON users;
DROP TRIGGER IF EXISTS update_user_demographics_modtime ON user_demographics;
DROP TRIGGER IF EXISTS ensure_exam_session ON exam_gamification;

-- Drop all functions
DROP FUNCTION IF EXISTS update_modified_at();
DROP FUNCTION IF EXISTS set_due_date();
DROP FUNCTION IF EXISTS check_exam_session_type();

-- Drop indexes
DROP INDEX IF EXISTS idx_subscription_tiers_price;
DROP INDEX IF EXISTS idx_subscription_tiers_name;
DROP INDEX IF EXISTS idx_user_subscriptions_is_active;
DROP INDEX IF EXISTS idx_user_subscriptions_tier_id;
DROP INDEX IF EXISTS idx_user_subscriptions_user_id;
DROP INDEX IF EXISTS idx_gamification_question_times;
DROP INDEX IF EXISTS idx_gamification_hints;
DROP INDEX IF EXISTS idx_gamification_exam_session;
DROP INDEX IF EXISTS idx_exam_scores_session_id;
DROP INDEX IF EXISTS idx_exam_sessions_answers;
DROP INDEX IF EXISTS idx_exam_tags_tag_id;
DROP INDEX IF EXISTS idx_exam_tags_exam_id;
DROP INDEX IF EXISTS idx_exam_categories_category_id;
DROP INDEX IF EXISTS idx_exam_categories_exam_id;
DROP INDEX IF EXISTS idx_categories_id;
DROP INDEX IF EXISTS idx_media_id;
DROP INDEX IF EXISTS idx_exam_qah_exam_id;
DROP INDEX IF EXISTS idx_available_exams_user_id;
DROP INDEX IF EXISTS idx_available_exams_baseline_price_name;
DROP INDEX IF EXISTS idx_email;
DROP INDEX IF EXISTS idx_users_email_username;

-- Drop tables in correct order (dependent tables first)
DROP TABLE IF EXISTS user_subscriptions;
DROP TABLE IF EXISTS subscription_tiers;
DROP TABLE IF EXISTS exam_gamification;
DROP TABLE IF EXISTS exam_scores;
DROP TABLE IF EXISTS exam_sessions;
DROP TABLE IF EXISTS feedback_surveys;
DROP TABLE IF EXISTS user_demographics;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS invoices;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS exam_tags;
DROP TABLE IF EXISTS tags;
DROP TABLE IF EXISTS exam_categories;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS exam_terms_of_services;
DROP TABLE IF EXISTS terms_of_services;
DROP TABLE IF EXISTS media;
DROP TABLE IF EXISTS exam_question_answer_hints;
DROP TABLE IF EXISTS available_exams;
DROP TABLE IF EXISTS user_verification_requests;
DROP TABLE IF EXISTS user_relationships;
DROP TABLE IF EXISTS user_profile_analytics;
DROP TABLE IF EXISTS user_logins;
DROP TABLE IF EXISTS users;

-- Drop ENUM types
DROP TYPE IF EXISTS subscription_type;
DROP TYPE IF EXISTS exam_game_status;
DROP TYPE IF EXISTS payment_status;
DROP TYPE IF EXISTS invoice_status;
DROP TYPE IF EXISTS order_status;
DROP TYPE IF EXISTS exam_session_type;
DROP TYPE IF EXISTS exam_session_status;
DROP TYPE IF EXISTS visibility_status;
DROP TYPE IF EXISTS user_token_status;
DROP TYPE IF EXISTS timezone_offset;
DROP TYPE IF EXISTS user_role;
DROP TYPE IF EXISTS user_tier;