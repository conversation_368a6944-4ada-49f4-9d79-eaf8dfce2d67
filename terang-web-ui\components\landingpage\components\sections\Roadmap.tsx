"use client";

import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";
import React, { useLayoutEffect, useRef } from "react";
import styled from "styled-components";
import dynamic from "next/dynamic";
import { useLanguage } from "@/app/language-wrapper";

// Theme interface
interface ThemeProps {
  theme: {
    body: string;
    text: string;
    carouselColor: string;
    fontxxl: string;
    fontxl: string;
    fontlg: string;
    fontmd: string;
    fontsm: string;
  }
}

// Use dynamic import with no SSR to ensure client-side only rendering for DrawSvg
const DrawSvg = dynamic(() => import("../DrawSvg"), {
  loading: () => <div style={{ height: "100vh" }}></div>,
  ssr: false // This is important! No server-side rendering for this component
});

const DotLottieAnimation = dynamic<any>(() => import("@/components/shared/dotlottie-animation"), {
  loading: () => <div style={{ height: "300px" }}></div>,
  ssr: false // Disable SSR for animations too
});

// Media query constants for better maintenance
const MEDIA_LARGE = "(max-width: 64em)";
const MEDIA_MEDIUM = "(max-width: 48em)";
const MEDIA_SMALL = "(max-width: 40em)";

const Section = styled.section<ThemeProps>`
  margin-top: 1rem;
  min-height: 100vh;
  width: 100vw;
  background-color: ${(props) => props.theme.body};
  position: relative;
  display: inline-block;
  overflow: hidden;
`;

const TitleContainer = styled.div`
  position: relative;
  width: 100%;
  margin: 3rem auto;
  text-align: center;
  will-change: transform;
`;

const Title = styled.h2<ThemeProps>`
  font-size: ${(props) => props.theme.fontxxl};
  text-transform: capitalize;
  color: ${(props) => props.theme.text};
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  position: relative;

  @media ${MEDIA_LARGE} {
    font-size: ${(props) => props.theme.fontxl};
  }
  @media ${MEDIA_SMALL} {
    font-size: ${(props) => props.theme.fontlg};
  }
`;

const LottieContainer = styled.div`
  position: relative;
  margin-top: 200px;
  left: 40%;
  right: 50%;
  margin-bottom: -300px;
  transform: translate(-50%, -50%);
  z-index: 1;
  width: 100%;
  height: 100%;

  @media ${MEDIA_LARGE} {
    margin-top: 100px;
    margin-bottom: -100px;
    left: 80%;
    width: 200%;
    height: 200%;
  }

  @media ${MEDIA_SMALL} {
    margin-top: 100px;
    margin-bottom: -100px;
    left: 80%;
    width: 200%;
    height: 200%;
  }
`;

const RoadmapWrapper = styled.span`
  margin-left: 1.2rem;
  position: relative;
  display: inline-block;
  z-index: 2;
  @media ${MEDIA_LARGE} {
    margin-left: 0.1rem;
  }
  @media ${MEDIA_SMALL} {
    margin-left: 0.1rem;
  }
`;

const RoadmapText = styled.span<ThemeProps>`
  color: ${(props) => props.theme.body};
  font-weight: 900;
  -webkit-text-stroke: 1.8px ${(props) => props.theme.body};
  letter-spacing: 2px;
  position: relative;
  z-index: 3;
  @media ${MEDIA_LARGE} {
    -webkit-text-stroke: 1px ${(props) => props.theme.body};
    letter-spacing: 1.5px;
  }
`;

const TitleSVG = styled.svg`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scaleX(0.64) scaleY(0.77) rotate(2deg);
  width: 165%;
  height: auto;
  z-index: 1;
  pointer-events: none;
  @media ${MEDIA_LARGE} {
    height: 390%;
  }
  @media ${MEDIA_SMALL} {
    height: 390%;
  }
`;

const Container = styled.div`
  width: 70%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  @media ${MEDIA_LARGE} {
    width: 80%;
  }
  @media ${MEDIA_MEDIUM} {
    width: 90%;
  }
`;

const SvgContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  will-change: transform;
  position: absolute; /* Important to keep this absolute */
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
`;

const Items = styled.ul`
  list-style: none;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;

  @media ${MEDIA_MEDIUM} {
    width: 90%;
  }

  & > *:nth-of-type(2n + 1) {
    justify-content: start;
    @media ${MEDIA_MEDIUM} {
      justify-content: center;
    }

    div {
      border-radius: 50px 0 50px 0;
      text-align: right;

      @media ${MEDIA_MEDIUM} {
        border-radius: 0 50px 0 50px;
        text-align: left;
        p {
          border-radius: 0 40px 0 40px;
        }
      }
    }
    p {
      border-radius: 40px 0 40px 0;
    }
  }
  & > *:nth-of-type(2n) {
    justify-content: end;
    @media ${MEDIA_MEDIUM} {
      justify-content: center;
    }
    div {
      border-radius: 0 50px 0 50px;
      text-align: left;
    }
    p {
      border-radius: 0 40px 0 40px;
    }
  }
`;

const Item = styled.li`
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 100;
  will-change: transform;
  &:first-child {
    padding-top: 10rem;
  }

  @media ${MEDIA_LARGE} {
    &:first-child {
      padding-top: 10rem;
    }
  }
  @media ${MEDIA_MEDIUM} {
    &:first-child {
      padding-top: 5rem;
    }
    justify-content: flex-end !important;
  }
`;

const ItemContainer = styled.div<ThemeProps>`
  width: 40%;
  height: fit-content;
  padding: 1rem;
  border: 3px solid ${(props) => props.theme.text};

  @media ${MEDIA_MEDIUM} {
    width: 70%;
  }
`;

const Box = styled.p<ThemeProps>`
  height: fit-content;
  background-color: ${(props) => props.theme.carouselColor};
  color: ${(props) => props.theme.text};
  padding: 1rem;
  position: relative;
  border: 1px solid ${(props) => props.theme.text};
`;

const SubTitle = styled.span<ThemeProps>`
  display: block;
  font-size: ${(props) => props.theme.fontxl};
  text-transform: capitalize;
  font-weight: 600;
  font-family: "Sora", sans-serif;
  color: ${(props) => props.theme.text};

  @media ${MEDIA_SMALL} {
    font-size: ${(props) => props.theme.fontlg};
    font-weight: 600;
  }
`;

const Text = styled.span<ThemeProps>`
  display: block;
  font-size: ${(props) => props.theme.fontmd};
  color: ${(props) => props.theme.text};
  font-family: "Sora", sans-serif;
  font-weight: 500;

  margin: 0.5rem 0;
  @media ${MEDIA_SMALL} {
    font-size: ${(props) => props.theme.fontsm};
  }
`;

interface RoadMapProps {
  titleKey: string;
  subtextKey: string;
  addToRef: (el: HTMLLIElement | null) => void;
}

const RoadMapItem: React.FC<RoadMapProps> = React.memo(({ titleKey, subtextKey, addToRef }) => {
  const { t } = useLanguage();
  return (
    <Item ref={addToRef}>
      <ItemContainer>
        <Box>
          <SubTitle>{t(titleKey)}</SubTitle>
          <Text>{t(subtextKey)}</Text>
        </Box>
      </ItemContainer>
    </Item>
  );
});

RoadMapItem.displayName = 'RoadMapItem';

// Define roadmap items with translation keys
const ROADMAP_ITEMS = [
  {
    titleKey: "roadmap_item1_title",
    subtextKey: "roadmap_item1_text"
  },
  {
    titleKey: "roadmap_item2_title",
    subtextKey: "roadmap_item2_text"
  },
  {
    titleKey: "roadmap_item3_title",
    subtextKey: "roadmap_item3_text"
  },
  {
    titleKey: "roadmap_item4_title",
    subtextKey: "roadmap_item4_text"
  },
  {
    titleKey: "roadmap_item5_title",
    subtextKey: "roadmap_item5_text"
  }
];

const Roadmap: React.FC = () => {
  const { t, language } = useLanguage();
  // Use any[] type for revealRefs to ensure GSAP compatibility
  const revealRefs = useRef<any[]>([]);
  revealRefs.current = [];
  
  // Make sure ScrollTrigger is only registered on the client
  if (typeof window !== 'undefined') {
    gsap.registerPlugin(ScrollTrigger);
  }

  const addToRefs = (el: HTMLLIElement | null) => {
    if (el && !revealRefs.current.includes(el)) {
      revealRefs.current.push(el);
    }
  };

  useLayoutEffect(() => {
    // Safety check for SSR
    if (typeof window === 'undefined') return;
    
    // Animation might need a slight delay to ensure DOM is ready
    const timer = setTimeout(() => {
      // Create timeline with optimized settings
      let t1 = gsap.timeline();

      revealRefs.current.forEach((el, index) => {
        if (!el) return;
        
        t1.fromTo(
          el.childNodes[0],
          {
            y: "0",
            force3D: true, // Use hardware acceleration
          },
          {
            y: "-30%",
            force3D: true, // Use hardware acceleration
            scrollTrigger: {
              id: `section-${index + 1}`,
              trigger: el,
              start: "top center+=200px",
              end: "bottom center",
              scrub: 0.5, // Smoother scrubbing
              fastScrollEnd: true, // Smoother behavior at the end of scrolling
            },
          },
        );
      });
    }, 100);

    return () => {
      clearTimeout(timer);
      if (typeof ScrollTrigger !== 'undefined') {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      }
    };
  }, []);

  return (
    <Section id="roadmap">
      <TitleContainer>
        <Title>
          Roadmap{"    "}
          <RoadmapWrapper>
            <TitleSVG viewBox="0 0 504 106" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M1106,1813.61193 C1391.48757,1788.3249 1542.09692,1785.22818 1557.82804,1804.32178 C1581.42472,1832.96217 1297.6495,1822.13368 1191.16891,1835.26224 C1084.68832,1848.39079 1016.09991,1866.56524 1566,1841.45052"
                fill="none"
                stroke="#4a90e2"
                strokeLinecap="round"
                strokeWidth="43"
                transform="translate(-1084 -1770)"
              />
            </TitleSVG>
            <RoadmapText>{t('structured_learning')}</RoadmapText>
          </RoadmapWrapper>
        </Title>
      </TitleContainer>
      <Container>
        <SvgContainer className="z-0">
          <DrawSvg />
        </SvgContainer>
        <Items>
          <LottieContainer>
            <DotLottieAnimation
              src="https://cdn.terang.ai/dotlotties/ai-robots-teaching-people.lottie"
              width="120%"
              height="120%"
              devicePixelRatio={1}
            />
          </LottieContainer>
          
          {ROADMAP_ITEMS.map((item, index) => (
            <RoadMapItem
              key={index}
              addToRef={addToRefs}
              titleKey={item.titleKey}
              subtextKey={item.subtextKey}
            />
          ))}
        </Items>
      </Container>
    </Section>
  );
};

export default Roadmap;