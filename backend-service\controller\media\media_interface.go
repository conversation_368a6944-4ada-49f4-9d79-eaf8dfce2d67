package controller

import "github.com/gin-gonic/gin"

type MediaControllerInterface interface {
	InsertMedia(*gin.Context)
	GetAllMedias(*gin.Context)
	GetOneMedia(*gin.Context)
	GetOneComsheetMediaIdByUrl(*gin.Context)
	GetOnePortfolioMediaIdByUrl(*gin.Context)
	UpdateMedia(*gin.Context)
	DeleteMedia(*gin.Context)
	TriggerLambdaMedia(*gin.Context)
	GetOneImageDiceByMediaId(*gin.Context)

	GetAllMediaByPortfolioId(*gin.Context)
	UpdateMediaPortfolioOrder(*gin.Context)
	DeleteMediaByPortfolioId(*gin.Context)

	GetAllMediaByComsheetId(*gin.Context)
	UpdateMediaComsheetOrder(*gin.Context)
	DeleteMediaByComsheetId(*gin.Context)
}
