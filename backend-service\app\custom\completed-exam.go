package custom

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

// CompletedPracticeExam represents a completed practice exam
type CompletedPracticeExam struct {
	ExamID          string  `json:"examId" db:"exam_id"`
	ExamName        string  `json:"examName" db:"name"`
	Subject         string  `json:"subject" db:"subject"`
	CompletionCount int     `json:"completionCount" db:"completion_count"`
	LastSessionID   string  `json:"lastSessionId" db:"last_session_id"`
	LastEndTime     string  `json:"lastEndTime" db:"last_end_time"`
	LastScore       float64 `json:"lastScore,omitempty" db:"last_score"`
}

// RegisterPracticeCompletionsRoute registers the route for completed practice exams
func RegisterPracticeCompletionsRoute(r *gin.Engine, dbx *sqlx.DB) {
	// Get all completed practice exams for a user
	r.GET("/v0/completed-practice-exams/user/:userId", getCompletedPracticeExams(dbx))
}

// getCompletedPracticeExams returns all practice exams completed by a user
func getCompletedPracticeExams(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		// Optional query parameters
		subject := c.Query("subject") // Filter by subject
		limit := c.DefaultQuery("limit", "100")

		// Base query to get completed practice exams with the count of completions
		query := `
			WITH completion_counts AS (
				SELECT 
					exam_id,
					subject,
					COUNT(*) as completion_count
				FROM exam_sessions
				WHERE user_id = $1
				AND type = 'PRACTICE'
				AND status = 'COMPLETED'
				GROUP BY exam_id, subject
			),
			latest_sessions AS (
				SELECT DISTINCT ON (exam_id) 
					exam_id,
					session_id as last_session_id,
					end_time as last_end_time
				FROM exam_sessions
				WHERE user_id = $1
				AND type = 'PRACTICE'
				AND status = 'COMPLETED'
				ORDER BY exam_id, end_time DESC
			),
			completed_sessions AS (
				SELECT 
					cc.exam_id,
					cc.subject,
					cc.completion_count,
					ls.last_session_id,
					ls.last_end_time
				FROM completion_counts cc
				JOIN latest_sessions ls ON cc.exam_id = ls.exam_id
			)
			SELECT 
				cs.exam_id,
				ae.name,
				cs.subject,
				cs.completion_count,
				cs.last_session_id,
				cs.last_end_time,
				COALESCE(sc.score, 0) as last_score
			FROM completed_sessions cs
			JOIN available_exams ae ON cs.exam_id = ae.id
			LEFT JOIN exam_scores sc ON cs.last_session_id = sc.session_id
		`

		// Add subject filter if provided
		args := []interface{}{userID}
		if subject != "" {
			query += " WHERE cs.subject = $2"
			args = append(args, subject)
		}

		// Add order and limit
		query += " ORDER BY cs.last_end_time DESC LIMIT $" + string('0'+len(args)+1)
		args = append(args, limit)

		// Execute query
		var completedExams []CompletedPracticeExam
		err := dbx.Select(&completedExams, query, args...)
		if err != nil {
			log.Printf("Error fetching completed practice exams: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch completed practice exams"})
			return
		}

		c.JSON(200, completedExams)
	}
}
