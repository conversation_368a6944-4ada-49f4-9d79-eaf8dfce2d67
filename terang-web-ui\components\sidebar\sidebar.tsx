import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import { useSidebarContext } from "../layout/layout-context";

import { Sidebar } from "./sidebar.styles";
import { SidebarItem } from "./sidebar-item";
import { SidebarMenu } from "./sidebar-menu";
import { CollapseItems } from "./collapse-items";
import CompanyCard from "./company-card";
import { RankingIcon } from './rank'
import SubscriptionTierCard from './sidebar-subscription'
import { Calendar02Icon, Message02Icon, AiChat02Icon, DashboardSquare02Icon, SearchCircleIcon, LicenseDraftIcon, BoardMathIcon,
  StudyLampIcon, CreditCardPosIcon, AnalysisTextLinkIcon, Quiz02Icon, BookBookmark02Icon, CardExchange02Icon, ProfitIcon, AiVoiceIcon
 } from "./sidebar-icons";

import { IconWrapper } from "./icon-wrapper";

export const SidebarWrapper = () => {
  const pathname = usePathname();
  const { collapsed, setCollapsed } = useSidebarContext();

  // Commented out the collapsible items as <PERSON><PERSON>han Topik and Flashcard are ongoing features
  const latihanItems = [
    {
      title: "Latihan Mata Uji",
      href: "/my-trials/subjects",
      icon: <IconWrapper icon={Quiz02Icon} size={18} />
    },
    // Ongoing feature - commented out
    // {
    //   title: "Latihan Topik",
    //   href: "/my-trials/keywords",
    //   icon: <IconWrapper icon={BookBookmark02Icon} size={18} />
    // },
    // Ongoing feature - commented out
    // {
    //   title: "Flashcard",
    //   href: "/my-trials/flashcards",
    //   icon: <IconWrapper icon={CardExchange02Icon} size={18} />
    // }
  ].map(item => (
    <Link 
      href={item.href}
      key={item.href}
      className={`w-full flex items-center gap-3 text-default-500 hover:text-default-900 transition-colors ${
        pathname === item.href ? "text-default-900 font-medium" : ""
      }`}
    >
      {item.icon}
      {item.title}
    </Link>
  ));

  return (
    <aside className="h-screen z-[20] sticky top-0">
      {collapsed ? (
        <div
          className={Sidebar.Overlay()}
          role="presentation"
          onClick={setCollapsed}
        />
      ) : null}
      <div
        className={Sidebar({
          collapsed: collapsed,
        })}
      >
        <Link href="/">
          <div className={Sidebar.Header()}>
            <CompanyCard
              location="Latihan Sekarang!"
              logo="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
              name="Terang.ai"
            />
          </div>
        </Link>
        <div className="flex flex-col justify-between h-full">
          <div className={Sidebar.Body()}>
            <SidebarItem
              href="/dashboard"
              icon={<DashboardSquare02Icon />}
              isActive={pathname === "/dashboard"}
              title="Dashboard"
            />
            <SidebarMenu title="Artificial Intelligence">
              <SidebarItem
                href={`/chat-ai/general/${Date.now()}`}
                icon={<AiChat02Icon />}
                isActive={pathname?.startsWith('/chat-ai/')}
                title="Tanya AI"
              />
              <div className="relative">
                <SidebarItem
                  href={`/available-interviews`}
                  icon={<AiVoiceIcon />}
                  isActive={pathname?.startsWith('/available-interviews')}
                  title="AI Interview"
                />
                {/* BETA Badge */}
                <span className="absolute -top-1 right-10 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-lg">
                  BETA
                </span>
                {/* NEW Badge */}
                <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-lg animate-pulse">
                  NEW
                </span>
              </div>
            </SidebarMenu>
            <SidebarMenu title="Intelligence Insights">
              <SidebarItem
                href="/reports"
                icon={<AnalysisTextLinkIcon />}
                isActive={pathname === "/reports"}
                title="Skill Insights"
              />
            </SidebarMenu>
            <SidebarMenu title="Main Menu">
              {/* <SidebarItem
                href="/6-join-class"
                icon={<BoardMathIcon />}
                isActive={pathname === "/6-join-class"}
                title="Kelas Saya"
              />
              <SidebarItem
                href="/8-study-planner"
                icon={<Calendar02Icon />}
                isActive={pathname === "/8-study-planner"}
                title="Study Planner"
              />
              <SidebarItem
                href="/9-communication"
                icon={<Message02Icon />}
                isActive={pathname === "/9-communication"}
                title="Communication"
              /> */}
              <SidebarItem
                href="/available-exams"
                icon={<SearchCircleIcon />}
                isActive={pathname === "/available-exams"}
                title="Pilih Kartu Ujian"
              />
              <SidebarItem
                href="/my-trials/subjects"
                icon={<StudyLampIcon />}
                isActive={pathname?.startsWith('/my-trials')}
                title="Latihan Saya"
              />
              <SidebarItem
                href="/my-exams"
                icon={<LicenseDraftIcon />}
                isActive={pathname === "/my-exams"}
                title="Ujian Saya"
              />
            </SidebarMenu>
            <SidebarMenu title="Leaderboard">
              <SidebarItem
                href="/leaderboard"
                icon={<RankingIcon />}
                isActive={pathname === "/leaderboard"}
                title="Si Paling Ambis"
              />
            </SidebarMenu>
            <SidebarMenu title="Referral Programme">
              <SidebarItem
                href={`/referral`}
                icon={<ProfitIcon />}
                isActive={pathname?.startsWith('/referral')}
                title="Referrals"
              />
            </SidebarMenu>
          </div>
          <SidebarMenu title="">
            <div className="mt-auto">
              <SubscriptionTierCard />
            </div>
          </SidebarMenu>
        </div>
      </div>
    </aside>
  );
};