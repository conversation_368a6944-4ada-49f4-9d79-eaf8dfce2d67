package categories

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	categories "github.com/terang-ai/backend-service/controller/categories"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	controller := categories.NewCategoryController(db, redis)

	r.POST("/v1/categories", controller.InsertCategory)
	r.GET("/v1/categories", controller.GetAllCategories)
	r.GET("/v1/categories/:id", controller.GetOneCategory)
	r.PUT("/v1/categories/:id", controller.UpdateCategory)
	r.DELETE("/v1/categories/:id", controller.DeleteCategory)

	r.POST("/v1/exam-categories", controller.InsertExamCategory)
	r.GET("/v1/exam-categories", controller.GetAllExamCategories)
	r.GET("/v1/exam-categories/:id", controller.GetOneExamCategory)
	r.PUT("/v1/exam-categories/:id", controller.UpdateExamCategory)
	r.DELETE("/v1/exam-categories/:id", controller.DeleteExamCategory)

	r.GET("/v2/categories", controller.GetAllCategoriesWithCache)
}
