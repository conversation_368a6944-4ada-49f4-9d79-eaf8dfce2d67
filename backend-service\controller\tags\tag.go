package controller

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/tags"
	"github.com/terang-ai/backend-service/repository"
)

type TagController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "wip", "user")
}

func NewTagController(db *sql.DB, redis *redis.Client) *TagController {
	return &TagController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "tags", "tag"), // Initialize with specific table and entity name
		Entity: "tag",
	}
}

func (c *TagController) DeleteTag(ctx *gin.Context) {
	var uri model.TagUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Tag
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Tag failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Tag not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Tag successfully"})
}

func (c *TagController) GetAllTags(ctx *gin.Context) {
	var entity model.Tag
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "tags retrieved successfully", "_pagination": paginationInfo})
}

func (c *TagController) GetOneTag(ctx *gin.Context) {
	var uri model.TagUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Tag
	entity := &model.Tag{}

	// Call repository method to retrieve one Tag by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Tag not found"})
		return
	}

	// Type assertion to *model.Tag
	if wipEntity, ok := result.(*model.Tag); ok {
		// Update entity with fetched data
		*entity = *wipEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Tag successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Tag failed", "msg": "internal error"})
}

func (c *TagController) InsertTag(ctx *gin.Context) {
	var post model.PostTag // Replace with your specific post type
	var entity model.Tag
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Tag failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Tag successfully"})
}

func (c *TagController) UpdateTag(ctx *gin.Context) {
	var updates model.UpdateTag
	var uri model.TagUri
	var entity model.Tag

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Tag by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Tag", "error": err.Error()})
		return
	}

	// Type assertion to *model.Tag
	if updatedTag, ok := updated.(*model.Tag); ok {
		// Update entity with fetched data after update
		entity = *updatedTag
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Tag updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Tag failed", "msg": "internal error"})
}
