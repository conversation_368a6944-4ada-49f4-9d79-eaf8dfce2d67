package controller

import "github.com/gin-gonic/gin"

type ExamsControllerInterface interface {
	InsertExamSessions(*gin.Context)
	GetAllExamSessionsByUserId(*gin.Context)
	GetLastExamSessionsByExamIdUserId(*gin.Context)
	GetLastTrialSessionsByExamIdUserId(*gin.Context)
	UpdateExamSessions(*gin.Context)
	GetExamSessionBySessionId(*gin.Context)
	DeleteExamSessions(*gin.Context)

	InsertExamScores(*gin.Context)
	UpdateExamScores(*gin.Context)
	DeleteExamScores(*gin.Context)
	GetExamScoresBySessionId(*gin.Context)
}
