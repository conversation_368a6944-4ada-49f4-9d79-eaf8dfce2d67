package custom

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/terang-ai/backend-service/lib"
)

// RegisterChatAIHistoryRoutes registers the chatai history related routes
func RegisterChatAIHistoryRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.GET("/chatai-history/:userId", getChatAIHistory(dbx))
		v0.GET("/chatai-history/:userId/:chatId", getSingleChatAIHistory(dbx))
		v0.POST("/chatai-history/:userId/:chatId", saveChatAIMessages(dbx)) // renamed to reflect array handling
		v0.PUT("/chatai-history/:userId/:chatId", updateChatAIHistory(dbx))
		v0.DELETE("/chatai-history/:userId/:chatId", deleteChatAIHistory(dbx))
	}
}

// ContentPartType defines the possible content part types
type ContentPartType string

const (
	TextType     ContentPartType = "text"
	ImageUrlType ContentPartType = "image_url"
)

// ImageUrlDetail defines the possible detail levels
type ImageUrlDetail string

const (
	Auto ImageUrlDetail = "auto"
	Low  ImageUrlDetail = "low"
	High ImageUrlDetail = "high"
)

// ImageUrl represents the image URL configuration
type ImageUrl struct {
	URL    string         `json:"url"`
	Detail ImageUrlDetail `json:"detail,omitempty"`
}

// ContentPart represents a single part of the message content
type ContentPart struct {
	Type     ContentPartType `json:"type"`
	Text     string          `json:"text,omitempty"`
	ImageUrl *ImageUrl       `json:"image_url,omitempty"`
}

// Message represents a chat message with flexible content
type Message struct {
	Role      string      `json:"role"`
	Content   interface{} `json:"content"` // Can be string or []ContentPart
	Timestamp int64       `json:"timestamp"`
}

// UnmarshalJSON implements custom JSON unmarshaling for Message
func (m *Message) UnmarshalJSON(data []byte) error {
	type tempMessage struct {
		Role      string      `json:"role"`
		Content   interface{} `json:"content"`
		Timestamp int64       `json:"timestamp"`
	}

	var temp tempMessage
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	m.Role = temp.Role
	m.Timestamp = temp.Timestamp

	// Handle content based on its type
	switch v := temp.Content.(type) {
	case string:
		m.Content = v
	case []interface{}:
		var parts []ContentPart
		for _, part := range v {
			partBytes, err := json.Marshal(part)
			if err != nil {
				return err
			}
			var contentPart ContentPart
			if err := json.Unmarshal(partBytes, &contentPart); err != nil {
				return err
			}
			parts = append(parts, contentPart)
		}
		m.Content = parts
	default:
		return fmt.Errorf("unsupported content type: %T", v)
	}

	return nil
}

// ChatAIHistory represents the chat_ai_history table structure
type ChatAIHistory struct {
	ID         string    `db:"id" json:"id"`
	UserID     string    `db:"user_id" json:"userId"`
	ChatID     string    `db:"chat_id" json:"chatId"`
	Messages   string    `db:"messages" json:"messages"`
	FromPage   string    `db:"from_page" json:"fromPage"`
	CreatedAt  time.Time `db:"created_at" json:"createdAt"`
	ModifiedAt time.Time `db:"modified_at" json:"modifiedAt"`
}

// ChatRequest struct to handle the incoming JSON
type ChatRequest struct {
	Messages []Message `json:"messages"`
	FromPage string    `json:"fromPage"`
}

// getChatAIHistory retrieves all chatai histories for a user
func getChatAIHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		var histories []ChatAIHistory
		query := `
            SELECT 
                id, user_id, chat_id, 
                messages, from_page, created_at, modified_at 
            FROM chat_ai_history 
            WHERE user_id = $1 
            ORDER BY modified_at DESC
        `

		if err := dbx.Select(&histories, query, userID); err != nil {
			log.Printf("Error fetching chatai histories for user %s: %v", userID, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch chatai histories"})
			return
		}

		// Parse messages JSON for each history
		for i := range histories {
			var messages []Message
			if err := json.Unmarshal([]byte(histories[i].Messages), &messages); err != nil {
				log.Printf("Error parsing messages for history %s: %v", histories[i].ID, err)
				// Continue with raw messages if parsing fails
				continue
			}
			histories[i].Messages = string(must(json.Marshal(messages)))
		}

		c.JSON(http.StatusOK, gin.H{"histories": histories})
	}
}

// getSingleChatAIHistory retrieves a specific chatai history
func getSingleChatAIHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		chatID := c.Param("chatId")

		var history ChatAIHistory
		query := `
            SELECT 
                id, user_id, chat_id, 
                messages, from_page, created_at, modified_at 
            FROM chat_ai_history 
            WHERE user_id = $1 AND chat_id = $2
        `

		if err := dbx.Get(&history, query, userID, chatID); err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "ChatAI history not found"})
				return
			}
			log.Printf("Error fetching chatai history: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch chatai history"})
			return
		}

		// Parse messages JSON
		var messages []Message
		if err := json.Unmarshal([]byte(history.Messages), &messages); err != nil {
			log.Printf("Error parsing messages: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse messages"})
			return
		}
		history.Messages = string(must(json.Marshal(messages)))

		c.JSON(http.StatusOK, gin.H{"history": history})
	}
}

// deleteChatAIHistory deletes a specific chatai history
func deleteChatAIHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		chatID := c.Param("chatId")

		query := "DELETE FROM chat_ai_history WHERE user_id = $1 AND chat_id = $2"

		result, err := dbx.Exec(query, userID, chatID)
		if err != nil {
			log.Printf("Error deleting chatai history: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete chatai history"})
			return
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "ChatAI history not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "ChatAI history deleted successfully"})
	}
}

// Helper function to handle JSON marshaling
func must(data []byte, err error) []byte {
	if err != nil {
		panic(err)
	}
	return data
}

func saveChatAIMessages(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		chatID := c.Param("chatId")

		var request ChatRequest
		if err := c.BindJSON(&request); err != nil {
			log.Printf("JSON binding error: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid request format",
				"details": err.Error(),
			})
			return
		}

		if len(request.Messages) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No messages provided"})
			return
		}

		// Set timestamps if not provided
		now := time.Now().UnixMilli()
		for i := range request.Messages {
			if request.Messages[i].Timestamp == 0 {
				request.Messages[i].Timestamp = now
			}
		}

		// Convert messages to JSON
		messagesJSON, err := json.Marshal(request.Messages)
		if err != nil {
			log.Printf("JSON marshaling error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process messages"})
			return
		}

		query := `
            INSERT INTO chat_ai_history (id, user_id, chat_id, messages, from_page)
            VALUES ($1, $2, $3, $4::jsonb, $5)
            ON CONFLICT (user_id, chat_id)
            DO UPDATE SET
                messages = chat_ai_history.messages || $4::jsonb,
                from_page = $5,
                modified_at = CURRENT_TIMESTAMP
            RETURNING id, user_id, chat_id, messages::text, from_page, created_at, modified_at
        `

		var history ChatAIHistory
		id := lib.GenerateULID()

		err = dbx.Get(&history, query, id, userID, chatID, string(messagesJSON), request.FromPage)
		if err != nil {
			log.Printf("Database error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to save messages",
				"details": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"history": history,
			"message": "Messages saved successfully",
		})
	}
}

func updateChatAIHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		chatID := c.Param("chatId")

		var messages []Message
		if err := c.BindJSON(&messages); err != nil {
			log.Printf("JSON binding error: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid messages format",
				"details": err.Error(),
			})
			return
		}

		if len(messages) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No messages provided"})
			return
		}

		// Set timestamps if not provided
		now := time.Now().UnixMilli()
		for i := range messages {
			if messages[i].Timestamp == 0 {
				messages[i].Timestamp = now
			}
		}

		// Convert messages to JSON
		messagesJSON, err := json.Marshal(messages)
		if err != nil {
			log.Printf("JSON marshaling error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process messages"})
			return
		}

		query := `
            UPDATE chat_ai_history 
            SET messages = $1::jsonb,
                modified_at = CURRENT_TIMESTAMP
            WHERE user_id = $2 AND chat_id = $3
            RETURNING id, user_id, chat_id, messages::text, created_at, modified_at
        `

		var history ChatAIHistory
		err = dbx.Get(&history, query, string(messagesJSON), userID, chatID)
		if err != nil {
			if err == sql.ErrNoRows {
				insertQuery := `
                    INSERT INTO chat_ai_history (id, user_id, chat_id, messages)
                    VALUES ($1, $2, $3, $4::jsonb)
                    RETURNING id, user_id, chat_id, messages::text, created_at, modified_at
                `
				id := lib.GenerateULID()
				err = dbx.Get(&history, insertQuery, id, userID, chatID, string(messagesJSON))
				if err != nil {
					log.Printf("Database insert error: %v", err)
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "Failed to create chat history",
						"details": err.Error(),
					})
					return
				}
			} else {
				log.Printf("Database update error: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Failed to update chat history",
					"details": err.Error(),
				})
				return
			}
		}

		c.JSON(http.StatusOK, gin.H{
			"history": history,
			"message": "Chat history updated successfully",
		})
	}
}
