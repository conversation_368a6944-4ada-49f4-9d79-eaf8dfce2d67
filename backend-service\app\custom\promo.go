package custom

import (
	"log"
	"net/http"
	"regexp"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/terang-ai/backend-service/lib"
)

func RegisterPromoRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.POST("/promo-email/subscribe", subscribeEmail(dbx))
		v0.GET("/promo-email/subscribe", subscribeEmailGet(dbx))
		v0.GET("/promo-email/subscribers", getSubscribers(dbx))
	}
}

type PromoEmailInput struct {
	Email     string `json:"email" binding:"required"`
	PartnerID string `json:"partner_id" binding:"required"`
}

// isValidEmail checks if the email is valid using a simple regex
func isValidEmail(email string) bool {
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	match, _ := regexp.MatchString(pattern, email)
	return match
}

func subscribeEmail(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var input PromoEmailInput

		if err := c.ShouldBindJSON(&input); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Validate email
		if !isValidEmail(input.Email) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email format"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
			return
		}

		// Generate ID
		id := lib.GenerateULID()

		// Insert email subscription
		query := `
            INSERT INTO promo_email_subscribers (
                id, email, partner_id
            ) VALUES (
                $1, $2, $3
            ) RETURNING id`

		var result = struct {
			ID string `db:"id"`
		}{}

		err = tx.Get(&result, query, id, input.Email, input.PartnerID)

		if err != nil {
			tx.Rollback()
			// Check for duplicate entry
			if err.Error() == "pq: duplicate key value violates unique constraint \"unique_email_partner\"" {
				c.JSON(http.StatusOK, gin.H{
					"message": "Email already subscribed",
					"status":  "success",
				})
				return
			}

			log.Printf("Error inserting subscription: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert subscription"})
			return
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			tx.Rollback()
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": "Subscription successful",
			"id":      result.ID,
			"status":  "success",
		})
	}
}

func subscribeEmailGet(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		partnerID := c.Query("partner_id")

		// Validate required parameters
		if email == "" || partnerID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Email and partner_id are required"})
			return
		}

		// Validate email
		if !isValidEmail(email) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email format"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
			return
		}

		// Generate ID
		id := lib.GenerateULID()

		// Insert email subscription
		query := `
            INSERT INTO promo_email_subscribers (
                id, email, partner_id
            ) VALUES (
                $1, $2, $3
            ) RETURNING id`

		var result = struct {
			ID string `db:"id"`
		}{}

		err = tx.Get(&result, query, id, email, partnerID)

		if err != nil {
			tx.Rollback()
			// Check for duplicate entry
			if err.Error() == "pq: duplicate key value violates unique constraint \"unique_email_partner\"" {
				c.JSON(http.StatusOK, gin.H{
					"message": "Email already subscribed",
					"status":  "success",
				})
				return
			}

			log.Printf("Error inserting subscription: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert subscription"})
			return
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			tx.Rollback()
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": "Subscription successful",
			"id":      result.ID,
			"status":  "success",
		})
	}
}

func getSubscribers(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		partnerID := c.Query("partner_id")
		if partnerID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "partner_id is required"})
			return
		}

		// Query to get subscribers
		query := `
			SELECT id, email, partner_id, created_at
			FROM promo_email_subscribers
			WHERE partner_id = $1
			ORDER BY created_at DESC
		`

		type Subscriber struct {
			ID        string `db:"id" json:"id"`
			Email     string `db:"email" json:"email"`
			PartnerID string `db:"partner_id" json:"partner_id"`
			CreatedAt string `db:"created_at" json:"created_at"`
		}

		var subscribers []Subscriber
		err := dbx.Select(&subscribers, query, partnerID)
		if err != nil {
			log.Printf("Error querying subscribers: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve subscribers"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"count":  len(subscribers),
			"data":   subscribers,
		})
	}
}
