package custom

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"
	"sync"
	"unicode"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

type CategoryGrading struct {
	Category          string              `json:"category"`
	PassingGrade      []PassingGradeEntry `json:"passing_grade"`
	ProgramThresholds []ProgramThreshold  `json:"program_thresholds"`
}

type PassingGradeEntry struct {
	Nama          string `json:"nama"`
	Score         int    `json:"score"`
	QuestionCount int    `json:"question_count"`
}

type ProgramThreshold struct {
	Program  string `json:"program"`
	MinScore int    `json:"min_score"` //might change this later due to error
}

type CategoryResult struct {
	Category          string          `json:"category" db:"category"`
	PassingGrade      json.RawMessage `json:"passing_grade" db:"passing_grade"`
	ProgramThresholds json.RawMessage `json:"program_thresholds" db:"program_thresholds"`
}

type QuestionCount struct {
	CategoryID    string `db:"category_id"`
	Category      string `db:"category"`
	Subject       string `db:"subject"`
	QuestionCount int    `db:"question_count"`
}

type ExtendedCategoryResult struct {
	CategoryID        string          `db:"category_id"`
	Category          string          `db:"category"`
	PassingGrade      json.RawMessage `db:"passing_grade"`
	ProgramThresholds json.RawMessage `db:"program_thresholds"`
}

// Thread-safe map for storing similarity results
type ConcurrentStringMap struct {
	mu sync.RWMutex
	m  map[string]float64
}

func NewConcurrentStringMap() *ConcurrentStringMap {
	return &ConcurrentStringMap{
		m: make(map[string]float64),
	}
}

func (c *ConcurrentStringMap) Set(key string, value float64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.m[key] = value
}

func (c *ConcurrentStringMap) Get(key string) (float64, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	val, ok := c.m[key]
	return val, ok
}

func (c *ConcurrentStringMap) GetAll() map[string]float64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make(map[string]float64, len(c.m))
	for k, v := range c.m {
		result[k] = v
	}
	return result
}

func RegisterExamGradingRoutes(r *gin.Engine, dbx *sqlx.DB) {
	// Register both endpoints with optimized handlers
	r.GET("/v0/exams/grading-info", getExamGradingWithQuestionCounts(dbx))
	r.GET("/v0/exams/question-counts", getExamQuestionCounts(dbx))
}

// Function 1: Get question counts for each category/subject
func getExamQuestionCounts(db *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		query := `
            WITH category_data AS (
                SELECT DISTINCT ON (c.id)
                    c.id AS category_id,
                    c.name AS category,
                    eqah.passing_grade->'passing_grade' AS passing_grade,
                    COALESCE(eqah.passing_grade->'program_thresholds', '[]'::JSONB) AS program_thresholds,
                    meta->>'value' AS subject
                FROM categories c
                LEFT JOIN exam_categories ec ON c.id = ec.category_id
                LEFT JOIN available_exams ae ON ec.exam_id = ae.id
                LEFT JOIN exam_question_answer_hints eqah ON ec.exam_id = eqah.exam_id,
                LATERAL jsonb_array_elements(eqah.data) AS data_element,
                LATERAL jsonb_array_elements(data_element->'metadata') AS meta
                WHERE meta->>'name' = 'subject'
                AND ae.type = 'EXAM'  -- Filter only EXAM type
                ORDER BY c.id, eqah.id  -- Ensure only one eqah per category
            ),
            question_counts AS (
                SELECT
                    cd.category_id,
                    cd.category,
                    meta->>'value' AS subject,
                    COUNT(*) AS question_count
                FROM exam_question_answer_hints eqah
                JOIN exam_categories ec ON eqah.exam_id = ec.exam_id
                JOIN available_exams ae ON ec.exam_id = ae.id
                JOIN categories c ON ec.category_id = c.id
                JOIN category_data cd ON c.id = cd.category_id AND eqah.id = (
                    SELECT eqah2.id
                    FROM exam_question_answer_hints eqah2
                    JOIN exam_categories ec2 ON eqah2.exam_id = ec2.exam_id
                    JOIN available_exams ae2 ON ec2.exam_id = ae2.id
                    WHERE ec2.category_id = c.id
                    AND ae2.type = 'EXAM'  -- Filter only EXAM type
                    LIMIT 1  -- Only count questions from one exam per category
                ),
                LATERAL jsonb_array_elements(eqah.data) AS data_element,
                LATERAL jsonb_array_elements(data_element->'metadata') AS meta
                WHERE meta->>'name' = 'subject'
                AND ae.type = 'EXAM'  -- Filter only EXAM type
                GROUP BY cd.category_id, cd.category, meta->>'value'
            )
            SELECT * FROM question_counts;`

		log.Println("Executing question counts query")
		var results []QuestionCount
		err := db.Select(&results, query)
		if err != nil {
			log.Println("Database error (question counts):", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}

		c.JSON(http.StatusOK, results)
	}
}

// Get exam grading with question counts combined - optimized with goroutines
func getExamGradingWithQuestionCounts(db *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var wg sync.WaitGroup
		var questionCounts []QuestionCount
		var gradingResults []ExtendedCategoryResult
		var questionCountsErr, gradingErr error

		// Query definitions
		questionCountQuery := `
            WITH category_data AS (
                SELECT DISTINCT ON (c.id)
                    c.id AS category_id,
                    c.name AS category,
                    eqah.passing_grade->'passing_grade' AS passing_grade,
                    COALESCE(eqah.passing_grade->'program_thresholds', '[]'::JSONB) AS program_thresholds,
                    meta->>'value' AS subject
                FROM categories c
                LEFT JOIN exam_categories ec ON c.id = ec.category_id
                LEFT JOIN available_exams ae ON ec.exam_id = ae.id
                LEFT JOIN exam_question_answer_hints eqah ON ec.exam_id = eqah.exam_id,
                LATERAL jsonb_array_elements(eqah.data) AS data_element,
                LATERAL jsonb_array_elements(data_element->'metadata') AS meta
                WHERE meta->>'name' = 'subject'
                AND ae.type = 'EXAM'  -- Filter only EXAM type
                ORDER BY c.id, eqah.id  -- Ensure only one eqah per category
            ),
            question_counts AS (
                SELECT
                    cd.category_id,
                    cd.category,
                    meta->>'value' AS subject,
                    COUNT(*) AS question_count
                FROM exam_question_answer_hints eqah
                JOIN exam_categories ec ON eqah.exam_id = ec.exam_id
                JOIN available_exams ae ON ec.exam_id = ae.id
                JOIN categories c ON ec.category_id = c.id
                JOIN category_data cd ON c.id = cd.category_id AND eqah.id = (
                    SELECT eqah2.id
                    FROM exam_question_answer_hints eqah2
                    JOIN exam_categories ec2 ON eqah2.exam_id = ec2.exam_id
                    JOIN available_exams ae2 ON ec2.exam_id = ae2.id
                    WHERE ec2.category_id = c.id
                    AND ae2.type = 'EXAM'  -- Filter only EXAM type
                    LIMIT 1  -- Only count questions from one exam per category
                ),
                LATERAL jsonb_array_elements(eqah.data) AS data_element,
                LATERAL jsonb_array_elements(data_element->'metadata') AS meta
                WHERE meta->>'name' = 'subject'
                AND ae.type = 'EXAM'  -- Filter only EXAM type
                GROUP BY cd.category_id, cd.category, meta->>'value'
            )
            SELECT * FROM question_counts`

		gradingQuery := `
            WITH category_data AS (
                SELECT DISTINCT ON (c.id)
                    c.id AS category_id,
                    c.name AS category,
                    eqah.passing_grade->'passing_grade' AS passing_grade,
                    COALESCE(eqah.passing_grade->'program_thresholds', '[]'::JSONB) AS program_thresholds
                FROM categories c
                LEFT JOIN exam_categories ec ON c.id = ec.category_id
                LEFT JOIN available_exams ae ON ec.exam_id = ae.id
                LEFT JOIN exam_question_answer_hints eqah ON ec.exam_id = eqah.exam_id
                WHERE ae.type = 'EXAM'  -- Filter only EXAM type
                ORDER BY c.id, eqah.id
            )
            SELECT
                category_id,
                category,
                passing_grade,
                program_thresholds
            FROM category_data
            WHERE passing_grade IS NOT NULL;`

		// Run both database queries concurrently
		wg.Add(2)

		// Query 1: Get question counts
		go func() {
			defer wg.Done()
			questionCountsErr = db.Select(&questionCounts, questionCountQuery)
			log.Println("Question counts query completed")
		}()

		// Query 2: Get grading information
		go func() {
			defer wg.Done()
			gradingErr = db.Select(&gradingResults, gradingQuery)
			log.Println("Grading info query completed")
		}()

		// Wait for both queries to complete
		wg.Wait()

		// Check for errors
		if questionCountsErr != nil {
			log.Println("Database error (question counts):", questionCountsErr)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}

		if gradingErr != nil {
			log.Println("Database error (grading info):", gradingErr)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}

		// Create a map of subject counts by category for easier lookup - use mutex for thread safety
		countsMutex := sync.RWMutex{}
		countsMap := make(map[string]map[string]int)
		normalizedCountsMap := make(map[string]map[string]int)

		// Process question counts concurrently
		subjectWg := sync.WaitGroup{}

		for _, qc := range questionCounts {
			subjectWg.Add(1)
			go func(qc QuestionCount) {
				defer subjectWg.Done()

				// Calculate normalized subject
				normalizedSubject := normalizeSubject(qc.Subject)

				// Safely update the maps
				countsMutex.Lock()

				// Initialize maps if needed
				if _, exists := countsMap[qc.CategoryID]; !exists {
					countsMap[qc.CategoryID] = make(map[string]int)
					normalizedCountsMap[qc.CategoryID] = make(map[string]int)
				}

				// Store the original mapping
				countsMap[qc.CategoryID][qc.Subject] = qc.QuestionCount

				// Store normalized versions for fuzzy matching
				normalizedCountsMap[qc.CategoryID][normalizedSubject] = qc.QuestionCount

				countsMutex.Unlock()
			}(qc)
		}

		// Wait for all subject processing to complete
		subjectWg.Wait()

		// Process the grading results with concurrency
		finalGradingResults := make([]CategoryGrading, 0, len(gradingResults))
		finalResultChan := make(chan CategoryGrading, len(gradingResults))
		processingWg := sync.WaitGroup{}

		for _, result := range gradingResults {
			processingWg.Add(1)

			go func(result ExtendedCategoryResult) {
				defer processingWg.Done()

				var rawPassingGrades []struct {
					Nama  string `json:"nama"`
					Score int    `json:"score"`
				}
				var programThresholds []ProgramThreshold

				// Unmarshal passing grades (without question counts)
				if err := json.Unmarshal(result.PassingGrade, &rawPassingGrades); err != nil {
					log.Printf("Error unmarshaling passing grades for category %s: %v", result.Category, err)
					return
				}

				// Unmarshal program thresholds
				if err := json.Unmarshal(result.ProgramThresholds, &programThresholds); err != nil {
					log.Printf("Error unmarshaling program thresholds for category %s: %v", result.Category, err)
					programThresholds = []ProgramThreshold{}
				}

				// Create channel to receive processed passing grades
				pgChan := make(chan PassingGradeEntry, len(rawPassingGrades))
				pgWg := sync.WaitGroup{}

				// Process each passing grade entry concurrently
				for _, pg := range rawPassingGrades {
					pgWg.Add(1)

					go func(pg struct {
						Nama  string `json:"nama"`
						Score int    `json:"score"`
					}) {
						defer pgWg.Done()

						countsMutex.RLock()
						categoryCountMap := countsMap[result.CategoryID]
						categoryNormalizedMap := normalizedCountsMap[result.CategoryID]
						countsMutex.RUnlock()

						// Find the best matching question count using concurrent strategies
						questionCount := findBestMatchingCountConcurrent(
							pg.Nama,
							result.CategoryID,
							categoryCountMap,
							categoryNormalizedMap,
						)

						// Send result to channel
						pgChan <- PassingGradeEntry{
							Nama:          pg.Nama,
							Score:         pg.Score,
							QuestionCount: questionCount,
						}
					}(pg)
				}

				// Close channel when all processing is done
				go func() {
					pgWg.Wait()
					close(pgChan)
				}()

				// Collect results
				passingGrades := make([]PassingGradeEntry, 0, len(rawPassingGrades))
				for entry := range pgChan {
					passingGrades = append(passingGrades, entry)
				}

				// Send complete category result
				finalResultChan <- CategoryGrading{
					Category:          result.Category,
					PassingGrade:      passingGrades,
					ProgramThresholds: programThresholds,
				}
			}(result)
		}

		// Close channel when all results are processed
		go func() {
			processingWg.Wait()
			close(finalResultChan)
		}()

		// Collect all category results
		for result := range finalResultChan {
			finalGradingResults = append(finalGradingResults, result)
		}

		// Return the combined response
		c.JSON(http.StatusOK, gin.H{
			"question_counts": questionCounts,
			"grading_info":    finalGradingResults,
		})
	}
}

// findBestMatchingCountConcurrent finds the best matching subject count using multiple concurrent strategies
func findBestMatchingCountConcurrent(subjectName, categoryID string, countMap, normalizedCountMap map[string]int) int {
	// Strategy 1: Quick direct match check first (no need for goroutine)
	if count, exists := countMap[subjectName]; exists {
		return count
	}

	// Set up a channel to receive results from different strategies
	resultChan := make(chan int, 4) // Buffer for all potential successful matches
	var wg sync.WaitGroup

	// Strategy 2: Normalized match
	wg.Add(1)
	go func() {
		defer wg.Done()
		normalizedSubject := normalizeSubject(subjectName)
		if count, exists := normalizedCountMap[normalizedSubject]; exists {
			resultChan <- count
		}
	}()

	// Strategy 3: Try with/without common prefixes
	wg.Add(1)
	go func() {
		defer wg.Done()
		prefixes := []string{"Tes ", "Test ", "Ujian "}

		// Try without prefix if it has one
		for _, prefix := range prefixes {
			if strings.HasPrefix(subjectName, prefix) {
				withoutPrefix := strings.TrimPrefix(subjectName, prefix)
				if count, exists := countMap[withoutPrefix]; exists {
					resultChan <- count
					return
				}
			}
		}

		// Try with prefix if it doesn't have one
		for _, prefix := range prefixes {
			if !strings.HasPrefix(subjectName, prefix) {
				withPrefix := prefix + subjectName
				if count, exists := countMap[withPrefix]; exists {
					resultChan <- count
					return
				}
			}
		}
	}()

	// Strategy 4: Try semantic equivalents
	wg.Add(1)
	go func() {
		defer wg.Done()
		if count := findSemanticMatchesConcurrent(subjectName, countMap); count > 0 {
			resultChan <- count
		}
	}()

	// Strategy 5: Similarity-based matching
	wg.Add(1)
	go func() {
		defer wg.Done()
		normalizedSubject := normalizeSubject(subjectName)
		bestMatch, similarity := findMostSimilarSubjectConcurrent(normalizedSubject, normalizedCountMap)
		if similarity >= 0.6 { // Only accept reasonably similar matches
			resultChan <- normalizedCountMap[bestMatch]
		}
	}()

	// Use a separate goroutine to close the channel when all work is done
	doneChan := make(chan struct{})
	go func() {
		wg.Wait()
		close(resultChan)
		close(doneChan)
	}()

	// Use a select to either get the first result or timeout
	count, ok := <-resultChan
	if ok {
		// Drain any other results that might come in
		go func() {
			<-doneChan // Make sure the close routine finishes
			for range resultChan {
				// Discard remaining results
			}
		}()
		return count
	}

	// No good match found
	return 0
}

// normalizeSubject standardizes a subject name for comparison
func normalizeSubject(subject string) string {
	// Convert to lowercase
	result := strings.ToLower(subject)

	// Remove common prefixes
	prefixes := []string{"tes ", "test ", "ujian "}
	for _, prefix := range prefixes {
		result = strings.TrimPrefix(result, prefix)
	}

	// Remove all non-alphanumeric characters
	result = strings.Map(func(r rune) rune {
		if unicode.IsLetter(r) || unicode.IsNumber(r) {
			return r
		}
		return -1
	}, result)

	return result
}

// findSemanticMatchesConcurrent looks for semantically equivalent terms using concurrency
func findSemanticMatchesConcurrent(subject string, countMap map[string]int) int {
	// Common semantic equivalents in educational context
	semanticPairs := []struct {
		term1 string
		term2 string
	}{
		{"Penalaran", "Pemecahan"},
		{"Verbal", "Bahasa"},
		{"Kuantitatif", "Numerik"},
		{"Masalah", "Problem"},
		{"Karakteristik", "Kepribadian"},
		{"Wawasan", "Pengetahuan"},
	}

	// Try to find each semantic pair in the subject
	resultChan := make(chan int, len(semanticPairs)*2) // Each pair can try 2 replacements
	var wg sync.WaitGroup

	for _, pair := range semanticPairs {
		if strings.Contains(subject, pair.term1) {
			wg.Add(1)
			go func(subject, term1, term2 string) {
				defer wg.Done()
				// Try replacing term1 with term2
				alternative := strings.Replace(subject, term1, term2, 1)
				if count, exists := countMap[alternative]; exists {
					resultChan <- count
				}
			}(subject, pair.term1, pair.term2)
		}

		if strings.Contains(subject, pair.term2) {
			wg.Add(1)
			go func(subject, term1, term2 string) {
				defer wg.Done()
				// Try replacing term2 with term1
				alternative := strings.Replace(subject, term2, term1, 1)
				if count, exists := countMap[alternative]; exists {
					resultChan <- count
				}
			}(subject, pair.term1, pair.term2)
		}
	}

	// Close channel when all work is done
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Return the first result we get, if any
	for count := range resultChan {
		return count
	}

	return 0
}

// findMostSimilarSubjectConcurrent finds the most similar subject name with parallel processing
func findMostSimilarSubjectConcurrent(normalizedSubject string, normalizedCountMap map[string]int) (string, float64) {
	resultMap := NewConcurrentStringMap()
	var wg sync.WaitGroup

	// Calculate similarity for each subject in parallel
	for subject := range normalizedCountMap {
		wg.Add(1)
		go func(subj string) {
			defer wg.Done()
			similarity := calculateSimilarity(normalizedSubject, subj)
			resultMap.Set(subj, similarity)
		}(subject)
	}

	// Wait for all calculations to complete
	wg.Wait()

	// Find the best match
	allResults := resultMap.GetAll()
	var bestMatch string
	var bestSimilarity float64 = 0

	for subject, similarity := range allResults {
		if similarity > bestSimilarity {
			bestSimilarity = similarity
			bestMatch = subject
		}
	}

	return bestMatch, bestSimilarity
}

// calculateSimilarity calculates the similarity between two strings
// Returns a value between 0 and 1, where 1 means identical
func calculateSimilarity(s1, s2 string) float64 {
	// If either string is empty, return 0
	if len(s1) == 0 || len(s2) == 0 {
		return 0
	}

	// If strings are identical, return 1
	if s1 == s2 {
		return 1
	}

	// Calculate Levenshtein distance
	distance := levenshteinDistance(s1, s2)
	maxLen := max(len(s1), len(s2))

	// Convert to similarity score (1 - normalized distance)
	return 1 - float64(distance)/float64(maxLen)
}

// levenshteinDistance calculates the edit distance between two strings
func levenshteinDistance(s1, s2 string) int {
	m, n := len(s1), len(s2)

	// Create matrix
	d := make([][]int, m+1)
	for i := range d {
		d[i] = make([]int, n+1)
	}

	// Initialize first row and column
	for i := 0; i <= m; i++ {
		d[i][0] = i
	}
	for j := 0; j <= n; j++ {
		d[0][j] = j
	}

	// Fill in the rest of the matrix
	for j := 1; j <= n; j++ {
		for i := 1; i <= m; i++ {
			if s1[i-1] == s2[j-1] {
				d[i][j] = d[i-1][j-1] // No operation needed
			} else {
				d[i][j] = min(
					d[i-1][j]+1,   // deletion
					d[i][j-1]+1,   // insertion
					d[i-1][j-1]+1, // substitution
				)
			}
		}
	}

	return d[m][n]
}

// min returns the minimum of three integers
func min(a, b, c int) int {
	if a < b {
		if a < c {
			return a
		}
		return c
	}
	if b < c {
		return b
	}
	return c
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
