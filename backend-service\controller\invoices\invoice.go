package controller

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/invoices"
	"github.com/terang-ai/backend-service/repository"
)

type InvoiceController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "wip", "user")
}

func NewInvoiceController(db *sql.DB, redis *redis.Client) *InvoiceController {
	return &InvoiceController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "invoices", "invoice"), // Initialize with specific table and entity name
		Entity: "invoice",
	}
}

func (c *InvoiceController) DeleteInvoice(ctx *gin.Context) {
	var uri model.InvoiceUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Invoice
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Invoice failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Invoice not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Invoice successfully"})
}

func (c *InvoiceController) GetAllInvoices(ctx *gin.Context) {
	var entity model.Invoice
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "invoices retrieved successfully", "_pagination": paginationInfo})
}

func (c *InvoiceController) GetOneInvoice(ctx *gin.Context) {
	var uri model.InvoiceUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Invoice
	entity := &model.Invoice{}

	// Call repository method to retrieve one Invoice by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Invoice not found"})
		return
	}

	// Type assertion to *model.Invoice
	if wipEntity, ok := result.(*model.Invoice); ok {
		// Update entity with fetched data
		*entity = *wipEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Invoice successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Invoice failed", "msg": "internal error"})
}

func (c *InvoiceController) InsertInvoice(ctx *gin.Context) {
	var post model.PostInvoice // Replace with your specific post type
	var entity model.Invoice
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Invoice failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Invoice successfully"})
}

func (c *InvoiceController) UpdateInvoice(ctx *gin.Context) {
	var updates model.UpdateInvoice
	var uri model.InvoiceUri
	var entity model.Invoice

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Invoice by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Invoice", "error": err.Error()})
		return
	}

	// Type assertion to *model.Invoice
	if updatedInvoice, ok := updated.(*model.Invoice); ok {
		// Update entity with fetched data after update
		entity = *updatedInvoice
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Invoice updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Invoice failed", "msg": "internal error"})
}
