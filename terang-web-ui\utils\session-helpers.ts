// utils/session-helpers.ts
"use client";

interface StoredSessionInfo {
  sessionId: string;
  roomName: string;
  interviewId: string;
  userId: string | boolean | null;
  createdAt: string;
  expiresAt: string;
}

const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Store session info in localStorage with expiration
export function storeSessionInfo(
  interviewId: string,
  userId: string | boolean | null,
  sessionId: string,
  roomName: string
): void {
  try {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + SESSION_DURATION);
    
    const sessionInfo: StoredSessionInfo = {
      sessionId,
      roomName,
      interviewId,
      userId,
      createdAt: now.toISOString(),
      expiresAt: expiresAt.toISOString()
    };
    
    const key = `interview_session_${interviewId}_${String(userId).replace(/[^a-zA-Z0-9]/g, '_')}`;
    localStorage.setItem(key, JSON.stringify(sessionInfo));
    
    console.log(`Session stored locally: ${sessionId} for interview ${interviewId}`);
  } catch (error) {
    console.error("Failed to store session info:", error);
  }
}

// Get stored session info with validation
export function getStoredSessionInfo(
  interviewId: string,
  userId: string | boolean | null
): StoredSessionInfo | null {
  try {
    const key = `interview_session_${interviewId}_${String(userId).replace(/[^a-zA-Z0-9]/g, '_')}`;
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      return null;
    }
    
    const sessionInfo: StoredSessionInfo = JSON.parse(stored);
    
    // Check if session has expired
    const now = new Date();
    const expiresAt = new Date(sessionInfo.expiresAt);
    
    if (now > expiresAt) {
      console.log(`Stored session expired: ${sessionInfo.sessionId}`);
      localStorage.removeItem(key);
      return null;
    }
    
    return sessionInfo;
  } catch (error) {
    console.error("Failed to get stored session info:", error);
    return null;
  }
}

// Check if there's a valid stored session
export function hasValidStoredSession(
  interviewId: string,
  userId: string | boolean | null
): boolean {
  const sessionInfo = getStoredSessionInfo(interviewId, userId);
  return sessionInfo !== null;
}

// Clear stored session info
export function clearStoredSessionInfo(
  interviewId: string,
  userId: string | boolean | null
): void {
  try {
    const key = `interview_session_${interviewId}_${String(userId).replace(/[^a-zA-Z0-9]/g, '_')}`;
    localStorage.removeItem(key);
    console.log(`Cleared stored session for interview ${interviewId}`);
  } catch (error) {
    console.error("Failed to clear stored session info:", error);
  }
}

// Clear all expired sessions (cleanup utility)
export function clearExpiredSessions(): number {
  let clearedCount = 0;
  
  try {
    const keys = Object.keys(localStorage);
    const sessionKeys = keys.filter(key => key.startsWith('interview_session_'));
    
    for (const key of sessionKeys) {
      try {
        const stored = localStorage.getItem(key);
        if (!stored) continue;
        
        const sessionInfo: StoredSessionInfo = JSON.parse(stored);
        const now = new Date();
        const expiresAt = new Date(sessionInfo.expiresAt);
        
        if (now > expiresAt) {
          localStorage.removeItem(key);
          clearedCount++;
          console.log(`Cleared expired session: ${sessionInfo.sessionId}`);
        }
      } catch (error) {
        // If we can't parse it, remove it
        localStorage.removeItem(key);
        clearedCount++;
      }
    }
  } catch (error) {
    console.error("Failed to clear expired sessions:", error);
  }
  
  return clearedCount;
}

// Get session ID from stored info
export function getStoredSessionId(
  interviewId: string,
  userId: string | boolean | null
): string | null {
  const sessionInfo = getStoredSessionInfo(interviewId, userId);
  return sessionInfo?.sessionId || null;
}

// Validate session belongs to user and interview
export function validateStoredSession(
  interviewId: string,
  userId: string | boolean | null,
  sessionId: string
): boolean {
  const sessionInfo = getStoredSessionInfo(interviewId, userId);
  
  if (!sessionInfo) {
    return false;
  }
  
  return (
    sessionInfo.sessionId === sessionId &&
    sessionInfo.interviewId === interviewId &&
    sessionInfo.userId === userId
  );
}