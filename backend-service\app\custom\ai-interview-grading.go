package custom

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	"google.golang.org/genai"
)

type InterviewGrading struct {
	SessionId string                 `json:"sessionId" db:"session_id"`
	Results   InterviewGradingResult `json:"results"`
	CreatedAt time.Time              `json:"createdAt" db:"created_at"`
	UpdatedAt time.Time              `json:"updatedAt" db:"updated_at"`
}

// Add this type definition
type AIConfig struct {
	GoogleGeminiAPIKey string `yaml:"google_gemini_api_key"`
}

type InterviewGradingResult struct {
	OverallScore          float64               `json:"overallScore" db:"overall_score"`
	CategoryScores        []CategoryScore       `json:"categoryScores"`
	CommunicationMetrics  CommunicationMetrics  `json:"communicationMetrics"`
	Highlights            []string              `json:"highlights"`
	ImprovementAreas      []string              `json:"improvementAreas"`
	QuestionStats         QuestionStats         `json:"questionStats"`
	InterviewerAssessment InterviewerAssessment `json:"interviewerAssessment"`
}

type CategoryScore struct {
	Category     string   `json:"category"`
	Score        float64  `json:"score"`
	MaxScore     float64  `json:"maxScore"`
	Feedback     string   `json:"feedback"`
	Strengths    []string `json:"strengths"`
	Improvements []string `json:"improvements"`
}

type CommunicationMetrics struct {
	ClarityScore          float64 `json:"clarityScore"`
	ConfidenceScore       float64 `json:"confidenceScore"`
	StructureScore        float64 `json:"structureScore"`
	ResponseTimeAvg       string  `json:"responseTimeAvg"`
	TotalWords            int     `json:"totalWords"`
	AverageResponseLength string  `json:"averageResponseLength"`
}

type QuestionStats struct {
	TotalQuestions              int `json:"totalQuestions"`
	PersonalQuestions           int `json:"personalQuestions"`
	StudyPlanQuestions          int `json:"studyPlanQuestions"`
	ContributionQuestions       int `json:"contributionQuestions"`
	QualificationQuestions      int `json:"qualificationQuestions"`
	LeadershipQuestions         int `json:"leadershipQuestions"`
	IndonesiaKnowledgeQuestions int `json:"indonesiaKnowledgeQuestions"`
}

type InterviewerAssessment struct {
	OverallComment  string  `json:"overallComment"`
	Recommendation  string  `json:"recommendation"`
	ConfidenceLevel string  `json:"confidenceLevel"`
	ReadinessLevel  float64 `json:"readinessLevel"`
}

type TranscriptEntry struct {
	Role      string `json:"role"`
	Content   string `json:"content"`
	Timestamp string `json:"timestamp"`
}

type TranscriptData struct {
	Transcript []TranscriptEntry `json:"transcript"`
}

// Database operations
func storeGradingInDB(ctx context.Context, dbx *sqlx.DB, grading *InterviewGrading) error {
	// Convert struct fields to JSON for storage
	categoryScores, err := json.Marshal(grading.Results.CategoryScores)
	if err != nil {
		return fmt.Errorf("failed to marshal category scores: %v", err)
	}

	communicationMetrics, err := json.Marshal(grading.Results.CommunicationMetrics)
	if err != nil {
		return fmt.Errorf("failed to marshal communication metrics: %v", err)
	}

	highlights, err := json.Marshal(grading.Results.Highlights)
	if err != nil {
		return fmt.Errorf("failed to marshal highlights: %v", err)
	}

	improvementAreas, err := json.Marshal(grading.Results.ImprovementAreas)
	if err != nil {
		return fmt.Errorf("failed to marshal improvement areas: %v", err)
	}

	questionStats, err := json.Marshal(grading.Results.QuestionStats)
	if err != nil {
		return fmt.Errorf("failed to marshal question stats: %v", err)
	}

	interviewerAssessment, err := json.Marshal(grading.Results.InterviewerAssessment)
	if err != nil {
		return fmt.Errorf("failed to marshal interviewer assessment: %v", err)
	}

	// Get the session ID FK from interview_sessions
	var sessionFK sql.NullInt64
	err = dbx.GetContext(ctx, &sessionFK, `SELECT id FROM interview_sessions WHERE session_id = $1`, grading.SessionId)
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("failed to get session FK: %v", err)
	}
	// If err == sql.ErrNoRows, sessionFK will remain sql.NullInt64{Valid: false}, which is acceptable for the DB.

	// Insert or update the record
	query := `
INSERT INTO interview_grading (
session_id,
session_id_fk,
overall_score,
category_scores,
communication_metrics,
highlights,
improvement_areas,
question_stats,
interviewer_assessment,
created_at,
updated_at
) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $10)
ON CONFLICT (session_id) DO UPDATE SET
session_id_fk = EXCLUDED.session_id_fk,
overall_score = EXCLUDED.overall_score,
category_scores = EXCLUDED.category_scores,
communication_metrics = EXCLUDED.communication_metrics,
highlights = EXCLUDED.highlights,
improvement_areas = EXCLUDED.improvement_areas,
question_stats = EXCLUDED.question_stats,
interviewer_assessment = EXCLUDED.interviewer_assessment,
updated_at = CURRENT_TIMESTAMP
`

	_, err = dbx.ExecContext(ctx, query,
		grading.SessionId,
		sessionFK,
		grading.Results.OverallScore,
		string(categoryScores),        // MODIFIED: Cast to string
		string(communicationMetrics),  // MODIFIED: Cast to string
		string(highlights),            // MODIFIED: Cast to string
		string(improvementAreas),      // MODIFIED: Cast to string
		string(questionStats),         // MODIFIED: Cast to string
		string(interviewerAssessment), // MODIFIED: Cast to string
		grading.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to store grading in database: %v", err)
	}

	return nil
}

func getGradingFromDB(ctx context.Context, dbx *sqlx.DB, sessionId string) (*InterviewGrading, error) {
	var result struct {
		ID                    int             `db:"id"`
		SessionId             string          `db:"session_id"`
		SessionIdFK           sql.NullInt64   `db:"session_id_fk"`
		OverallScore          float64         `db:"overall_score"`
		CategoryScores        json.RawMessage `db:"category_scores"`
		CommunicationMetrics  json.RawMessage `db:"communication_metrics"`
		Highlights            json.RawMessage `db:"highlights"`
		ImprovementAreas      json.RawMessage `db:"improvement_areas"`
		QuestionStats         json.RawMessage `db:"question_stats"`
		InterviewerAssessment json.RawMessage `db:"interviewer_assessment"`
		CreatedAt             time.Time       `db:"created_at"`
		UpdatedAt             time.Time       `db:"updated_at"`
	}

	query := `SELECT * FROM interview_grading WHERE session_id = $1`
	err := dbx.GetContext(ctx, &result, query, sessionId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get grading from database: %v", err)
	}

	// Unmarshal the JSON fields
	grading := &InterviewGrading{
		SessionId: result.SessionId,
		Results: InterviewGradingResult{
			OverallScore: result.OverallScore,
		},
		CreatedAt: result.CreatedAt,
		UpdatedAt: result.UpdatedAt,
	}

	// Unmarshal each JSON field
	if err := json.Unmarshal(result.CategoryScores, &grading.Results.CategoryScores); err != nil {
		return nil, fmt.Errorf("failed to unmarshal category scores: %v", err)
	}
	if err := json.Unmarshal(result.CommunicationMetrics, &grading.Results.CommunicationMetrics); err != nil {
		return nil, fmt.Errorf("failed to unmarshal communication metrics: %v", err)
	}
	if err := json.Unmarshal(result.Highlights, &grading.Results.Highlights); err != nil {
		return nil, fmt.Errorf("failed to unmarshal highlights: %v", err)
	}
	if err := json.Unmarshal(result.ImprovementAreas, &grading.Results.ImprovementAreas); err != nil {
		return nil, fmt.Errorf("failed to unmarshal improvement areas: %v", err)
	}
	if err := json.Unmarshal(result.QuestionStats, &grading.Results.QuestionStats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal question stats: %v", err)
	}
	if err := json.Unmarshal(result.InterviewerAssessment, &grading.Results.InterviewerAssessment); err != nil {
		return nil, fmt.Errorf("failed to unmarshal interviewer assessment: %v", err)
	}

	return grading, nil
}

// Update your route registration to pass AI config
func RegisterAIInterviewGradingRoutes(r *gin.Engine, dbx *sqlx.DB, redisClient *redis.Client, aiConfig *AIConfig) {
	r.POST("/v0/ai-interview/interview_:session_id/grade", handleGradeInterview(dbx, redisClient, aiConfig))
	r.GET("/v0/ai-interview/interview_:session_id/results", handleGetInterviewResults(dbx, redisClient))
}

func handleGradeInterview(dbx *sqlx.DB, redisClient *redis.Client, aiConfig *AIConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.Background()
		startTime := time.Now()

		sessionId := c.Param("session_id")
		c.Set("logger_prefix", "[AI Interview Grading]")
		c.Set("session_id", sessionId)

		log.Printf("🚀 [GRADING START] Session: %s | Timestamp: %s", sessionId, startTime.Format(time.RFC3339))

		// Initialize Google Cloud Storage client
		storageClient, err := storage.NewClient(ctx)
		if err != nil {
			errorMsg := fmt.Sprintf("❌ [GCS ERROR] Failed to create storage client: %v", err)
			log.Printf(errorMsg)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create storage client"})
			return
		}
		defer storageClient.Close()

		// FIXED: Use config for API key (you'll need to pass config to this function)
		// For now, try environment variable first, then fall back to config
		apiKey := aiConfig.GoogleGeminiAPIKey
		if apiKey == "" {
			apiKey = os.Getenv("GOOGLE_GEMINI_API_KEY")
			if apiKey == "" {
				errorMsg := "❌ [CONFIG ERROR] Google Gemini API key not found"
				log.Printf(errorMsg)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "API key not configured"})
				return
			}
		}

		// Initialize Gemini client with environment variable
		client, err := genai.NewClient(ctx, &genai.ClientConfig{
			APIKey:  apiKey,
			Backend: genai.BackendGeminiAPI,
		})
		if err != nil {
			errorMsg := fmt.Sprintf("❌ [AI INIT ERROR] Failed to initialize AI client: %v", err)
			log.Printf(errorMsg)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize AI client"})
			return
		}

		// Get transcript from Google Cloud Storage
		bucketName := "terang-ai-assets"
		objectPath := fmt.Sprintf("recordings/interview_%s/transcript.json", sessionId)

		bucket := storageClient.Bucket(bucketName)
		obj := bucket.Object(objectPath)

		reader, err := obj.NewReader(ctx)
		if err != nil {
			if err == storage.ErrObjectNotExist {
				log.Printf("🔍 [NOT FOUND] Transcript file not found (404) for object: %s/%s", bucketName, objectPath)
				c.JSON(http.StatusNotFound, gin.H{"error": "Transcript file not found"})
			} else {
				log.Printf("💥 [INTERNAL ERROR] Error fetching transcript: %v from object: %s/%s", err, bucketName, objectPath)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch transcript"})
			}
			return
		}
		defer reader.Close()

		var transcriptData TranscriptData
		if err := json.NewDecoder(reader).Decode(&transcriptData); err != nil {
			log.Printf("❌ [JSON ERROR] Failed to decode transcript: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode transcript"})
			return
		}

		// MODIFIED: Format transcript for the prompt
		var transcriptContentBuilder strings.Builder
		if len(transcriptData.Transcript) == 0 {
			log.Printf("⚠️ [TRANSCRIPT WARNING] Transcript is empty for session %s. Proceeding with empty transcript.", sessionId)
			// You might want to return an error or a specific response if the transcript is empty
			// transcriptContentBuilder.WriteString("User did not provide any input.") // Or handle as error
		} else {
			for _, entry := range transcriptData.Transcript {
				// Analyzing USER, so filter for user's role or include all for context.
				// Assuming 'user' or 'candidate' role is what needs analysis.
				// For now, including all roles for full context.
				transcriptContentBuilder.WriteString(fmt.Sprintf("%s: %s\n", entry.Role, entry.Content))
			}
		}
		formattedTranscript := transcriptContentBuilder.String()

		// MODIFIED: Define prompt as a template and use fmt.Sprintf
		// The %d in "totalWords": %d is an instruction for the LLM to fill in a number, so it's kept.
		// Changed "readinessLevel": 0 to "readinessLevel": 0.0 to match float64 type.
		promptTemplate := `Analyze the user in this interview transcript (NOT THE AI) based on the LPDP scholarship assessment criteria.
		Provide a comprehensive assessment for each specified category and additional metrics.

		LANGUAGE SETTING:
		- Use bahasa indonesia
		- Ensure the language is SEO friendly

		Transcript:
		%s

		Respond ONLY with valid JSON in the following exact format (no additional text, explanations, or markdown fences):
{
"overallScore": 0.0, // Overall interview score out of 10.0
"categoryScores": [
				{
					"category": "Personal Background & Motivation",
					"score": 0.0, // Assign a score out of 10.0 based on the candidate's performance in this category.
					"maxScore": 10.0,
					"feedback": "...", // Provide detailed feedback for this category.
					"strengths": ["...", "..."], // List key strengths demonstrated in this category.
					"improvements": ["...", "..."] // List areas for improvement in this category.
				},
				{
					"category": "Study Plans & University Choice",
					"score": 0.0,
					"maxScore": 10.0,
					"feedback": "...",
					"strengths": ["...", "..."],
					"improvements": ["...", "..."]
				},
				{
					"category": "Future Contributions to Indonesia",
					"score": 0.0,
					"maxScore": 10.0,
					"feedback": "...",
					"strengths": ["...", "..."],
					"improvements": ["...", "..."]
				},
				{
					"category": "Academic & Professional Qualifications",
					"score": 0.0,
					"maxScore": 10.0,
					"feedback": "...",
					"strengths": ["...", "..."],
					"improvements": ["...", "..."]
				},
				{
					"category": "Leadership & Organizational Experience",
					"score": 0.0,
					"maxScore": 10.0,
					"feedback": "...",
					"strengths": ["...", "..."],
					"improvements": ["...", "..."]
				},
				{
					"category": "Knowledge about Indonesia",
					"score": 0.0,
					"maxScore": 10.0,
					"feedback": "...",
					"strengths": ["...", "..."],
					"improvements": ["...", "..."]
				}
			],
			"communicationMetrics": {
				"clarityScore": 0.0, // Score out of 10.0 for overall clarity of communication.
				"confidenceScore": 0.0, // Score out of 10.0 for confidence in responses.
				"structureScore": 0.0, // Score out of 10.0 for logical structuring of answers.
				"responseTimeAvg": "0.0 seconds", // Estimate the average response time.
				"totalWords": %d, // Total words in the provided transcript (or candidate's responses, clarify if needed).
				"averageResponseLength": "0 words" // Estimate the average length of the candidate's responses.
			},
			"highlights": ["...", "..."], // List overall key strengths or positive takeaways from the interview.
			"improvementAreas": ["...", "..."], // List overall areas where the candidate could improve.
			"questionStats": {
				"totalQuestions": 0, // Count the total number of questions asked in the transcript.
				"personalQuestions": 0, // Count questions related to "Personal Background & Motivation".
				"studyPlanQuestions": 0, // Count questions related to "Study Plans & University Choice".
				"contributionQuestions": 0, // Count questions related to "Future Contributions to Indonesia".
				"qualificationQuestions": 0, // Count questions related to "Academic & Professional Qualifications".
				"leadershipQuestions": 0, // Count questions related to "Leadership & Organizational Experience".
				"indonesiaKnowledgeQuestions": 0 // Count questions related to "Knowledge about Indonesia".
			},
			"interviewerAssessment": {
				"overallComment": "...", // Provide a general overall comment on the candidate's performance.
				"recommendation": "...", // Provide a recommendation (e.g., "Highly Recommended", "Recommended", "Not Recommended").
				"confidenceLevel": "...", // State the confidence level in this assessment (e.g., "High", "Medium", "Low").
				"readinessLevel": 0 // Provide an overall readiness score out of 100 for the LPDP scholarship.
			}
		}`

		finalPrompt := fmt.Sprintf(promptTemplate, formattedTranscript)

		// Use the finalPrompt for AI analysis
		// Note: The genai SDK and model name "gemini-2.0-flash-001" might be specific.
		// Ensure this matches your genai library version and available models.
		// For official Google genai SDKs, model names are usually like "gemini-1.5-flash" or "gemini-pro".
		resp, err := client.Models.GenerateContent(ctx, "gemini-2.0-flash-001", genai.Text(finalPrompt), nil)
		if err != nil {
			log.Printf("❌ [AI ERROR] Failed to analyze interview: %v. Prompt length: %d", err, len(finalPrompt))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to analyze interview"})
			return
		}

		// Assuming `resp.Text()` is the correct way to get the response text from your `genai` client.
		// Some SDKs might use `resp.Candidates[0].Content.Parts[0].(genai.Text)` or similar.
		responseText := resp.Text() // Adjust if necessary based on your genai client library
		if responseText == "" {
			log.Printf("❌ [AI RESPONSE ERROR] No analysis results generated. Model response was empty.")
			// Potentially log more details from `resp` if available, e.g., finish reasons, safety ratings.
			c.JSON(http.StatusInternalServerError, gin.H{"error": "No analysis results generated"})
			return
		}
		// Log raw response for debugging
		log.Printf("🤖 [AI RAW RESPONSE] Length: %d chars", len(responseText))

		// Clean the AI response
		cleanedResponse := cleanAIResponse(responseText)
		log.Printf("🧹 [CLEANED RESPONSE] Length: %d chars", len(cleanedResponse))

		// Validate JSON before parsing
		if !json.Valid([]byte(cleanedResponse)) {
			log.Printf("❌ [JSON VALIDATION ERROR] Invalid JSON structure from AI.")
			log.Printf("🔍 [DEBUG] Cleaned response: %s", cleanedResponse) // Be cautious logging full PII data in production
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid JSON response from AI"})
			return
		}

		// Parse AI response
		var gradingResult InterviewGradingResult
		if err := json.Unmarshal([]byte(cleanedResponse), &gradingResult); err != nil {
			log.Printf("❌ [JSON PARSE ERROR] Failed to parse AI response: %v", err)
			log.Printf("🔍 [DEBUG] Cleaned response for parsing: %s", cleanedResponse) // Be cautious logging full PII data
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process analysis results"})
			return
		}

		// Create grading record
		now := time.Now()
		grading := InterviewGrading{
			SessionId: sessionId,
			Results:   gradingResult,
			CreatedAt: now, // Use a consistent 'now' for CreatedAt and UpdatedAt on initial creation
			UpdatedAt: now,
		}

		// Store in database first
		if err := storeGradingInDB(ctx, dbx, &grading); err != nil {
			errorMsg := fmt.Sprintf("❌ [DB ERROR] Failed to store in database: %v", err)
			log.Printf(errorMsg)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store grading results"})
			return
		}
		log.Printf("✅ [DB SUCCESS] Results stored in database successfully for session %s", sessionId)

		// Store in Redis cache
		gradingJson, err := json.Marshal(grading)
		if err != nil {
			log.Printf("⚠️ [REDIS WARNING] Failed to marshal for Redis: %v for session %s", err, sessionId)
		} else {
			gradingKey := fmt.Sprintf("interview_grading:%s", sessionId)
			if err := redisClient.Set(ctx, gradingKey, string(gradingJson), 24*time.Hour).Err(); err != nil {
				log.Printf("⚠️ [REDIS WARNING] Failed to store in Redis: %v for session %s", err, sessionId)
			} else {
				log.Printf("✅ [REDIS SUCCESS] Results cached successfully for session %s", sessionId)
			}
		}

		totalDuration := time.Since(startTime)
		log.Printf("🏁 [GRADING END] Session: %s | Status: SUCCESS | Duration: %v", sessionId, totalDuration)

		c.JSON(http.StatusOK, grading)
	}
}

func handleGetInterviewResults(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		sessionId := c.Param("session_id")
		ctx := context.Background()

		log.Printf("🔍 [RESULTS START] Session: %s | Timestamp: %s", sessionId, startTime.Format(time.RFC3339))

		if sessionId == "" {
			log.Printf("❌ [ERROR] Session ID is empty")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Session ID is required"})
			return
		}

		// Try Redis first
		gradingKey := fmt.Sprintf("interview_grading:%s", sessionId)
		log.Printf("💾 [REDIS FETCH] Attempting to fetch from Redis")

		var grading *InterviewGrading
		gradingJson, err := redisClient.Get(ctx, gradingKey).Result()
		if err == nil {
			// Redis hit
			if err := json.Unmarshal([]byte(gradingJson), &grading); err == nil {
				log.Printf("✅ [REDIS HIT] Results found in Redis cache")
				c.JSON(http.StatusOK, grading)
				return
			}
			log.Printf("⚠️ [REDIS WARNING] Failed to parse Redis data, falling back to DB")
		}

		// Redis miss or error, try database
		log.Printf("🔍 [DB FETCH] Attempting to fetch from database")
		grading, err = getGradingFromDB(ctx, dbx, sessionId)
		if err != nil {
			log.Printf("❌ [DB ERROR] Failed to fetch from database: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch grading results"})
			return
		}

		if grading == nil {
			log.Printf("🔍 [NOT FOUND] No results found for session: %s", sessionId)
			c.JSON(http.StatusNotFound, gin.H{"error": "Grading results not found"})
			return
		}

		// Update Redis cache with database data
		if gradingJson, err := json.Marshal(grading); err == nil {
			if err := redisClient.Set(ctx, gradingKey, string(gradingJson), 24*time.Hour).Err(); err != nil {
				log.Printf("⚠️ [REDIS WARNING] Failed to update cache: %v", err)
			} else {
				log.Printf("✅ [REDIS UPDATE] Cache updated with database data")
			}
		}

		fetchDuration := time.Since(startTime)
		log.Printf("🏁 [RESULTS END] Session: %s | Status: SUCCESS | Duration: %v", sessionId, fetchDuration)

		c.JSON(http.StatusOK, grading)
	}
}

// Add this helper function to clean AI response
func cleanAIResponse(response string) string {
	// Remove markdown code fences
	response = strings.ReplaceAll(response, "```json", "")
	response = strings.ReplaceAll(response, "```", "")

	// Remove backticks
	response = strings.ReplaceAll(response, "`", "")

	// Trim whitespace
	response = strings.TrimSpace(response)

	// Find JSON start and end
	start := strings.Index(response, "{")
	end := strings.LastIndex(response, "}")

	if start != -1 && end != -1 && end > start {
		return response[start : end+1]
	}

	return response
}
