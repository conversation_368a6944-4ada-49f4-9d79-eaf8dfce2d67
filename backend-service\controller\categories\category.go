package controller

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/categories"
	"github.com/terang-ai/backend-service/repository"
)

type CategoryController struct {
	Db               *sql.DB
	Redis            *redis.Client
	Repo             repository.Repository // Use the Repository interface
	RepoExamCategory repository.Repository // Use the Repository interface
	Entity           string                // Entity name (e.g., "wip", "user")
}

func NewCategoryController(db *sql.DB, redis *redis.Client) *CategoryController {
	return &CategoryController{
		Db:               db,
		Redis:            redis,
		Repo:             repository.NewBaseRepository(db, redis, "categories", "category"),           // Initialize with specific table and entity name
		RepoExamCategory: repository.NewBaseRepository(db, redis, "exam_categories", "exam category"), // Initialize with specific table and entity name
		Entity:           "category",
	}
}

func (c *CategoryController) DeleteCategory(ctx *gin.Context) {
	var uri model.CategoryUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Category
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Category failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Category not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Category successfully"})
}

func (c *CategoryController) GetAllCategories(ctx *gin.Context) {
	var entity model.Category
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "categories retrieved successfully", "_pagination": paginationInfo})
}

func (c *CategoryController) GetAllCategoriesWithCache(ctx *gin.Context) {
	var entity model.Category
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, cache_status, err := c.Repo.GetAllWithCache(ctx.Request.Context(), page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "is_cache_hit": cache_status, "msg": "Categories retrieved successfully", "_pagination": paginationInfo})
}

func (c *CategoryController) GetOneCategory(ctx *gin.Context) {
	var uri model.CategoryUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Category
	entity := &model.Category{}

	// Call repository method to retrieve one Category by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Category not found"})
		return
	}

	// Type assertion to *model.Category
	if categoryEntity, ok := result.(*model.Category); ok {
		// Update entity with fetched data
		*entity = *categoryEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Category successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Category failed", "msg": "internal error"})
}

func (c *CategoryController) InsertCategory(ctx *gin.Context) {
	var post model.PostCategory // Replace with your specific post type
	var entity model.Category
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Category failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Category successfully"})
}

func (c *CategoryController) UpdateCategory(ctx *gin.Context) {
	var updates model.UpdateCategory
	var uri model.CategoryUri
	var entity model.Category

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Category by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Category", "error": err.Error()})
		return
	}

	// Type assertion to *model.Category
	if updatedCategory, ok := updated.(*model.Category); ok {
		// Update entity with fetched data after update
		entity = *updatedCategory
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Category updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Category failed", "msg": "internal error"})
}

func (c *CategoryController) DeleteExamCategory(ctx *gin.Context) {
	var uri model.ExamCategoryUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Category
	deleted, err := c.RepoExamCategory.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Category failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Category not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Category successfully"})
}

func (c *CategoryController) GetAllExamCategories(ctx *gin.Context) {
	var entity model.ExamCategory
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.RepoExamCategory.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "categories retrieved successfully", "_pagination": paginationInfo})
}

func (c *CategoryController) GetOneExamCategory(ctx *gin.Context) {
	var uri model.ExamCategoryUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Category
	entity := &model.ExamCategory{}

	// Call repository method to retrieve one Category by ID
	result, err := c.RepoExamCategory.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Category not found"})
		return
	}

	// Type assertion to *model.Category
	if categoryEntity, ok := result.(*model.ExamCategory); ok {
		// Update entity with fetched data
		*entity = *categoryEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Category successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Category failed", "msg": "internal error"})
}

func (c *CategoryController) InsertExamCategory(ctx *gin.Context) {
	var post model.PostExamCategory // Replace with your specific post type
	var entity model.ExamCategory
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.RepoExamCategory.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Category failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Category successfully"})
}

func (c *CategoryController) UpdateExamCategory(ctx *gin.Context) {
	var updates model.UpdateExamCategory
	var uri model.ExamCategoryUri
	var entity model.ExamCategory

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Category by ID
	updated, err := c.RepoExamCategory.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Category", "error": err.Error()})
		return
	}

	// Type assertion to *model.Category
	if updatedCategory, ok := updated.(*model.ExamCategory); ok {
		// Update entity with fetched data after update
		entity = *updatedCategory
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Category updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Category failed", "msg": "internal error"})
}
