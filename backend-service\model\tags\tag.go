package model

import (
	"time"
)

// Tag represents a Tag in the system
type Tag struct {
	Id         string    `json:"id"`
	Name       string    `json:"name"`
	CreatedAt  time.Time `json:"created_at"`
	ModifiedAt time.Time `json:"modified_at"`
}

// PostTag is used for creating a new Tag. Fields can be added as necessary.
type PostTag struct {
	Id   string `json:"id"`
	Name string `json:"name" binding:"required"`
}

type UpdateTag struct {
	Name *string `json:"name,omitempty"`
}

type TagUri struct {
	ID string `uri:"id" binding:"required"`
}
