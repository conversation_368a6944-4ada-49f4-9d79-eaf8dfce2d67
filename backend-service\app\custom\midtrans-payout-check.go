package custom

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

// PayoutStatusResponse represents the response from Iris API for status check
type PayoutStatusResponse struct {
	Amount             string    `json:"amount"`
	BeneficiaryName    string    `json:"beneficiary_name"`
	BeneficiaryAccount string    `json:"beneficiary_account"`
	Bank               string    `json:"bank"`
	ReferenceNo        string    `json:"reference_no"`
	Notes              string    `json:"notes"`
	BeneficiaryEmail   string    `json:"beneficiary_email"`
	Status             string    `json:"status"`
	CreatedBy          string    `json:"created_by"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// Get payout status endpoint handler
func GetPayoutStatusAPI(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		referenceNo := c.Param("referenceNo")

		// Initialize Iris service
		irisService, err := NewIrisPayoutService()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to initialize payout service",
			})
			return
		}

		// Get payout status from Iris
		payoutStatus, err := irisService.GetPayoutStatus(referenceNo)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get payout status",
				"details": err.Error(),
			})
			return
		}

		// Return the status response
		c.JSON(http.StatusOK, gin.H{
			"status": payoutStatus.Status,
			"details": gin.H{
				"amount":              payoutStatus.Amount,
				"beneficiary_name":    payoutStatus.BeneficiaryName,
				"beneficiary_account": payoutStatus.BeneficiaryAccount,
				"bank":                payoutStatus.Bank,
				"reference_no":        payoutStatus.ReferenceNo,
				"notes":               payoutStatus.Notes,
				"beneficiary_email":   payoutStatus.BeneficiaryEmail,
				"created_at":          payoutStatus.CreatedAt,
				"updated_at":          payoutStatus.UpdatedAt,
			},
		})
	}
}

// GetPayoutStatus retrieves the current status of a payout from Iris API
func (s *IrisPayoutService) GetPayoutStatus(referenceNo string) (*PayoutStatusResponse, error) {
	endpoint := fmt.Sprintf("%s/payouts/%s", s.GetBaseURL(), referenceNo)

	// Create HTTP request
	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Add headers
	auth := base64.StdEncoding.EncodeToString([]byte(s.config.Midtrans.APIKey + ":"))
	req.Header.Set("Authorization", "Basic "+auth)
	req.Header.Set("Accept", "application/json")

	// Send request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var payoutStatus PayoutStatusResponse
	if err := json.Unmarshal(body, &payoutStatus); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &payoutStatus, nil
}

// UpdateWithdrawalStatus updates the status of a withdrawal request based on payout status
func UpdateWithdrawalStatus(dbx *sqlx.DB, referenceNo string) error {
	// Start transaction
	tx, err := dbx.Beginx()
	if err != nil {
		return fmt.Errorf("error starting transaction: %v", err)
	}
	defer tx.Rollback()

	// Get withdrawal request details with amount
	var withdrawal struct {
		ID      string          `db:"id"`
		Status  string          `db:"status"`
		Amount  float64         `db:"amount"`
		UserID  string          `db:"user_id"`
		Details json.RawMessage `db:"payment_details"`
	}

	err = tx.Get(&withdrawal, `
        SELECT id, status, amount, user_id, payment_details 
        FROM withdrawal_requests 
        WHERE payment_details->>'reference_no' = $1
        FOR UPDATE`,
		referenceNo)
	if err != nil {
		return fmt.Errorf("error fetching withdrawal: %v", err)
	}

	// Initialize Iris service
	irisService, err := NewIrisPayoutService()
	if err != nil {
		return fmt.Errorf("error initializing iris service: %v", err)
	}

	// Get payout status from Iris
	payoutStatus, err := irisService.GetPayoutStatus(referenceNo)
	if err != nil {
		return fmt.Errorf("error getting payout status: %v", err)
	}

	// Map Iris status to withdrawal status
	var newStatus string
	switch payoutStatus.Status {
	case "completed":
		newStatus = "COMPLETED"
	case "failed":
		newStatus = "FAILED"
	case "processed":
		newStatus = "PROCESSING"
	case "queued":
		newStatus = "PROCESSING"
	default:
		newStatus = "PENDING"
	}

	// Update status and details
	statusDetails := map[string]interface{}{
		"iris_status":    payoutStatus.Status,
		"last_check":     time.Now().Format(time.RFC3339),
		"status_notes":   payoutStatus.Notes,
		"last_update_at": payoutStatus.UpdatedAt.Format(time.RFC3339),
	}

	statusDetailsJSON, err := json.Marshal(statusDetails)
	if err != nil {
		return fmt.Errorf("error marshaling status details: %v", err)
	}

	// Should update processed_at and handle balance updates
	shouldUpdateProcessedAt := (newStatus == "COMPLETED" || newStatus == "FAILED")

	if shouldUpdateProcessedAt {
		// Update referral balance based on status
		var balanceUpdateQuery string
		if newStatus == "FAILED" {
			// If failed, return the amount to available balance and reduce pending balance
			balanceUpdateQuery = `
                UPDATE referral_balances 
                SET 
                    available_balance = available_balance + $1,
                    pending_balance = pending_balance - $1,
                    modified_at = CURRENT_TIMESTAMP
                WHERE user_id = $2`
		} else if newStatus == "COMPLETED" {
			// If completed, just reduce pending balance
			balanceUpdateQuery = `
                UPDATE referral_balances 
                SET 
                    pending_balance = pending_balance - $1,
                    modified_at = CURRENT_TIMESTAMP
                WHERE user_id = $2`
		}

		_, err = tx.Exec(balanceUpdateQuery, withdrawal.Amount, withdrawal.UserID)
		if err != nil {
			return fmt.Errorf("error updating referral balance: %v", err)
		}
	}

	// Build query with explicit type casts
	query := `
        UPDATE withdrawal_requests 
        SET 
            status = $1::text,
            processed_at = CASE 
                WHEN $2::boolean THEN CURRENT_TIMESTAMP
                ELSE processed_at
            END,
            payment_details = payment_details || $3::jsonb,
            notes = $4::text,
            modified_at = CURRENT_TIMESTAMP
        WHERE id = $5::text`

	_, err = tx.Exec(query,
		newStatus,
		shouldUpdateProcessedAt,
		string(statusDetailsJSON),
		payoutStatus.Notes,
		withdrawal.ID)
	if err != nil {
		return fmt.Errorf("error updating withdrawal status: %v", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("error committing transaction: %v", err)
	}

	return nil
}

// CheckPayoutStatusHandler provides an endpoint to check payout status
func CheckPayoutStatusHandler(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		referenceNo := c.Param("referenceNo")

		if err := UpdateWithdrawalStatus(dbx, referenceNo); err != nil {
			log.Printf("Error updating withdrawal status: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to check payout status",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Payout status updated successfully",
		})
	}
}

// UpdatePendingPayouts updates all pending payouts
func UpdatePendingPayouts(dbx *sqlx.DB) error {
	// Get all pending withdrawals
	var pendingWithdrawals []struct {
		ReferenceNo string `db:"reference_no"`
	}

	err := dbx.Select(&pendingWithdrawals, `
		SELECT payment_details->>'reference_no' as reference_no
		FROM withdrawal_requests
		WHERE 
			status IN ('PENDING', 'PROCESSING')
			AND payment_details->>'reference_no' IS NOT NULL
			AND (
				payment_details->>'last_check' IS NULL
				OR (CURRENT_TIMESTAMP - (payment_details->>'last_check')::timestamptz) > interval '5 minutes'
			)
	`)
	if err != nil {
		return fmt.Errorf("error fetching pending withdrawals: %v", err)
	}

	// Update each pending withdrawal
	for _, withdrawal := range pendingWithdrawals {
		if err := UpdateWithdrawalStatus(dbx, withdrawal.ReferenceNo); err != nil {
			log.Printf("Error updating withdrawal %s: %v", withdrawal.ReferenceNo, err)
			continue
		}
	}

	return nil
}

// ScheduledPayoutStatusCheck handles periodic status checks
func ScheduledPayoutStatusCheck(dbx *sqlx.DB) {
	if err := UpdatePendingPayouts(dbx); err != nil {
		log.Printf("Error in scheduled payout status check: %v", err)
	}
}
