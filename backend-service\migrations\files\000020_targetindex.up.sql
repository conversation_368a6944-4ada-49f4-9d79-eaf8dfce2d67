-- Adding indexes for target university and target major columns
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'public' AND indexname = 'idx_user_demographics_target_university'
    ) THEN
        CREATE INDEX idx_user_demographics_target_university ON user_demographics(target_university);
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'public' AND indexname = 'idx_user_demographics_target_major'
    ) THEN
        CREATE INDEX idx_user_demographics_target_major ON user_demographics(target_major);
    END IF;
END $$;