package model

import (
	"time"
)

// TermsOfService represents a TermsOfService in the system
type TermsOfService struct {
	Id         string    `json:"id"`
	Name       string    `json:"name"`
	Content    string    `json:"content"`
	ArtistId   string    `json:"artist_id"`
	CreatedAt  time.Time `json:"created_at"`
	ModifiedAt time.Time `json:"modified_at"`
}

// PostTermsOfService is used for creating a new TermsOfService. Fields can be added as necessary.
type PostTermsOfService struct {
	Id       string `json:"id"`
	Name     string `json:"name" binding:"required"`
	Content  string `json:"content" binding:"required"`
	ArtistId string `json:"artist_id" binding:"required"`
}

type UpdateTermsOfService struct {
	Name    *string `json:"name,omitempty"`
	Content *string `json:"content,omitempty"`
}

type TermsOfServiceUri struct {
	ID string `uri:"id" binding:"required"`
}

type TermsOfServiceArtistUri struct {
	ARTISTID string `uri:"artist_id" binding:"required"`
}

type TermsOfServiceIdArtistUri struct {
	ID       string `uri:"id" binding:"required"`
	ARTISTID string `uri:"artist_id" binding:"required"`
}
