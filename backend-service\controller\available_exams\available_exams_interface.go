package controller

import "github.com/gin-gonic/gin"

type AvailableExamsControllerInterface interface {
	InsertAvailableExam(*gin.Context)
	GetAllAvailableExams(*gin.Context)
	GetAllAvailableExamsWithCache(*gin.Context)
	GetOneAvailableExam(*gin.Context)
	UpdateAvailableExam(*gin.Context)
	DeleteAvailableExam(*gin.Context)

	GetAllAvailableExamsV2(*gin.Context)
	GetPublicAvailableExamsV2(*gin.Context)
	GetAvailableExamBundlesV2(*gin.Context)
	GetAllPurchasedExamsByEmail(*gin.Context)
	GetAllExamsTaken(*gin.Context)

	InsertExamQuestionAnswerHint(*gin.Context)
	GetExamQuestionAnswerHints(*gin.Context)
	GetExamQuestionAnswerHintsBySessionId(*gin.Context)
	GetExamQuestionAnswerHintsBySessionIdBySubject(*gin.Context)
}
