-- 20250416000000_alter_orders_nullable_client_id.down.sql

-- First, ensure all NULL client_id values are set to something valid
-- This is needed before making the column NOT NULL again
-- You may need to adjust this strategy based on your business logic
UPDATE orders SET client_id = 'guest_placeholder' WHERE client_id IS NULL;

-- Drop the foreign key constraint
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_client_id_fkey;

-- Make the column NOT NULL again
ALTER TABLE orders ALTER COLUMN client_id SET NOT NULL;

-- Add back the original foreign key constraint
ALTER TABLE orders ADD CONSTRAINT orders_client_id_fkey 
FOREIGN KEY (client_id) REFERENCES users(id);

-- Remove the index on client_email if it's no longer needed
DROP INDEX IF EXISTS idx_orders_client_email;

-- Remove comments
COMMENT ON COLUMN orders.client_id IS NULL;
COMMENT ON COLUMN orders.client_email IS NULL;