import { Session } from "next-auth";
import { JWT } from "next-auth/jwt";
declare module "next-auth" {
  interface Session {
    id?: string;
    firstname?: string;
    lastname?: string;
    role?: string;
    password?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }

  interface User {
    id?: string;
    firstname?: string;
    lastname?: string;
    role?: string;
    password?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    firstname?: string;
    lastname?: string;
    role?: string;
    password?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }
}
