import "@/styles/globals.css";
import type { <PERSON>ada<PERSON>, Viewport } from "next";
import clsx from "clsx";
import { SessionProvider } from "next-auth/react";
import { auth } from "@/auth";
import { Providers } from "./providers";
import { fontSans } from "@/config/fonts";
import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google';
import { Toaster } from 'sonner';
import { LanguageWrapper } from "./language-wrapper";

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1, // This prevents iOS auto-zoom without removing ability to zoom in iOS 10+
  viewportFit: 'cover',
};

export const metadata: Metadata = {
  metadataBase: new URL('https://terang.ai'),
  title: {
    default: "Terang.ai | Persiapan CPNS, UTBK & Beasiswa dengan AI",
    template: "%s | Terang.ai"
  },
  description: "Platform AI simulator untuk persiapan CPNS, UTBK, LPDP & beasiswa. La<PERSON>han wawancara virtual dengan AI, feedback personal real-time untuk TWK, TIU, TKP, TBS, TOEFL & IELTS.",
  keywords: [
    // AI Interview Keywords - New additions
    "AI interview", "wawancara AI", "simulasi wawancara AI", "interview simulator",
    "AI interview simulator", "virtual interview", "wawancara virtual",
    "AI powered interview", "interview practice AI", "mock interview AI",
    "AI interview training", "latihan wawancara AI", "interview simulation",
    "AI interview platform", "platform wawancara AI", "virtual interviewer",
    "AI interview coach", "coach wawancara AI", "interview feedback AI",
    "real-time interview feedback", "personalized interview training",
    "AI interview analysis", "analisis wawancara AI", "interview scoring AI",
    "adaptive interview system", "contextual AI interview",
    "AI interview preparation", "persiapan wawancara AI",
    
    // CPNS with AI Interview
    "CPNS AI interview", "wawancara CPNS AI", "simulasi wawancara CPNS",
    "CPNS interview simulator", "AI interview CPNS", "mock interview CPNS",
    "latihan wawancara CPNS AI", "persiapan wawancara CPNS",
    "CPNS", "ujian CPNS", "tes CPNS", "persiapan CPNS",
    "TWK", "Tes Wawasan Kebangsaan",
    "TIU", "Tes Intelegensi Umum",
    "TKP", "Tes Karakteristik Pribadi",
    "SKD", "Seleksi Kompetensi Dasar",
    "SKB", "Seleksi Kompetensi Bidang",
    "CAT", "Computer Assisted Test",
    "PNS", "Pegawai Negeri Sipil",
    "ASN", "Aparatur Sipil Negara",
    "belajar CPNS", "tips CPNS", "latihan soal CPNS",
    "Simulasi CPNS Gratis", "Simulasi CAT CPNS Gratis",
    "simulasi ujian CPNS", "tryout CPNS", "bank soal CPNS",

    // LPDP with AI Interview
    "LPDP AI interview", "wawancara LPDP AI", "simulasi wawancara LPDP",
    "LPDP interview simulator", "AI interview LPDP", "mock interview LPDP",
    "latihan wawancara LPDP AI", "persiapan wawancara LPDP",
    "LPDP", "beasiswa LPDP", "tes LPDP", "essay LPDP", "TBS LPDP", "Tes Bakat Skolastik", 
    "persiapan TBS", "tips TBS", "soal TBS", "simulasi TBS", "materi TBS",
    "latihan LPDP gratis", "Penalaran Verbal", "Pemecahan Masalah",
    "Penalaran Kuantitatif", "Simulasi LPDP Pemecahan Masalah", "latihan LPDP murah",

    // Scholarship AI Interview
    "scholarship AI interview", "wawancara beasiswa AI", "beasiswa AI interview",
    "AI interview scholarship", "mock interview beasiswa", "virtual scholarship interview",
    "beasiswa", "beasiswa dalam negeri", "beasiswa luar negeri", "beasiswa Fulbright", 
    "beasiswa Chevening", "beasiswa DAAD", "beasiswa AAS", "beasiswa Erasmus",
    "Beasiswa Unggulan", "UKBI Beasiswa Unggulan", "UKBI", "Forum Nasional Beasiswa Unggulan",
    "Platform Try Out UKBI", "tryout UKBI", "lolos UKBI", "ukbi dan beasiswa unggulan", 
    "lolos beasiswa unggulan ukbi", "persiapan latihan ukbi",

    // UTBK with AI Interview
    "UTBK AI interview", "wawancara UTBK AI", "interview UTBK",
    "UTBK", "SNBT", "persiapan kuliah", "tes UTBK", "soal UTBK", "tips UTBK", 
    "materi UTBK", "tryout UTBK", "strategi UTBK", "target UTBK", "SNBT", "persiapan PTN",
    "latihan UTBK gratis", "SNBP", "persiapan UKBI",

    // Professional AI Interview
    "job interview AI", "wawancara kerja AI", "AI job interview simulator",
    "professional interview AI", "career interview preparation",
    "AI interview for jobs", "virtual job interview", "mock job interview AI",
    "wawancara kerja", "interview tips", "persiapan interview kerja",

    // Language Tests with AI Interview
    "TOEFL AI interview", "IELTS AI interview", "English test AI interview",
    "TOEFL", "IELTS", "persiapan TOEFL", "persiapan IELTS",
    "English speaking practice AI", "AI English interview",

    // General Testing and Skills
    "tes potensi akademik", "TPA", "psikotes", "tes verbal", "tes numerik", "tes logika", 
    "tes spasial", "tes penalaran", "tes kemampuan kognitif",
    "wawancara kerja", "interview tips",

    // AI and Technology
    "AI", "kecerdasan buatan", "belajar personal", "teknologi pendidikan",
    "personalized learning AI", "adaptive learning", "machine learning education",
    "AI tutoring", "intelligent tutoring system", "AI assessment",

    // Online Learning
    "bimbingan belajar online", "kursus online", "e-learning",
    "online interview training", "virtual learning platform",
    "digital interview preparation", "online mock interview",

    // Content and Materials
    "materi CPNS", "rangkuman CPNS", "kisi-kisi CPNS",
    "passing grade CPNS", "formasi CPNS", "jadwal CPNS",
    "tips lolos CPNS", "strategi CPNS", "trik CPNS",
    "PPPK", "P3K", "ASN PPPK", "tes PPPK",
    "STAN", "STIS", "STSN", "sekolah kedinasan",
  ],
  authors: [{ name: "Tim Terang.ai" }],
  creator: "Terang.ai",
  publisher: "Terang.ai",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'id_ID',
    url: 'https://terang.ai',
    siteName: 'Terang.ai',
    title: 'Terang.ai | Platform AI untuk persiapan CPNS, UTBK & Beasiswa',
    description: 'Simulator wawancara AI terdepan untuk persiapan CPNS, UTBK & beasiswa. Latihan interview virtual dengan feedback real-time dan analisis mendalam.',
    images: [
      {
        url: 'https://cdn.terang.ai/images/logo/logo-thumbnail-terang-ai.png',
        width: 1200,
        height: 630,
        alt: 'Logo Terang.ai - AI Interview Simulator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Terang.ai | AI Simulator untuk persiapan CPNS, UTBK & Beasiswa',
    description: 'Platform wawancara AI terdepan. Latihan interview virtual dengan feedback personal dan analisis mendalam untuk meningkatkan performa wawancara kamu!',
    creator: '@Terangai',
    images: ['https://cdn.terang.ai/images/logo/logo-thumbnail-terang-ai.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <html lang="id" suppressHydrationWarning>
      <head>
        <link rel="canonical" href="https://terang.ai" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#ffffff" />
        
        {/* Viewport meta tag to prevent iOS zooming on form inputs while preserving zoom functionality */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no, viewport-fit=cover" />
        
        {/* Additional meta tags for WhatsApp sharing with CDN */}
        <meta property="og:image:secure_url" content="https://cdn.terang.ai/images/logo/logo-thumbnail-terang-ai.png" />
        <meta property="og:image:type" content="image/png" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        
        {/* Comprehensive font-size fix for iOS zooming issue */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Target iOS devices specifically */
            @supports (-webkit-touch-callout: none) {
              /* Form controls and HeroUI elements */
              input,
              select,
              textarea,
              button,
              .heroui-autocomplete-input,
              [role="combobox"] input,
              .heroui-autocomplete input {
                font-size: 16px !important;
              }
              
              /* Target input types individually to ensure coverage */
              input[type="text"],
              input[type="email"],
              input[type="password"],
              input[type="search"],
              input[type="tel"],
              input[type="number"],
              input[type="url"] {
                font-size: 16px !important;
              }
              
              /* For hover states in necessary */
              input:hover,
              select:hover,
              textarea:hover {
                font-size: 16px !important;
              }
              
              /* Specific to ProgramStudySelect component */
              .program-study-select-container input,
              .program-study-select-container select {
                font-size: 16px !important;
              }
            }
          `
        }} />
      </head>
      <body className={clsx("font-sans antialiased", fontSans.className)}>
        <SessionProvider session={session}>
          <LanguageWrapper>
            <Providers>
              {children}
              <Toaster 
                position="top-right"
                richColors
                expand
                closeButton
              />
            </Providers>
          </LanguageWrapper>
        </SessionProvider>
        
        {/* Google Tag Manager */}
        <GoogleTagManager gtmId="GTM-PF652WZZ" />
        <GoogleAnalytics gaId="G-6EMP12ZBF2" />
        
        {/* Google Ads Conversion Tracking */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'AW-*********');
              gtag('event', 'conversion', {
                'send_to': 'AW-*********/JE2ACNX27eYZEO60tswD'
              });
            `
          }}
        />
        
        {/* Optional: iOS auto-zoom prevention script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Only run on iOS devices
              if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
                // Check if a device is iOS and apply maximum-scale=1 to the meta viewport
                const metaViewport = document.querySelector('meta[name="viewport"]');
                if (metaViewport) {
                  let content = metaViewport.getAttribute('content');
                  if (content && !content.includes('maximum-scale=1')) {
                    content = content.replace(/maximum-scale=[0-9\.]+/g, 'maximum-scale=1');
                    if (!content.includes('maximum-scale')) {
                      content = content + ', maximum-scale=1';
                    }
                    metaViewport.setAttribute('content', content);
                  }
                }
              }
            `
          }}
        />
      </body>
    </html>
  );
}