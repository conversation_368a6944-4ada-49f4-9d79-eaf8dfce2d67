package lib

import (
	"math/rand"
	"sync"
	"time"

	"github.com/oklog/ulid"
)

var ulidMutex sync.Mutex

func GenerateULID() string {
	ulidMutex.Lock()
	defer ulidMutex.Unlock()

	var ulidValue ulid.ULID
	var err error
	retries := 3 // Maximum number of retries
	for attempt := 0; attempt < retries; attempt++ {
		t := time.Now()
		entropy := ulid.Monotonic(rand.New(rand.NewSource(t.UnixNano())), 0)
		ulidValue, err = ulid.New(ulid.Timestamp(t), entropy)
		if err == nil {
			break // Break the loop if a valid ULID is generated
		}
		// Exponential backoff with jitter
		delay := time.Duration((1 << uint(attempt)) * 1000) // exponential backoff in milliseconds
		delay = delay + time.Duration(rand.Intn(1000))      // add jitter up to 1 second
		time.Sleep(delay * time.Millisecond)
	}
	return ulidValue.String()
}
