// components/sections/ResultsPreviewSection.tsx
import React from 'react';
import { Play, BookO<PERSON>, Users, CheckCircle } from 'lucide-react';

const ResultsPreviewSection: React.FC = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full text-indigo-800 text-sm font-medium mb-6">
            <img src="https://cdn.terang.ai/landingpage-assets/bitcoin-growth.svg" alt="Analytics" className="w-4 h-4 mr-2" />
            Hasil yang Mendalam & Actionable
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Hasil & Feedback yang 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600"> Mendalam</span>
          </h2>
          <p className="text-xl text-gray-600">
            Dapatkan analisis komprehensif dan rekomendasi pembelajaran yang dipersonalisasi untuk kesuksesan kamu
          </p>
        </div>

        <div className="grid md:grid-cols-4 gap-8">
          {/* Mindmap Card */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 group hover:shadow-2xl transition-all transform hover:scale-105">
            <div className="flex items-center mb-6">
              <div className="mr-4 group-hover:scale-110 transition-transform w-24 h-24 flex items-center justify-center">
                <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="Brain" className="max-w-24 max-h-24 w-auto h-auto object-contain" />
              </div>
              <h3 className="text-2xl font-bold">Mindmap</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center text-sm bg-green-50 p-3 rounded-xl">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                <span className="font-medium">Communication Skills ✨</span>
              </div>
              <div className="flex items-center text-sm bg-yellow-50 p-3 rounded-xl">
                <div className="w-5 h-5 border-2 border-yellow-500 rounded mr-3"></div>
                <span className="font-medium">Technical Knowledge 📚</span>
              </div>
              <div className="flex items-center text-sm bg-red-50 p-3 rounded-xl">
                <div className="w-5 h-5 border-2 border-red-500 rounded mr-3"></div>
                <span className="font-medium">Leadership Experience 👑</span>
              </div>
            </div>
          </div>

          {/* Resources Card */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 group hover:shadow-2xl transition-all transform hover:scale-105">
            <div className="flex items-center mb-6">
              <div className="mr-4 group-hover:scale-110 transition-transform w-24 h-24 flex items-center justify-center">
                <img src="https://cdn.terang.ai/landingpage-assets/ai-book.svg" alt="Book" className="max-w-24 max-h-24 w-auto h-auto object-contain" />
              </div>
              <h3 className="text-2xl font-bold">Resources</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center text-sm bg-blue-50 p-3 rounded-xl">
                <Play className="w-5 h-5 text-blue-500 mr-3" />
                <span className="font-medium">📹 Video: LPDP Interview Tips</span>
              </div>
              <div className="flex items-center text-sm bg-green-50 p-3 rounded-xl">
                <BookOpen className="w-5 h-5 text-green-500 mr-3" />
                <span className="font-medium">📖 Article: Leadership Dev</span>
              </div>
              <div className="flex items-center text-sm bg-purple-50 p-3 rounded-xl">
                <Users className="w-5 h-5 text-purple-500 mr-3" />
                <span className="font-medium">🎓 Course: Communication</span>
              </div>
            </div>
          </div>

          {/* Analytics Card */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 group hover:shadow-2xl transition-all transform hover:scale-105">
            <div className="flex items-center mb-6">
              <div className="mr-4 group-hover:scale-110 transition-transform w-24 h-24 flex items-center justify-center">
                <img src="https://cdn.terang.ai/landingpage-assets/dashboard.svg" alt="Analytics" className="max-w-24 max-h-24 w-auto h-auto object-contain" />
              </div>
              <h3 className="text-2xl font-bold">Analytics</h3>
            </div>
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-xl">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Overall Score</span>
                  <span className="text-lg font-bold text-green-600">7.2/10</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{width: '72%'}}></div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-xl">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Confidence</span>
                  <span className="text-lg font-bold text-orange-600">6.5/10</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full" style={{width: '65%'}}></div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-xl">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Clarity</span>
                  <span className="text-lg font-bold text-blue-600">8.1/10</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{width: '81%'}}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Plan Card */}
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 group hover:shadow-2xl transition-all transform hover:scale-105">
            <div className="flex items-center mb-6">
              <div className="mr-4 group-hover:scale-110 transition-transform w-24 h-24 flex items-center justify-center">
                <img src="https://cdn.terang.ai/landingpage-assets/target.svg" alt="Target" className="max-w-24 max-h-24 w-auto h-auto object-contain" />
              </div>
              <h3 className="text-2xl font-bold">Action Plan</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-start text-sm bg-red-50 p-3 rounded-xl">
                <div className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</div>
                <span className="font-medium">Focus on storytelling techniques untuk memperkuat jawaban</span>
              </div>
              <div className="flex items-start text-sm bg-yellow-50 p-3 rounded-xl">
                <div className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</div>
                <span className="font-medium">Practice body language dan eye contact improvement</span>
              </div>
              <div className="flex items-start text-sm bg-green-50 p-3 rounded-xl">
                <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</div>
                <span className="font-medium">Study Indonesian development challenges lebih mendalam</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResultsPreviewSection;