-- First backup the current data
CREATE TABLE referral_configurations_json_backup AS 
SELECT * FROM referral_configurations;

-- Drop the view
DROP VIEW IF EXISTS effective_referral_configurations;

-- Drop the existing table
DROP TABLE referral_configurations;

-- Recreate the original table structure
CREATE TABLE referral_configurations (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    type referral_type NOT NULL,
    referrer_reward_type VARCHAR(50) NOT NULL,
    referrer_reward_amount NUMERIC(10, 2) NOT NULL,
    referee_reward_type VARCHAR(50) NOT NULL,
    referee_reward_amount NUMERIC(10, 2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create the original index
CREATE INDEX idx_referral_configs_type ON referral_configurations(type);

-- Add the original trigger
CREATE TRIGGER referral_configs_modified_at
    BEFORE UPDATE ON referral_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

-- Migrate data back to the original structure
-- Note: This will only migrate the default (non-user-specific) configurations
INSERT INTO referral_configurations (
    id,
    type,
    referrer_reward_type,
    referrer_reward_amount,
    referee_reward_type,
    referee_reward_amount,
    is_active,
    created_at,
    modified_at
)
SELECT 
    id,
    type,
    config->'referrer'->>'type' as referrer_reward_type,
    (config->'referrer'->>'amount')::NUMERIC(10,2) as referrer_reward_amount,
    config->'referee'->>'type' as referee_reward_type,
    (config->'referee'->>'amount')::NUMERIC(10,2) as referee_reward_amount,
    is_active,
    created_at,
    modified_at
FROM referral_configurations_json_backup
WHERE user_id IS NULL;

-- Drop the backup table
DROP TABLE referral_configurations_json_backup;