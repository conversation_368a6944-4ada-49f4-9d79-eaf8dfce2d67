package controller

import (
	"database/sql"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/users"
	"github.com/terang-ai/backend-service/repository"
)

type UserController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "user", "user")
}

func NewUserController(db *sql.DB, redis *redis.Client) *UserController {
	return &UserController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "users", "user"), // Initialize with specific table and entity name
		Entity: "user",
	}
}

func NewUserVerificationTokenController(db *sql.DB, redis *redis.Client) *UserController {
	return &UserController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "user_verification_requests", "user_verification_request"), // Initialize with specific table and entity name
		Entity: "user_verification_request",
	}
}

func (c *UserController) DeleteUser(ctx *gin.Context) {
	var uri model.UserUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete User
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete User failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "User not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete User successfully"})
}

func (c *UserController) GetAllUsers(ctx *gin.Context) {
	var entity model.User
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "users retrieved successfully", "_pagination": paginationInfo})
}

func (c *UserController) GetOneUser(ctx *gin.Context) {
	var uri model.UserUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.User
	entity := &model.User{}

	// Call repository method to retrieve one User by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "User not found"})
		return
	}

	// Type assertion to *model.User
	if userEntity, ok := result.(*model.User); ok {
		// Update entity with fetched data
		*entity = *userEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get User successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.User failed", "msg": "internal error"})
}

func (c *UserController) InsertUser(ctx *gin.Context) {
	var post model.PostUser
	var entity model.User
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert User failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert User successfully"})
}

func (c *UserController) UpdateUser(ctx *gin.Context) {
	var updates model.UpdateUser
	var uri model.UserUri
	var entity model.User

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update User by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update User", "error": err.Error()})
		return
	}

	// Type assertion to *model.User
	if updatedUser, ok := updated.(*model.User); ok {
		// Update entity with fetched data after update
		entity = *updatedUser
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "User updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.User failed", "msg": "internal error"})
}

func (c *UserController) UpdateUserByEmail(ctx *gin.Context) {
	var updates model.UpdateUser
	var uri model.UserEmailUri
	var entity model.User

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update User by ID
	updated, err := c.Repo.UpdateBySomething("email", uri.EMAIL, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update User", "error": err.Error()})
		return
	}

	// Type assertion to *model.User
	if updatedUser, ok := updated.(*model.User); ok {
		// Update entity with fetched data after update
		entity = *updatedUser
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "User updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.User failed", "msg": "internal error"})
}

func (c *UserController) GetOneUserByEmail(ctx *gin.Context) {
	var uri model.UserEmailUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.User
	entity := &model.User{}

	// Call repository method to retrieve one User by artist ID
	result, cache_status, err := c.Repo.GetOneBySomethingWithCache(ctx.Request.Context(), "email", uri.EMAIL, entity)
	if err != nil {
		log.Println(err)
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "User not found"})
		return
	}

	// Type assertion to *model.User
	if userEntity, ok := result.(*model.User); ok {
		// Update entity with fetched data
		*entity = *userEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "is_cache_hit": cache_status, "msg": "get User successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.User failed", "msg": "internal error"})
}

func (c *UserController) GetUserVerificationByToken(ctx *gin.Context) {
	var uri model.UserTokenUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.User
	entity := &model.UserVerificationRequest{}

	// Call repository method to retrieve one User by artist ID
	result, err := c.Repo.GetOneBySomething("token", uri.TOKEN, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "User not found"})
		return
	}

	// Type assertion to *model.User
	if userEntity, ok := result.(*model.UserVerificationRequest); ok {
		// Update entity with fetched data
		*entity = *userEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get User token successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.UserToken failed", "msg": "internal error"})
}

func (c *UserController) InsertUserVerificationToken(ctx *gin.Context) {
	var post model.PostUserVerificationRequest
	var entity model.UserVerificationRequest
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.InsertSerialToken(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert User verification token failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert User verification token successfully"})
}

func (c *UserController) UpdateUserVerificationByToken(ctx *gin.Context) {
	var updates model.UpdateUserVerificationRequest
	var uri model.UserTokenUri
	var entity model.UserVerificationRequest

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update User by ID
	updated, err := c.Repo.UpdateBySomething("token", uri.TOKEN, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update User", "error": err.Error()})
		return
	}

	// Type assertion to *model.User
	if updatedUser, ok := updated.(*model.UserVerificationRequest); ok {
		// Update entity with fetched data after update
		entity = *updatedUser
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "User updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.User failed", "msg": "internal error"})
}
