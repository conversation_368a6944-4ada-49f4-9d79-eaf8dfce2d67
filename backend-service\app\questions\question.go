package questions

import (
	"database/sql"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/terang-ai/backend-service/lib"
)

func RegisterRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.POST("/questions", insertQuestion(dbx))
	}
}

type QuestionInput struct {
	ID      string `json:"id" binding:"required"`
	Content string `json:"content" binding:"required"` // Changed this line
}

func insertQuestion(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var input QuestionInput

		if err := c.ShouldBind<PERSON>(&input); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
			return
		}

		// Generate ID if not provided
		if input.ID == "" {
			input.ID = lib.GenerateULID()
		}

		// Insert question - using content directly as it's already JSON
		query := `
            INSERT INTO rag_raw_questions (
                id, content
            ) VALUES (
                $1, $2::jsonb
            ) RETURNING id`

		var result sql.Result
		result, err = tx.Exec(
			query,
			input.ID,
			input.Content, // Using content directly as it's already JSON
		)

		if err != nil {
			tx.Rollback()
			log.Printf("Error inserting question: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert question"})
			return
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			tx.Rollback()
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
			return
		}

		// Get affected rows
		rows, _ := result.RowsAffected()

		c.JSON(http.StatusCreated, gin.H{
			"message":  "Question created successfully",
			"id":       input.ID,
			"affected": rows,
		})
	}
}
