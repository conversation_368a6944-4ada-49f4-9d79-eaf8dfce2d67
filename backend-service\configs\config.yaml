db:
  # username: "gnome<PERSON>"
  # password: "Capcapcap123"
  # host: "localhost"
  # dbname: "backend_service"
  # port: 6432
  username: "gnomefin"
  password: "Capcapcap123"
  host: "**************"
  dbname: "backend_service"
  port: 5433 # can be ha proxy or pgbouncer test test

redis:
  # username: ""
  # password: "cassieinkl"
  # host: localhost
  # port: 6379
  username: ""
  password: "cassie<PERSON>l"
  host: "localhost"
  port: 6379

api:
  key: "cassieinkl"

run:
  port: 8081
  healthport: 8082

mailgun:
  environment: "local"
  domain: "mail.terang.ai"
  api_key: "**************************************************"
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"

midtrans:
  environment: "local"  # or production, local
  api_key: "IRIS-6737b09e-8860-4a6e-914f-fd224c0b56da"