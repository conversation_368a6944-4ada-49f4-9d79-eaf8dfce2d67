package exams

import (
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	exams "github.com/terang-ai/backend-service/controller/exams"
)

func RegisterRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	examsCtrl := exams.NewExamsController(dbx, redis)

	// Group for ExamSessions
	examsBaseGroup := r.Group("/v1")
	{
		// Exam Sessions routes
		examsBaseGroup.POST("/exam-sessions", examsCtrl.InsertExamSessions)
		examsBaseGroup.GET("/exam-sessions/users/:user_id", examsCtrl.GetAllExamSessionsByUserId)
		examsBaseGroup.GET("/exam-sessions/last/:exam_id/:user_id/exam", examsCtrl.GetLastExamSessionsByExamIdUserId)
		examsBaseGroup.POST("/exam-sessions/last/:exam_id/:user_id/practice", examsCtrl.GetLastTrialSessionsByExamIdUserId)
		examsBaseGroup.PUT("/exam-sessions/:session_id", examsCtrl.UpdateExamSessions)
		examsBaseGroup.GET("/exam-sessions/:session_id", examsCtrl.GetExamSessionBySessionId)
		examsBaseGroup.DELETE("/exam-sessions/:id", examsCtrl.DeleteExamSessions)

		// Exam Scores routes
		examsBaseGroup.POST("/exam-scores", examsCtrl.InsertExamScores)
		examsBaseGroup.PUT("/exam-scores/:id", examsCtrl.UpdateExamScores)
		examsBaseGroup.DELETE("/exam-scores/:id", examsCtrl.DeleteExamScores)
		examsBaseGroup.GET("/exam-scores/:session_id", examsCtrl.GetExamScoresBySessionId)
	}
}
