package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/terang-ai/backend-service/app"
)

var (
	SemanticVersion string
	BuildHash       string
)

func main() {
	// Define command-line flags
	generateSchema := flag.Bool("generate-schema", false, "Generate consolidated schema file from migrations")
	schemaOutput := flag.String("schema-output", "schema/latest_schema.sql", "Output path for the generated schema file")
	flag.Parse()

	// Setup logging
	log.SetFlags(log.Ldate | log.Ltime | log.LUTC | log.Lshortfile)

	// Print version information
	log.Printf("Starting application - Version: %s, Build: %s", SemanticVersion, BuildHash)

	// Initialize app for connection
	var a app.App

	// Create database connections (needed for both normal startup and schema generation)
	if err := createConnections(&a); err != nil {
		log.Fatalf("Failed to create connections: %v", err)
	}

	// Setup migrations
	debug := os.Getenv("DEBUG") == "true"
	if err := a.SetupMigrations(debug); err != nil {
		log.Fatalf("Failed to setup migrations: %v", err)
	}

	// Check if we should generate schema
	if *generateSchema {
		log.Printf("Generating consolidated schema file at %s...", *schemaOutput)

		// Ensure the schema directory exists
		schemaDir := filepath.Dir(*schemaOutput)
		if err := os.MkdirAll(schemaDir, 0755); err != nil {
			log.Fatalf("Failed to create schema directory: %v", err)
		}

		// Generate the schema
		if err := a.Migrator.GenerateLatestSchema(*schemaOutput); err != nil {
			log.Fatalf("Failed to generate schema: %v", err)
		}

		log.Printf("Schema generation completed successfully")
		return
	}

	// Continue with normal application initialization
	if err := initializeApp(&a); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// Setup routes and run the server
	a.Routes()
	a.Run()
}

func initializeApp(a *app.App) error {
	start := time.Now()
	log.Println("Initializing application...")

	// Database connections already created in main()

	// Run migrations
	debug := os.Getenv("DEBUG") == "true"
	if err := a.RunMigrations(debug); err != nil {
		return fmt.Errorf("migration process failed: %v", err)
	}

	log.Printf("Application initialized successfully in %v", time.Since(start))
	return nil
}

func createConnections(a *app.App) error {
	log.Println("Creating database connections...")

	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered from panic in createConnections: %v", r)
			os.Exit(1)
		}
	}()

	a.CreateConnection()
	return nil
}
