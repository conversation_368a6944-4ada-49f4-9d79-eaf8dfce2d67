-- Create the normalized schema for exam questions with hints and explanations

-- Main questions table with composite primary key
CREATE TABLE IF NOT EXISTS exam_questions (
    id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    title TEXT NOT NULL,
    PRIMARY KEY (id, exam_id),
    CONSTRAINT fk_questions_exam FOREIGN KEY (exam_id) REFERENCES available_exams(id)
);

-- Question metadata table
CREATE TABLE IF NOT EXISTS question_metadata (
    id SERIAL PRIMARY KEY,
    question_id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    name VARCHAR(100) NOT NULL,
    value TEXT NOT NULL,
    level INT NOT NULL DEFAULT 0,
    CONSTRAINT fk_question_metadata_question FOREIGN KEY (question_id, exam_id) REFERENCES exam_questions(id, exam_id)
);

-- Options table with composite primary key
CREATE TABLE IF NOT EXISTS question_options (
    id VARCHAR(1024) NOT NULL,
    question_id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    content TEXT NOT NULL,
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (id, question_id, exam_id),
    CONSTRAINT fk_options_question FOREIGN KEY (question_id, exam_id) REFERENCES exam_questions(id, exam_id)
);

-- Instructions table
CREATE TABLE IF NOT EXISTS question_instructions (
    id SERIAL PRIMARY KEY,
    question_id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    content TEXT NOT NULL,
    display_order INT NOT NULL,
    CONSTRAINT fk_instructions_question FOREIGN KEY (question_id, exam_id) REFERENCES exam_questions(id, exam_id)
);

-- Hints table
CREATE TABLE IF NOT EXISTS question_hints (
    id SERIAL PRIMARY KEY,
    question_id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    content TEXT NOT NULL,
    display_order INT NOT NULL,
    CONSTRAINT fk_hints_question FOREIGN KEY (question_id, exam_id) REFERENCES exam_questions(id, exam_id)
);

-- Explanations table
CREATE TABLE IF NOT EXISTS question_explanations (
    id SERIAL PRIMARY KEY,
    question_id VARCHAR(1024) NOT NULL,
    exam_id VARCHAR(1024) NOT NULL,
    content TEXT NOT NULL,
    display_order INT NOT NULL,
    CONSTRAINT fk_explanations_question FOREIGN KEY (question_id, exam_id) REFERENCES exam_questions(id, exam_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_exam_questions_exam_id ON exam_questions(exam_id);
CREATE INDEX IF NOT EXISTS idx_question_metadata_question_id ON question_metadata(question_id, exam_id);
CREATE INDEX IF NOT EXISTS idx_question_metadata_name_value ON question_metadata(name, value);
CREATE INDEX IF NOT EXISTS idx_question_options_question_id ON question_options(question_id, exam_id);
CREATE INDEX IF NOT EXISTS idx_question_options_is_correct ON question_options(is_correct);
CREATE INDEX IF NOT EXISTS idx_question_instructions_question_id ON question_instructions(question_id, exam_id);
CREATE INDEX IF NOT EXISTS idx_question_hints_question_id ON question_hints(question_id, exam_id);
CREATE INDEX IF NOT EXISTS idx_question_explanations_question_id ON question_explanations(question_id, exam_id);

-- Create stored procedure for processing question data with comprehensive error handling
CREATE OR REPLACE PROCEDURE process_question_data(p_exam_id VARCHAR(1024), question_data JSONB)
LANGUAGE plpgsql
AS $$
DECLARE
    q JSONB;
    m JSONB;
    o JSONB;
    i JSONB;
    h JSONB;
    e JSONB;
    instruction_idx INT;
    hint_idx INT;
    exp_idx INT;
    instruction_content TEXT;
    hint_content TEXT;
    q_id VARCHAR(1024);
    option_id VARCHAR(1024);
BEGIN
    -- Loop through each question
    FOR q IN SELECT jsonb_array_elements(question_data)
    LOOP
        -- Use the ID exactly as it appears in the data
        q_id := q->>'id';
        
        -- Insert question with upsert
        INSERT INTO exam_questions(id, exam_id, title)
        VALUES (
            q_id,
            p_exam_id,
            COALESCE((q->'question'->0->'contents'->0->>'content'), 
                     (q->'question'->0->>'content'), 
                     (q->>'question'), 
                     'No title')
        ) ON CONFLICT (id, exam_id) DO UPDATE SET
            title = COALESCE((q->'question'->0->'contents'->0->>'content'), 
                            (q->'question'->0->>'content'), 
                            (q->>'question'), 
                            'No title');
        
        -- Clear existing metadata before inserting new
        DELETE FROM question_metadata 
        WHERE question_metadata.question_id = q_id AND question_metadata.exam_id = p_exam_id;
        
        -- Insert metadata with null check
        IF q ? 'metadata' AND jsonb_typeof(q->'metadata') = 'array' THEN
            FOR m IN SELECT jsonb_array_elements(q->'metadata')
            LOOP
                -- Skip metadata with null values
                IF m->>'name' IS NOT NULL AND m->>'value' IS NOT NULL THEN
                    INSERT INTO question_metadata(question_id, exam_id, name, value, level)
                    VALUES (
                        q_id,
                        p_exam_id,
                        m->>'name',
                        m->>'value',
                        COALESCE((m->>'level')::INT, 0)
                    );
                END IF;
            END LOOP;
        END IF;
        
        -- Clear existing options before inserting new
        DELETE FROM question_options 
        WHERE question_options.question_id = q_id AND question_options.exam_id = p_exam_id;
        
        -- Insert options if they exist
        IF q ? 'options' AND q->'options' ? 'values' AND jsonb_typeof(q->'options'->'values') = 'array' THEN
            FOR o IN SELECT jsonb_array_elements(q->'options'->'values')
            LOOP
                -- Use the option ID exactly as it appears in the data
                option_id := o->>'id';
                
                -- Only insert if we have an ID
                IF option_id IS NOT NULL THEN
                    -- Use ON CONFLICT to handle duplicate keys
                    INSERT INTO question_options(id, question_id, exam_id, content, is_correct)
                    VALUES (
                        option_id,
                        q_id,
                        p_exam_id,
                        COALESCE((o->'data'->0->'contents'->0->>'content'), 
                                 (o->'data'->0->>'content'),
                                 (o->>'content'),
                                 'No content'),
                        COALESCE((o->>'is_correct')::BOOLEAN, FALSE)
                    )
                    ON CONFLICT (id, question_id, exam_id) DO UPDATE SET
                        content = COALESCE((o->'data'->0->'contents'->0->>'content'), 
                                          (o->'data'->0->>'content'),
                                          (o->>'content'),
                                          'No content'),
                        is_correct = COALESCE((o->>'is_correct')::BOOLEAN, FALSE);
                END IF;
            END LOOP;
        END IF;
        
        -- Clear existing instructions before inserting new
        DELETE FROM question_instructions 
        WHERE question_instructions.question_id = q_id AND question_instructions.exam_id = p_exam_id;
        
        -- Insert instructions with content check, handling different formats
        IF q ? 'instruction' THEN
            instruction_idx := 1;
            
            IF jsonb_typeof(q->'instruction') = 'array' THEN
                FOR i IN SELECT jsonb_array_elements(q->'instruction')
                LOOP
                    instruction_content := NULL;
                    
                    -- Check different possible structures for instructions
                    IF jsonb_typeof(i) = 'string' THEN
                        -- Direct string in array
                        instruction_content := i#>>'{}';
                    ELSIF i ? 'contents' AND jsonb_array_length(i->'contents') > 0 THEN
                        -- Complex structure with contents array
                        instruction_content := i->'contents'->0->>'content';
                    ELSIF i ? 'content' THEN
                        -- Simple object with content field
                        instruction_content := i->>'content';
                    END IF;
                    
                    IF instruction_content IS NOT NULL THEN
                        INSERT INTO question_instructions(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            instruction_content,
                            instruction_idx
                        );
                        instruction_idx := instruction_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'instruction') = 'string' THEN
                -- Single string instruction
                INSERT INTO question_instructions(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'instruction',
                    instruction_idx
                );
            END IF;
        END IF;
        
        -- Clear existing hints before inserting new
        DELETE FROM question_hints 
        WHERE question_hints.question_id = q_id AND question_hints.exam_id = p_exam_id;
        
        -- Insert hints with content check, handling different formats
        IF q ? 'hints' THEN
            hint_idx := 1;
            
            IF jsonb_typeof(q->'hints') = 'array' THEN
                FOR h IN SELECT jsonb_array_elements(q->'hints')
                LOOP
                    hint_content := NULL;
                    
                    -- Check different possible structures for hints
                    IF jsonb_typeof(h) = 'string' THEN
                        -- Direct string in array
                        hint_content := h#>>'{}';
                    ELSIF h ? 'contents' AND jsonb_array_length(h->'contents') > 0 THEN
                        -- Complex structure with contents array
                        hint_content := h->'contents'->0->>'content';
                    ELSIF h ? 'content' THEN
                        -- Simple object with content field
                        hint_content := h->>'content';
                    END IF;
                    
                    IF hint_content IS NOT NULL THEN
                        INSERT INTO question_hints(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            hint_content,
                            hint_idx
                        );
                        hint_idx := hint_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'hints') = 'string' THEN
                -- Single string hint
                INSERT INTO question_hints(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'hints',
                    hint_idx
                );
            END IF;
        END IF;
        
        -- Clear existing explanations before inserting new
        DELETE FROM question_explanations 
        WHERE question_explanations.question_id = q_id AND question_explanations.exam_id = p_exam_id;
        
        -- Insert explanations with content check, handling different formats
        IF q ? 'explanation' THEN
            exp_idx := 1;
            
            IF jsonb_typeof(q->'explanation') = 'array' THEN
                FOR e IN SELECT jsonb_array_elements(q->'explanation')
                LOOP
                    IF jsonb_typeof(e) = 'string' THEN
                        -- Direct string in array
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e#>>'{}',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    ELSIF e ? 'contents' AND jsonb_array_length(e->'contents') > 0 AND e->'contents'->0->>'content' IS NOT NULL THEN
                        -- Complex structure with contents array
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e->'contents'->0->>'content',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    ELSIF e ? 'content' AND e->>'content' IS NOT NULL THEN
                        -- Simple object with content field
                        INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                        VALUES (
                            q_id,
                            p_exam_id,
                            e->>'content',
                            exp_idx
                        );
                        exp_idx := exp_idx + 1;
                    END IF;
                END LOOP;
            ELSIF jsonb_typeof(q->'explanation') = 'string' THEN
                -- Single string explanation
                INSERT INTO question_explanations(question_id, exam_id, content, display_order)
                VALUES (
                    q_id,
                    p_exam_id,
                    q->>'explanation',
                    exp_idx
                );
            END IF;
        END IF;
    END LOOP;
EXCEPTION WHEN OTHERS THEN
    -- Log error details
    RAISE WARNING 'Error in process_question_data: %, exam_id: %', SQLERRM, p_exam_id;
END;
$$;

-- Create trigger function with error handling
CREATE OR REPLACE FUNCTION sync_question_data_trigger() RETURNS TRIGGER AS $$
BEGIN
    -- Skip processing if data is NULL or empty array
    IF NEW.data IS NULL OR NEW.data = '[]'::jsonb THEN
        RETURN NEW;
    END IF;
    
    BEGIN
        -- Call the procedure to normalize the data
        CALL process_question_data(NEW.exam_id, NEW.data);
    EXCEPTION WHEN OTHERS THEN
        -- Log the error but don't fail the transaction
        RAISE WARNING 'Error normalizing question data for exam_id %: %', NEW.exam_id, SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER exam_question_data_sync
AFTER INSERT OR UPDATE ON exam_question_answer_hints
FOR EACH ROW EXECUTE FUNCTION sync_question_data_trigger();

-- Create a function to process batches properly
CREATE OR REPLACE FUNCTION process_exam_data_in_batches() RETURNS void AS $$
DECLARE
    rec RECORD;
    batch_counter INT := 0;
    exam_cursor CURSOR FOR SELECT exam_id, data FROM exam_question_answer_hints;
BEGIN
    OPEN exam_cursor;
    
    LOOP
        -- Fetch next record
        FETCH exam_cursor INTO rec;
        EXIT WHEN NOT FOUND;
        
        -- Process this batch in a separate transaction
        BEGIN
            RAISE NOTICE 'Processing exam_id: %', rec.exam_id;
            CALL process_question_data(rec.exam_id, rec.data);
            batch_counter := batch_counter + 1;
            RAISE NOTICE 'Processed exam: %, total: %', rec.exam_id, batch_counter;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error processing exam_id %: %', rec.exam_id, SQLERRM;
        END;
    END LOOP;
    
    CLOSE exam_cursor;
    
    RAISE NOTICE 'Finished processing % exams', batch_counter;
END;
$$ LANGUAGE plpgsql;

-- Create a comment to notify about the batch processing function
COMMENT ON FUNCTION process_exam_data_in_batches() IS 
'Run this function to process all exam data in batches: SELECT process_exam_data_in_batches();';

SELECT process_exam_data_in_batches();