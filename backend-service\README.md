# Overview
This repository is intended to make all in one restful API with API key to terang AI. This is going to be a high-performing and high-availability design that I will propose.

## Well...
The Problem:
We aim to expose our service similarly to an edtech customer-facing app. This involves several considerations:

- The backend will serve as a RESTful API capable of handling various requests related to the available exams, including quiz/latihan, gamification.
- The backend must be highly optimized to prevent frequent queries on customer-facing pages, such as the homepage where all exams are displayed.
- There will be multiple integrations with other services, including an AI-based engine (maybe TBD). A modular approach to building the backend is essential.

Design Elaboration:

- Our backend API, specifically the Golang compiled version (binary), will be packaged as a tar archive or container image. It can be managed using a runtime manager (systemd) or deployed in containers such as Docker or Kubernetes, with minimal adjustments required to the CI/CD pipeline. This setup is already in place.
- To optimize the client-facing app, we will implement a caching strategy. Using the "Read Through" principle, we will focus on read operations rather than writes.
- HAProxy will be used for high-performance load balancing of database connections.
- PgBouncer will be employed for connection pooling. While PgBouncer may increase latency and response time under high load due to its queuing mechanism, it efficiently manages connections without dropping requests. PostgreSQL, by design, lacks flexibility in handling excess connections, making PgBouncer a suitable choice for this purpose.

## Docker Setup
Please install docker, and create 2 volumes:

```
docker volume create pgdata
docker volume create pgadmin
```

Then create 1 network:
```
docker network create my_network
```

Now we can make everything is up and running:
```
docker compose up -d
```

## Mac Setup

You need `psql`, therefore, please install:

```
brew install postgresql
brew install hey
sudo ulimit -n 6049
sudo sysctl -w kern.ipc.somaxconn=1024 # for handling 1k connection
```

## Data Init or Seeding

Create database:
```
psql -h localhost -U postgres -p 6435 -c "CREATE DATABASE backend_service;"
```

And then execute the schema.sql
```
psql -h localhost -U postgres -p 6435 -d backend_service -f schema.sql
```

## Benchmark Database

First you need to init `pgbench`
```
pgbench -i -h 127.0.0.1 -p 6435 -U postgres backend_service
```

Run without any `pgbouncer` or `HAProxy` by using Postgresql Port (5432):
```
pgbench -c 1000 -T 60 backend_service -h 127.0.0.1 -p 5432 -U postgres
```

You will see _max connection is exceeded or too many client_.

Now, we can see proceed with **HAProxy Port -> Pgbouncer**:
```
pgbench -c 1000 -T 60 backend_service -h 127.0.0.1 -p 6435 -U postgres
```

Now I got no transaction failed:
```
pgbench (15.7 (Homebrew), server 16.3 (Debian 16.3-1.pgdg120+1))
starting vacuum...end.
transaction type: <builtin: TPC-B (sort of)>
scaling factor: 1
query mode: simple
number of clients: 1000
number of threads: 1
maximum number of tries: 1
duration: 60 s
number of transactions actually processed: 38854
number of failed transactions: 0 (0.000%)
latency average = 1527.644 ms
initial connection time = 2164.710 ms
tps = 654.602774 (without initial connection time)
```

## Run App

It is simple, we can just perform:
```
go run main.go
```

Then it will automatically run the app.

## Benchmark API and Cache

Example without cache by using API v1
```
hey -n 5000 -c 500 -H "X-API-KEY: cassieinkl" "http://localhost:8081/v1/available-exams?pageSize=2000&page=1" > v1_available-exams_benchmark.txt
```

In V2 I have introduced the redis read through:
``` 
hey -n 5000 -c 500 -H "X-API-KEY: cassieinkl" "http://localhost:8081/v2/available-exams?pageSize=2000&page=1" > v2_available-exams_benchmark.txt
```

still constructing this documentation, help wanted, lol, but the flag `--generate-schema` is yet another to generate latest schema