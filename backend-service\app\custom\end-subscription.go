package custom

import (
	"database/sql"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/robfig/cron/v3"
)

// StartSubscriptionMaintenanceCron initializes and starts the subscription maintenance cron
func StartSubscriptionMaintenanceCron(r *gin.Engine, db *sqlx.DB) {
	maintenance := &SubscriptionMaintenance{db: db}

	// Set up cron job
	c := cron.New()

	// Run every minute for testing (you can change to "0 0 * * *" for daily at midnight)
	_, err := c.AddFunc("* * * * *", func() {
		if err := maintenance.EndUnpaidSubscriptions(); err != nil {
			log.Printf("Error running subscription maintenance: %v", err)
		}
	})

	if err != nil {
		log.Printf("Error setting up subscription maintenance cron: %v", err)
		return
	}

	// Start the cron scheduler
	c.Start()

	// Add maintenance endpoint for manual triggering (optional)
	r.POST("/admin/maintenance/subscriptions", func(c *gin.Context) {
		if err := maintenance.EndUnpaidSubscriptions(); err != nil {
			log.Printf("Error running manual subscription maintenance: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to run subscription maintenance"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Subscription maintenance completed successfully"})
	})
}

type SubscriptionMaintenance struct {
	db *sqlx.DB
}

func (sm *SubscriptionMaintenance) EndUnpaidSubscriptions() error {
	tx, err := sm.db.Beginx()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Direct update query without CTE
	query := `
		UPDATE user_subscriptions 
		SET 
			tier_id = 'free_tier_001',
			payment_status = 'PAID',
			auto_renewal = false,
			end_date = NULL,
			modified_at = CURRENT_TIMESTAMP
		WHERE 
			is_active = true 
			AND end_date <= CURRENT_TIMESTAMP
			AND payment_status != 'PAID'
		RETURNING id, user_id;
	`

	type EndedSubscription struct {
		ID     string `db:"id"`
		UserID string `db:"user_id"`
	}

	var endedSubscriptions []EndedSubscription
	err = tx.Select(&endedSubscriptions, query)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	// If there are ended subscriptions, create audit logs
	if len(endedSubscriptions) > 0 {
		auditQuery := `
			INSERT INTO subscription_audit_log 
			(id, subscription_id, action, reason, created_at)
			VALUES (lib.generate_ulid(), $1, 'DOWNGRADED', 'No payment received by end date - downgraded to free tier', CURRENT_TIMESTAMP)
		`

		for _, sub := range endedSubscriptions {
			_, err = tx.Exec(auditQuery, sub.ID)
			if err != nil {
				return err
			}
		}
	}

	return tx.Commit()
}
