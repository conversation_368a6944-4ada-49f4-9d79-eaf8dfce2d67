package db

import (
	"database/sql"
	"fmt"
	"io"
	"os"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gopkg.in/yaml.v2"
)

func ReadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func GetDBConfig() (*Config, error) {
	config := &Config{}

	// Check if environment variables exist
	username := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	dbname := os.Getenv("DB_NAME")

	if username != "" && password != "" && host != "" && port != "" && dbname != "" {
		// Use environment variables
		config.DB.Username = username
		config.DB.Password = password
		config.DB.Host = host
		config.DB.Port = port
		config.DB.DBName = dbname
	} else {
		// Use config.yaml as a fallback
		var err error
		config, err = ReadConfig("configs/config.yaml")
		if err != nil {
			return nil, err
		}
	}

	return config, nil
}

func Connectdb() (*sql.DB, error) {
	config, err := GetDBConfig()
	if err != nil {
		return nil, err
	}

	// First, try with SSL
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=require&binary_parameters=yes",
		config.DB.Username, config.DB.Password, config.DB.Host, config.DB.Port, config.DB.DBName)
	db, err := sql.Open("postgres", connStr)
	if err == nil {
		if err = db.Ping(); err == nil {
			return db, nil
		}
		db.Close()
	}

	// If SSL connection fails, try without SSL
	connStr = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable&binary_parameters=yes",
		config.DB.Username, config.DB.Password, config.DB.Host, config.DB.Port, config.DB.DBName)
	db, err = sql.Open("postgres", connStr)
	if err != nil {
		return nil, err
	}

	// Check if the connection is alive
	if err = db.Ping(); err != nil {
		return nil, err
	}

	return db, nil
}

func Connectdbx() (*sqlx.DB, error) {
	config, err := GetDBConfig()
	if err != nil {
		return nil, err
	}

	// First, try with SSL
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=require&binary_parameters=yes",
		config.DB.Username, config.DB.Password, config.DB.Host, config.DB.Port, config.DB.DBName)
	db, err := sqlx.Open("postgres", connStr)
	if err == nil {
		if err = db.Ping(); err == nil {
			return db, nil
		}
		db.Close()
	}

	// If SSL connection fails, try without SSL
	connStr = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable&binary_parameters=yes",
		config.DB.Username, config.DB.Password, config.DB.Host, config.DB.Port, config.DB.DBName)
	db, err = sqlx.Open("postgres", connStr)
	if err != nil {
		return nil, err
	}

	// Check if the connection is alive
	if err = db.Ping(); err != nil {
		return nil, err
	}

	return db, nil
}
