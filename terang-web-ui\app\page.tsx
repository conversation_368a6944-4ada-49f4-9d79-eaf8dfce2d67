"use client";

import React, { useEffect, useRef, useCallback } from "react";
import { ThemeProvider } from "styled-components";
import { light } from "@/components/landingpage/styles/Themes";

// TypeScript declarations for GSAP and ScrollTrigger - inline
interface ScrollTriggerInstance {
  kill(): void;
  refresh(): void;
  vars: { id?: string; [key: string]: any };
}

interface CustomScrollTrigger {
  config: (config: any) => void;
  refresh: (safe?: boolean) => void;
  getAll: () => ScrollTriggerInstance[];
  addEventListener: (event: string, callback: (trigger?: ScrollTriggerInstance) => void) => void;
  removeEventListener: (event: string, callback: (trigger?: ScrollTriggerInstance) => void) => void;
  kill: () => void;
}

declare global {
  interface Window {
    ScrollTrigger?: CustomScrollTrigger;
    gsap?: any;
  }
}

// Import all components directly (no lazy loading)
import Navigation from "@/components/landingpage/components/Navigation";
import Home from "@/components/landingpage/components/sections/Home";
import Ticket from "@/components/landingpage/components/sections/Ticket";
import Roadmap from "@/components/landingpage/components/sections/Roadmap";
import Showcase from "@/components/landingpage/components/sections/Showcase";
import Products from "@/components/landingpage/components/sections/Products";
import Team from "@/components/landingpage/components/sections/Team";
import Faq from "@/components/landingpage/components/sections/Faq";
import Contact from "@/components/landingpage/components/sections/Contact";
import Footer from "@/components/landingpage/components/Footer";
import AIInterview from "@/components/landingpage/components/sections/AIInterview";
import ScrollToTop from "@/components/landingpage/components/ScrollToTop";
import GlobalStyles from "@/components/landingpage/styles/GlobalStyles";

// Mobile viewport and ScrollTrigger utilities
const MobileViewportFix: React.FC = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Function to set the viewport height CSS custom property
    const setViewportHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    // Detect mobile device
    const isMobile = window.matchMedia('(hover: none)').matches || 
                     window.matchMedia('(pointer: coarse)').matches ||
                     /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    let lastKnownWidth = window.innerWidth;

    // Set initial viewport height
    setViewportHeight();

    if (isMobile) {
      console.log('📱 Applying mobile viewport fixes (no lazy loading)');
      
      // Configure ScrollTrigger for mobile - remove resize from autoRefreshEvents
      if (window.ScrollTrigger) {
        window.ScrollTrigger.config({
          autoRefreshEvents: "visibilitychange,DOMContentLoaded", // Remove "resize" for mobile
          limitCallbacks: true // Improve performance on mobile
        });
      }

      // Function to handle resize with debouncing (only on width changes)
      let resizeTimeout: NodeJS.Timeout;
      const handleMobileResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          const currentWidth = window.innerWidth;
          
          // Only refresh on width changes (not height changes from address bar)
          if (Math.abs(currentWidth - lastKnownWidth) > 10) {
            console.log('📱 Mobile width change detected, refreshing ScrollTrigger');
            lastKnownWidth = currentWidth;
            setViewportHeight();
            
            if (window.ScrollTrigger) {
              window.ScrollTrigger.refresh(true);
            }
          }
        }, 250);
      };

      // Handle orientation changes
      const handleOrientationChange = () => {
        setTimeout(() => {
          setViewportHeight();
          if (window.ScrollTrigger) {
            window.ScrollTrigger.refresh(true);
          }
        }, 500); // Delay to allow browser to settle
      };

      // Listen for resize events (debounced)
      window.addEventListener('resize', handleMobileResize);
      
      // Listen for orientation changes
      window.addEventListener('orientationchange', handleOrientationChange);
      
      // Handle visual viewport changes (for better browser support)
      if (window.visualViewport) {
        const handleVisualViewportChange = () => {
          // Only update if it's a significant change (not just address bar)
          const viewport = window.visualViewport;
          if (viewport && viewport.scale === 1) { // Not zoomed
            setViewportHeight();
          }
        };
        
        window.visualViewport.addEventListener('resize', handleVisualViewportChange);
        
        return () => {
          window.removeEventListener('resize', handleMobileResize);
          window.removeEventListener('orientationchange', handleOrientationChange);
          if (window.visualViewport) {
            window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
          }
          clearTimeout(resizeTimeout);
        };
      } else {
        return () => {
          window.removeEventListener('resize', handleMobileResize);
          window.removeEventListener('orientationchange', handleOrientationChange);
          clearTimeout(resizeTimeout);
        };
      }
    } else {
      // Desktop: normal resize handling
      const handleDesktopResize = () => {
        setViewportHeight();
        if (window.ScrollTrigger) {
          window.ScrollTrigger.refresh();
        }
      };
      
      window.addEventListener('resize', handleDesktopResize);
      
      return () => {
        window.removeEventListener('resize', handleDesktopResize);
      };
    }
  }, []);

  return null;
};

// Interface for the MainSection props
interface MainSectionProps {
  children: React.ReactNode;
}

// More efficient MainSection component
const MainSection: React.FC<MainSectionProps> = ({ children }) => {
  return <>{children}</>;
};

const App: React.FC = () => {
  const componentMounted = useRef(true);

  // Function to safely refresh ScrollTrigger
  const safeScrollTriggerRefresh = useCallback(() => {
    if (typeof window !== 'undefined' && window.ScrollTrigger && componentMounted.current) {
      try {
        setTimeout(() => {
          if (componentMounted.current && window.ScrollTrigger) {
            window.ScrollTrigger.refresh(true); // Safe refresh
          }
        }, 100);
      } catch (error) {
        console.warn('ScrollTrigger refresh error:', error);
      }
    }
  }, []);

  // Cleanup function
  const cleanup = useCallback(() => {
    componentMounted.current = false;
    
    // Clean up ScrollTrigger
    if (typeof window !== 'undefined' && window.ScrollTrigger) {
      try {
        window.ScrollTrigger.getAll().forEach((trigger: ScrollTriggerInstance) => trigger.kill());
      } catch (error) {
        console.warn('ScrollTrigger cleanup error:', error);
      }
    }
  }, []);

  useEffect(() => {
    componentMounted.current = true;
    
    // Measure LCP
    if (typeof window !== 'undefined') {
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (error) {
        console.warn('LCP observer error:', error);
      }
    }
    
    // Initial ScrollTrigger refresh after all components are mounted
    const initialRefreshTimer = setTimeout(() => {
      safeScrollTriggerRefresh();
    }, 300); // Give time for all components to mount and render
    
    // Add error boundary for ScrollTrigger
    const handleError = (event: ErrorEvent) => {
      if (event.message?.includes('ScrollTrigger') || event.message?.includes('gsap')) {
        console.warn('GSAP/ScrollTrigger error caught:', event.message);
        event.preventDefault();
      }
    };
    
    // Add a global scroll listener to detect potential navigation issues
    const handleBeforeUnload = () => {
      cleanup();
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('error', handleError);
    }
    
    // Cleanup function
    return () => {
      clearTimeout(initialRefreshTimer);
      cleanup();
      
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        window.removeEventListener('error', handleError);
      }
    };
  }, [safeScrollTriggerRefresh, cleanup]);

  // Handle component unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return (
    <main>
      <GlobalStyles />
      <ThemeProvider theme={light}>
        <MobileViewportFix />
        <Navigation />
        <Home />
        
        <MainSection>
          {/* All sections loaded immediately - no lazy loading */}
          <Ticket />
          <AIInterview />
          <Roadmap />
          <Products />
          <Showcase />
          <Team />
          <Faq />
          <Contact />
          <Footer />
          <ScrollToTop />
        </MainSection>
      </ThemeProvider>
    </main>
  );
};

export default App;