global
    log /dev/log    local0
    log /dev/log    local1 notice
    maxconn 399999
    user haproxy
    group haproxy

defaults
    log     global
    mode    tcp
    option  tcplog
    option  dontlognull
    timeout connect 5000
    timeout client  50000
    timeout server  50000

frontend pg_frontend
    bind *:6435
    mode tcp
    default_backend pg_backend

backend pg_backend
    mode tcp
    balance roundrobin
    option tcp-check
    server pgbouncer1 pgbouncer1:6432 check inter 2000 rise 2 fall 3
    server pgbouncer2 pgbouncer2:6433 check inter 2000 rise 2 fall 3 backup
    server pgbouncer3 pgbouncer3:6434 check inter 2000 rise 2 fall 3 backup
