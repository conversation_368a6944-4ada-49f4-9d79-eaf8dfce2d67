"use client";

import { useEffect, useState, useRef } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { getInterviewById } from "@/app/lib/actions/available-interviews/actions";

// Direct import instead of dynamic import
import AIInterviewResults from "./components/ai-interview-results";

// Define interface for interview data
interface InterviewData {
  interviewId: string;
  sessionId: string;
  interviewData: any | null;
  isValidSession: boolean;
}

// Mock data structure to use while real data is loading
const mockInterviewData: InterviewData = {
  interviewId: "",
  sessionId: "",
  interviewData: null,
  isValidSession: true
};

// Fallback loading component
const LoadingComponent = ({ message = "Loading...", retry = 0, maxRetries = 3 }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Interview Results</h3>
      <p className="text-gray-600">
        {retry > 0 
          ? `Please wait while we prepare your results (retry ${retry}/${maxRetries})...` 
          : "Please wait while we prepare your results..."}
      </p>
    </div>
  </div>
);

export default function InterviewResultPage() {
  const params = useParams();
  const router = useRouter();
  const interviewId = params?.interviewId as string;
  const sessionId = params?.sessionId as string;
  
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [interviewData, setInterviewData] = useState<InterviewData>(mockInterviewData);
  const [dataFetched, setDataFetched] = useState(false);
  const retryCount = useRef(0);
  const maxRetries = 3;

  // Set mounted state on client side
  useEffect(() => {
    console.log("Setting mounted state");
    setMounted(true);
  }, []);

  // Function to fetch data with retry logic
  const fetchDataWithRetry = async () => {
    if (!interviewId) {
      console.error("Interview ID missing");
      setError("Interview ID is missing");
      setLoading(false);
      return;
    }

    // Prevent duplicate fetch attempts
    if (dataFetched) return;

    try {
      console.log(`Fetching data for interviewId: ${interviewId}, sessionId: ${sessionId || 'none'}`);
      
      // If we have a sessionId, assume it's valid for now as per new requirement (dont need to validate through redis)
      let isValidSession = !!sessionId;
      if (sessionId) {
        console.log(`SessionId is present: ${sessionId}, assuming valid.`);
      } else {
        console.log('No sessionId present.');
      }

      // Get interview details
      console.log('Fetching interview details...');
      const interviewDetails = await getInterviewById(interviewId);
      
      if (!interviewDetails) {
        console.error('Interview not found');
        setError("Interview not found");
      } else {
        console.log('Interview details fetched successfully:', interviewDetails);
        setInterviewData({
          interviewId,
          sessionId,
          interviewData: interviewDetails,
          isValidSession
        });
        setDataFetched(true);
        
        // IMPORTANT: Set loading to false here to ensure the component renders
        console.log('Setting loading to false after successful data fetch');
        setLoading(false);
      }
    } catch (err) {
      console.error("Failed to fetch interview data:", err);
      
      // Retry on network errors
      if (retryCount.current < maxRetries) {
        console.log(`Error fetching data, retrying (${retryCount.current + 1}/${maxRetries})...`);
        retryCount.current += 1;
        setTimeout(fetchDataWithRetry, 2000);
        return;
      }
      
      setError("Failed to load interview data. Please try again later.");
      setLoading(false);
    }
  };

  // Fetch data when component is mounted
  useEffect(() => {
    if (mounted && !dataFetched) {
      console.log('Component mounted, fetching data...');
      fetchDataWithRetry();
    }
  }, [mounted, interviewId, sessionId, dataFetched]);

  // Debug render cycle
  useEffect(() => {
    console.log(`Render cycle - mounted: ${mounted}, loading: ${loading}, dataFetched: ${dataFetched}, error: ${error}`);
  });

  // Don't render anything until mounted (prevents hydration issues)
  if (!mounted) {
    console.log("Not mounted yet, showing initial loading state");
    return <LoadingComponent message="Loading..." />;
  }

  if (loading) {
    console.log("In loading state, showing loading UI");
    return <LoadingComponent retry={retryCount.current} maxRetries={maxRetries} />;
  }

  if (error) {
    console.log("Error state, showing error UI:", error);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-red-50 p-6 rounded-lg">
            <p className="text-red-600 text-lg font-semibold mb-4">{error}</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button 
                onClick={() => {
                  console.log("Reload button clicked, resetting state");
                  retryCount.current = 0;
                  setDataFetched(false);
                  setLoading(true);
                  setError(null);
                  window.location.reload();
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Reload Page
              </button>
              <button 
                onClick={() => router.push('/available-interviews')}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back to Interviews
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!interviewData.isValidSession && sessionId) {
    console.log("Invalid session state, showing invalid session UI");
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md p-6 bg-yellow-50 rounded-lg">
          <p className="text-yellow-700 text-lg font-semibold mb-3">Invalid Session ID</p>
          <p className="text-gray-700 mb-5">
            The session ID provided ({sessionId}) is not valid for this interview. 
            Would you like to view the results without session data, or return to the interview list?
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button 
              onClick={() => router.push(`/ai-interview-result/${interviewId}`)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              View Without Session
            </button>
            <button 
              onClick={() => router.push('/interview-history')}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              View Interview History
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we've reached here, we should be rendering the AIInterviewResults component
  console.log('Ready to render AIInterviewResults component with:', interviewId, sessionId);
  
  try {
    return <AIInterviewResults interviewId={interviewId} sessionId={sessionId} />;
  } catch (e) {
    console.error('Error rendering AIInterviewResults:', e);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-red-50 p-6 rounded-lg">
            <p className="text-red-600 text-lg font-semibold mb-4">Error rendering results</p>
            <p className="text-gray-700 mb-4">An unexpected error occurred while displaying your results.</p>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }
}