package controller

import (
	"database/sql"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/payments"
	"github.com/terang-ai/backend-service/repository"
)

type PaymentController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "wip", "user")
}

func NewPaymentController(db *sql.DB, redis *redis.Client) *PaymentController {
	return &PaymentController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "payments", "payment"), // Initialize with specific table and entity name
		Entity: "payment",
	}
}

func (c *PaymentController) DeletePayment(ctx *gin.Context) {
	var uri model.PaymentUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Payment
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Payment failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Payment not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Payment successfully"})
}

func (c *PaymentController) GetAllPayments(ctx *gin.Context) {
	var entity model.Payment
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "payments retrieved successfully", "_pagination": paginationInfo})
}

func (c *PaymentController) GetOnePayment(ctx *gin.Context) {
	var uri model.PaymentUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Payment
	entity := &model.Payment{}

	// Call repository method to retrieve one Payment by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Payment not found"})
		return
	}

	// Type assertion to *model.Payment
	if wipEntity, ok := result.(*model.Payment); ok {
		// Update entity with fetched data
		*entity = *wipEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Payment successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Payment failed", "msg": "internal error"})
}

func (c *PaymentController) InsertPayment(ctx *gin.Context) {
	var post model.PostPayment // Replace with your specific post type
	var entity model.Payment
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Payment failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Payment successfully"})
}

func (c *PaymentController) UpdatePayment(ctx *gin.Context) {
	var updates model.UpdatePayment
	var uri model.PaymentUri
	var entity model.Payment

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}
	log.Println(updates)

	// Call repository method to update Payment by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Payment", "error": err.Error()})
		return
	}

	// Type assertion to *model.Payment
	if updatedPayment, ok := updated.(*model.Payment); ok {
		// Update entity with fetched data after update
		entity = *updatedPayment
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Payment updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Payment failed", "msg": "internal error"})
}
