package rediscache

import (
	"context"
	"fmt"
	"io"
	"os"

	"github.com/redis/go-redis/v9"
	"gopkg.in/yaml.v2"
)

func ReadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func getRedisConfig() (*Config, error) {
	config := &Config{}

	// Check if environment variables exist
	username := os.Getenv("REDIS_USERNAME")
	password := os.Getenv("REDIS_PASSWORD")
	host := os.Getenv("REDIS_HOST")
	port := os.Getenv("REDIS_PORT")

	// Use environment variables if password, host, and port are set
	if password != "" && host != "" && port != "" {
		config.REDIS.Username = username // This can be empty
		config.REDIS.Password = password
		config.REDIS.Host = host
		config.REDIS.Port = port
	} else {
		// Use config.yaml as a fallback only if any of password, host, or port is missing
		var err error
		config, err = ReadConfig("configs/config.yaml")
		if err != nil {
			return nil, err
		}
	}

	return config, nil
}

func ConnectRedis(ctx context.Context) (*redis.Client, error) {
	config, err := getRedisConfig()
	if err != nil {
		return nil, err
	}

	var connStr string
	if config.REDIS.Username != "" {
		// Use both username and password if username is provided
		connStr = fmt.Sprintf("redis://%s:%s@%s:%s",
			config.REDIS.Username, config.REDIS.Password, config.REDIS.Host, config.REDIS.Port)
	} else {
		// Use only password if username is not provided
		connStr = fmt.Sprintf("redis://:%s@%s:%s",
			config.REDIS.Password, config.REDIS.Host, config.REDIS.Port)
	}

	opts, err := redis.ParseURL(connStr)
	if err != nil {
		return nil, err
	}

	client := redis.NewClient(opts)

	// Check connection
	_, err = client.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return client, nil
}
