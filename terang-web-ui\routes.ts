export const privateRoutes = [
  "/dashboard",
  "/my-exams",
  "/my-trials",
  "/exam",
  "/trial",
  "/available-exams",
  "/available-interviews",
  "/settings",
  "/leaderboard",
  "/chat-ai",
  "/reports",
  "/personalised-reports"
];

export const authRoutes = ["/login"];

export const registerRoutes = ["/register"];

// When user is not logged in and tries to access protected routes redirect to login page
export const DEFAULT_REDIRECT_LOGIN_URL = "/login";

// logout
export const DEFAULT_REDIRECT_LOGOUT_URL = "/logout";

// When user is logged in and tries to access login page redirect to dashboard
export const DEFAULT_REDIRECT_HOME_URL = "/dashboard";

// to base url
export const DEFAULT_REDIRECT_BASE_URL = "/";

// to registerFinal Page
export const DEFAULT_REDIRECT_REGISTER_FINAL_URL = "/register/final";
