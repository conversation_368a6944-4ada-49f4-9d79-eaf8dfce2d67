// Create a new file: middleware/ai_config.go
package middleware

import (
	"io"
	"os"

	"gopkg.in/yaml.v2"
)

// AIConfig represents the AI configuration structure
type AIConfig struct {
	AI struct {
		GoogleGeminiAPIKey string `yaml:"google_gemini_api_key"`
	} `yaml:"ai"`
}

// ReadAIConfig reads AI configuration from YAML file
func ReadAIConfig(filename string) (*AIConfig, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config AIConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// GetGeminiAPIKey gets the Google Gemini API key from environment or config
func GetGeminiAPIKey() (string, error) {
	// Check environment variable first (for production)
	apiKey := os.Getenv("GOOGLE_GEMINI_API_KEY")
	if apiKey != "" {
		return apiKey, nil
	}

	// Fall back to YAML configuration (for development)
	config, err := ReadAIConfig("configs/config.yaml")
	if err != nil {
		return "", err
	}

	return config.AI.GoogleGeminiAPIKey, nil
}
