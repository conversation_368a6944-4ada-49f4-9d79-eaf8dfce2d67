package portfolios

import (
	"github.com/jmoiron/sqlx"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	media "github.com/terang-ai/backend-service/controller/media"
)

func RegisterRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	mediaCtrl := media.NewMediaController(dbx)

	mediaBaseGroup := r.Group("/v1")
	{
		mediaBaseGroup.POST("/media", mediaCtrl.InsertMedia)
		mediaBaseGroup.GET("/media", mediaCtrl.GetAllMedia)
		mediaBaseGroup.GET("/media/:id", mediaCtrl.GetOneMedia)
		mediaBaseGroup.PUT("/media/:id", mediaCtrl.UpdateMedia)
		mediaBaseGroup.DELETE("/media/:id", mediaCtrl.DeleteMedia)
	}
}
