package repository

import (
	model "github.com/terang-ai/backend-service/model/exams"
)

type ExamsRepositoryInterface interface {
	InsertExamSessions(model.PostExamSessions) (*model.ExamSessions, error)
	UpdateExamSessions(string, model.UpdateExamSessions) (*model.ExamSessions, error)
	DeleteExamSessions(string) (bool, error)
	GetAllExamSessionsByUserId(string, int, int) ([]model.ExamSessions, ExamSessionsPaginationInfo, error)
	GetLastExamSessionsByExamIdUserId(string, string) (*model.ExamSessionsWithDuration, error)
	GetLastTrialSessionsByExamIdUserId(string, string, string) (*model.ExamSessionsWithDuration, error)
	GetExamSessionBySessionId(string) (*model.ExamSessionsWithDuration, error)

	// Exam Scores
	InsertExamScores(model.PostExamScores) (*model.ExamScores, error)
	UpdateExamScores(string, model.UpdateExamScores) (*model.ExamScores, error)
	DeleteExamScores(string) (bool, error)
	GetExamScoresBySessionId(string) (*model.ExamScores, error)
}
