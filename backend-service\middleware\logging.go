package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// CustomLogger middleware adds custom logging functionality
func CustomLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Get logging context
		prefix := c.GetString("logger_prefix")
		if prefix == "" {
			prefix = "[API]"
		}
		sessionID := c.GetString("session_id")
		logMessage := c.GetString("log_message")

		// End timer
		end := time.Now()
		latency := end.Sub(start)

		// Base log format
		baseLog := fmt.Sprintf("%s %s | %d | %v | %s | %s %s",
			prefix,
			end.Format("2006/01/02 - 15:04:05"),
			c.Writer.Status(),
			latency,
			c.ClientIP(),
			c.Request.Method,
			c.Request.URL.Path,
		)

		// Add session ID if present
		if sessionID != "" {
			baseLog = fmt.Sprintf("%s | Session: %s", baseLog, sessionID)
		}

		// Add custom message if present
		if logMessage != "" {
			baseLog = fmt.Sprintf("%s | %s", baseLog, logMessage)
		}

		// Log based on status code
		statusCode := c.Writer.Status()
		switch {
		case statusCode >= 500:
			fmt.Printf("\x1b[31m%s\x1b[0m\n", baseLog) // Red for server errors
		case statusCode >= 400:
			fmt.Printf("\x1b[33m%s\x1b[0m\n", baseLog) // Yellow for client errors
		case statusCode >= 300:
			fmt.Printf("\x1b[36m%s\x1b[0m\n", baseLog) // Cyan for redirects
		default:
			fmt.Printf("\x1b[32m%s\x1b[0m\n", baseLog) // Green for success
		}
	}
}
