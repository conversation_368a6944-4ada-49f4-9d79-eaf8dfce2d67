package model

import (
	"time"
)

// OrderStatus represents the visibility status of the comsheet
type OrderStatus string

const (
	PENDING             OrderStatus = "PENDING"
	PAYMENT_PROCESSING  OrderStatus = "PAYMENT_PROCESSING"
	PAYMENT_RECEIVED    OrderStatus = "PAYMENT_RECEIVED"
	IN_PROGRESS         OrderStatus = "IN_PROGRESS"
	REVISION_REQUESTED  OrderStatus = "REVISION_REQUESTED"
	REVISIONS_COMPLETED OrderStatus = "REVISIONS_COMPLETED"
	COMPLETED           OrderStatus = "COMPLETED"
	CANCELLED           OrderStatus = "CANCELLED"
)

// Order represents a Order in the system
type Order struct {
	Id          string      `json:"id"`
	ClientId    *string     `json:"client_id"`
	ExamId      string      `json:"exam_id"`
	Quantity    int         `json:"quantity"`
	Status      OrderStatus `json:"status"`
	CreatedAt   time.Time   `json:"created_at"`
	ModifiedAt  time.Time   `json:"modified_at"`
	BundleId    *string     `json:"bundle_id"`
	ClientEmail *string     `json:"client_email"`
}

// PostOrder is used for creating a new Order. Fields can be added as necessary.
type PostOrder struct {
	Id          string      `json:"id"`
	ClientId    *string     `json:"client_id"`
	ExamId      string      `json:"exam_id" binding:"required"`
	Quantity    int         `json:"quantity" binding:"required"`
	Status      OrderStatus `json:"status" binding:"required"`
	BundleId    *string     `json:"bundle_id"`
	ClientEmail *string     `json:"client_email"`
}

type UpdateOrder struct {
	Name        *string      `json:"name,omitempty"`
	ClientId    *string      `json:"client_id,omitempty"`
	ExamId      *string      `json:"exam_id,omitempty"`
	Quantity    *int         `json:"quantity,omitempty"`
	Status      *OrderStatus `json:"status,omitempty"`
	BundleId    *string      `json:"bundle_id,omitempty"`
	ClientEmail *string      `json:"client_email,omitempty"`
}

type OrderUri struct {
	ID string `uri:"id" binding:"required"`
}
