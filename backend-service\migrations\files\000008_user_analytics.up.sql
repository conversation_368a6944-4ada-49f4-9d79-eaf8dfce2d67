CREATE TABLE IF NOT EXISTS user_analytics (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    event_type VARCHAR(255) NOT NULL, -- login, logout, page_view, feature_usage, etc.
    event_timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),  -- to group related events
    duration_seconds INT,    -- for tracking time spent
    path VARCHAR(255),      -- URL or feature path
    device_info JSONB DEFAULT '{}', -- device, browser, OS info
    location_info JSONB DEFAULT '{}', -- IP, country, city
    event_metadata JSONB DEFAULT '{}', -- any additional event-specific data
    status VARCHAR(255), -- success, failure, error
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes for common queries
CREATE INDEX idx_user_analytics_user_id ON user_analytics(user_id);
CREATE INDEX idx_user_analytics_event_type ON user_analytics(event_type);
CREATE INDEX idx_user_analytics_timestamp ON user_analytics(event_timestamp);
CREATE INDEX idx_user_analytics_session ON user_analytics(session_id);
CREATE INDEX idx_user_analytics_metadata ON user_analytics USING gin (event_metadata);