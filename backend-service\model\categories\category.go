package model

import (
	"time"
)

// Category represents a Category in the system
type Category struct {
	Id          string    `json:"id"`
	Name        string    `json:"name"`
	ImageUrl    *string   `json:"image_url"`
	SizeInBytes *int      `json:"size_in_bytes"`
	MimeType    *string   `json:"mime_type"`
	Width       *int      `json:"width"`
	Height      *int      `json:"height"`
	CreatedAt   time.Time `json:"created_at"`
	ModifiedAt  time.Time `json:"modified_at"`
}

// PostCategory is used for creating a new Category. Fields can be added as necessary.
type PostCategory struct {
	Id       string  `json:"id"`
	Name     string  `json:"name" binding:"required"`
	ImageUrl *string `json:"image_url"`
}

type UpdateCategory struct {
	Name     *string `json:"name,omitempty"`
	ImageUrl *string `json:"image_url,omitempty"`
}

type CategoryUri struct {
	ID string `uri:"id" binding:"required"`
}

// ExamCategory represents the association between an exam and a category
type ExamCategory struct {
	Id         string `json:"id"`
	ExamId     string `json:"exam_id"`
	CategoryId string `json:"category_id"`
}

// PostExamCategory is used for creating a new ExamCategory association
type PostExamCategory struct {
	Id         string `json:"id"`
	ExamId     string `json:"exam_id" binding:"required"`
	CategoryId string `json:"category_id" binding:"required"`
}

// UpdateExamCategory is used for updating an ExamCategory association
type UpdateExamCategory struct {
	ExamId     *string `json:"exam_id,omitempty"`
	CategoryId *string `json:"category_id,omitempty"`
}

type ExamCategoryUri struct {
	ID string `uri:"id" binding:"required"`
}
