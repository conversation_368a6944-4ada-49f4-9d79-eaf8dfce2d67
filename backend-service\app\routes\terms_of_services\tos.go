package terms_of_services

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	terms_of_services "github.com/terang-ai/backend-service/controller/terms_of_services"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	controller := terms_of_services.NewTermsOfServiceController(db, redis)

	r.POST("/v1/terms_of_services", controller.InsertTermsOfService)
	r.GET("/v1/terms_of_services", controller.GetAllTermsOfServices)
	r.GET("/v1/terms_of_services/:id", controller.GetOneTermsOfService)
	r.PUT("/v1/terms_of_services/:id", controller.UpdateTermsOfService)
	r.DELETE("/v1/terms_of_services/:id", controller.DeleteTermsOfService)

	r.GET("/v1/terms_of_services/artists/:artist_id", controller.GetAllTermsOfServicesByArtistId)
	r.GET("/v1/terms_of_services/:id/artists/:artist_id", controller.GetOneTermsOfServicesByArtistId)
	r.GET("/v2/terms_of_services", controller.GetAllTermsOfServicesWithCache)
}
