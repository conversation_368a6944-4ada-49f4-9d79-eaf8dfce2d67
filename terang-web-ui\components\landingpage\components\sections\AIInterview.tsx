// components/landingpage/components/sections/AIInterview.tsx
"use client";

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import styled, { keyframes, css } from 'styled-components';
import dynamic from 'next/dynamic';
import Loading from '../Loading';
import { useLanguage } from '@/app/language-wrapper';

// Lazy load heavy components only when needed
const AudioVisualizerComponent = dynamic(() => import('../AudioVisualizerComponent'), {
  loading: () => null,
  ssr: false
});

const DotLottie = dynamic(() => import("@/components/shared/dotlottie-animation"), {
  loading: () => <Loading />,
  ssr: false // Don't render on server to reduce initial bundle
});

// Reduced animation complexity - fewer keyframes, simpler transforms
const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.95; }
`;

const ripple = keyframes`
  0% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
  100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
`;

const slideUp = keyframes`
  from { opacity: 0; transform: translateY(15px); }
  to { opacity: 1; transform: translateY(0); }
`;

const breathe = keyframes`
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
`;

// Optimized Section with reduced gradient complexity
const Section = styled.section`
  width: 100%;
  background: linear-gradient(135deg, #FEFEFE 0%, #F6F8FB 50%, #E8EFF5 100%);
  position: relative;
  overflow: hidden;
  padding: 8rem 0;
  
  @media (max-width: 70em) {
    padding: 6rem 0;
  }
  
  @media (max-width: 48em) {
    padding: 4rem 1rem; /* Add horizontal padding on mobile */
  }
`;

const Container = styled.div`
  width: 85%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  
  @media (max-width: 70em) {
    width: 90%;
  }
  
  @media (max-width: 64em) {
    width: 95%;
    flex-direction: column;
    gap: 4rem;
  }
  
  @media (max-width: 48em) {
    width: 100%; /* Full width on mobile */
    gap: 3rem;
    padding: 0; /* Remove any internal padding */
  }
`;

// Simplified background decoration - reduced from 3 to 2 elements
const BackgroundDecor = styled.div`
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(168, 85, 247, 0.03);
  filter: blur(30px);
  
  &:nth-child(1) {
    top: 15%;
    left: 10%;
    animation: ${float} 12s ease-in-out infinite;
  }
  
  &:nth-child(2) {
    bottom: 20%;
    right: 15%;
    animation: ${float} 12s ease-in-out infinite 6s;
  }
  
  @media (max-width: 48em) {
    display: none; /* Hide decorations on mobile for cleaner look */
  }
`;

const ContentSection = styled.div`
  flex: 1;
  color: #1F2937;
  max-width: 600px;
  
  @media (max-width: 64em) {
    text-align: center;
    max-width: 100%;
  }
  
  @media (max-width: 48em) {
    padding: 0 1rem; /* Add padding on mobile */
  }
`;

const Badge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(168, 85, 247, 0.08);
  border: 1px solid rgba(168, 85, 247, 0.15);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #7C3AED;
  
  img {
    width: 20px;
    height: 20px;
  }
  
  @media (max-width: 48em) {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }
`;

const Title = styled.h2`
  font-size: 4rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #1F2937 0%, #7C3AED 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: "Sora", sans-serif;
  
  @media (max-width: 70em) {
    font-size: 3rem;
  }
  
  @media (max-width: 48em) {
    font-size: 2.2rem; /* Slightly smaller on mobile */
    line-height: 1.2;
    margin-bottom: 1rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.8;
  color: #4B5563;
  
  @media (max-width: 48em) {
    font-size: 1rem; /* Smaller subtitle on mobile */
    margin-bottom: 1.5rem;
  }
`;

const FeatureList = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 3rem;
  
  @media (max-width: 48em) {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    margin-bottom: 2rem;
  }
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  color: #4B5563;
  
  img {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }
  
  @media (max-width: 48em) {
    font-size: 0.9rem;
    gap: 0.5rem;
    
    img {
      width: 16px;
      height: 16px;
    }
  }
`;

// Simplified button hover effects
const CTAButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  background: ${props => 
    props.variant === 'secondary' 
      ? 'linear-gradient(135deg, #A855F7 0%, #7C3AED 100%)' 
      : 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
  };
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 50px;
  cursor: pointer;
  transition: transform 0.15s ease;
  box-shadow: ${props => 
    props.variant === 'secondary' 
      ? '0 6px 20px rgba(168, 85, 247, 0.2)' 
      : '0 6px 20px rgba(255, 107, 107, 0.2)'
  };
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  width: 50%; /* Make buttons half width */
  max-width: 300px; /* Optional: set a maximum width */
  
  img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
  }
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  @media (max-width: 48em) {
    width: 100%; /* Full width on mobile for better UX */
    max-width: none;
    padding: 0.9rem 2rem;
    font-size: 1rem;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start; /* Align buttons to the left */
  
  @media (max-width: 48em) {
    align-items: center; /* Center on mobile */
    gap: 0.8rem;
  }
`;

const VoiceSection = styled.div`
  flex: 1;
  max-width: 500px;
  
  @media (max-width: 64em) {
    max-width: 600px;
    width: 100%;
  }
  
  @media (max-width: 48em) {
    max-width: 100%;
    padding: 0 1rem; /* Add padding on mobile */
  }
`;

const VoiceContainer = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 3rem 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  text-align: center;
  
  @media (max-width: 48em) {
    padding: 2rem 1.5rem; /* Reduce padding on mobile */
    border-radius: 20px;
    margin: 0; /* Remove any margin */
  }
`;

const SessionInfo = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  
  span {
    font-size: 0.9rem;
    color: #666;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    img {
      width: 16px;
      height: 16px;
    }
    
    &:last-child {
      background: #ef4444;
      color: white;
      
      img {
        filter: brightness(0) invert(1);
      }
    }
  }
  
  @media (max-width: 48em) {
    flex-direction: column;
    gap: 0.5rem;
    
    span {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }
  }
`;

const AvatarContainer = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  margin-top: 2rem;
  height: 180px;
  
  @media (max-width: 48em) {
    height: 150px; /* Reduce height on mobile */
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
  }
`;

// Reduced avatar animation complexity
const Avatar = styled.div<{ $isActive: boolean }>`
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: linear-gradient(135deg, #A855F7 0%, #7C3AED 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 2rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: ${props => props.$isActive ? '0 0 25px rgba(168, 85, 247, 0.4)' : '0 6px 20px rgba(168, 85, 247, 0.2)'};
  position: relative;
  overflow: hidden;
  z-index: 2;
  
  ${props => props.$isActive && css`
    animation: ${breathe} 4s ease-in-out infinite;
  `}
  
  @media (max-width: 48em) {
    width: 180px; /* Smaller avatar on mobile */
    height: 180px;
    font-size: 1.5rem;
  }
`;

const LottieContainer = styled.div`
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
`;

// Simplified ripple effect with reduced layers
const RippleEffect = styled.div<{ $isActive: boolean }>`
  position: absolute;
  top: 50%;
  left: 50%;
  width: 250px;
  height: 250px;
  border: 2px solid rgba(168, 85, 247, 0.6); /* Increased opacity and thickness */
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: ${props => props.$isActive ? 0.8 : 0}; /* Higher initial opacity */
  pointer-events: none; /* Prevent interaction issues */
  
  ${props => props.$isActive && css`
    animation: ${ripple} 2s ease-out infinite; /* Faster animation */
  `}
  
  /* Add a second ripple layer for better visibility */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    border: 1px solid rgba(168, 85, 247, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: ${props => props.$isActive ? 1 : 0};
    
    ${props => props.$isActive && css`
      animation: ${ripple} 2s ease-out infinite 0.5s; /* Delayed second ripple */
    `}
  }
  
  @media (max-width: 48em) {
    width: 200px; /* Smaller ripple on mobile */
    height: 200px;
    
    &::before {
      width: 160px;
      height: 160px;
    }
  }
`;

const AvatarInfo = styled.div`
  margin-bottom: 2rem;
  
  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
  }
  
  p {
    margin: 0;
    font-size: 1rem;
    color: #666;
  }
  
  @media (max-width: 48em) {
    margin-bottom: 1.5rem;
    
    h3 {
      font-size: 1.3rem;
    }
    
    p {
      font-size: 0.9rem;
    }
  }
`;

const StatusDisplay = styled.div<{ status: string }>`
  background: ${props => 
    props.status === 'speaking' ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)' : 
    props.status === 'listening' ? 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)' : 
    'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
  };
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transition: background 0.2s ease;
  
  @media (max-width: 48em) {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
`;

const CurrentQuestion = styled.div`
  background: rgba(168, 85, 247, 0.08);
  border: 1px solid rgba(168, 85, 247, 0.15);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: #333;
    text-align: center;
    font-style: italic;
  }
  
  @media (max-width: 48em) {
    padding: 1.2rem;
    margin-bottom: 1.5rem;
    min-height: 80px;
    
    p {
      font-size: 0.9rem;
    }
  }
`;

const ControlsArea = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  
  @media (max-width: 48em) {
    padding-top: 1.5rem;
    flex-direction: column;
    gap: 1rem;
  }
`;

const MicButton = styled.button<{ $isActive: boolean }>`
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: none;
  background: ${props => 
    props.$isActive ? 
    'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' : 
    'linear-gradient(135deg, #A855F7 0%, #7C3AED 100%)'
  };
  color: white;
  cursor: pointer;
  transition: transform 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  
  img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
  
  &:hover {
    transform: scale(1.03);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  @media (max-width: 48em) {
    width: 60px;
    height: 60px;
    
    img {
      width: 20px;
      height: 20px;
    }
  }
`;

const Timer = styled.div`
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  font-weight: 600;
  color: #A855F7;
  background: rgba(168, 85, 247, 0.08);
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(168, 85, 247, 0.15);
  
  @media (max-width: 48em) {
    font-size: 1rem;
    padding: 0.6rem 1.2rem;
  }
`;

const AIInterview: React.FC = () => {
  const { t } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isAISpeaking, setIsAISpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [timer, setTimer] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Memoize questions array with localized content
  const questions = useMemo(() => [
    t('ai_question_1'),
    t('ai_question_2'),
    t('ai_question_3'),
    t('ai_question_4'),
    t('ai_question_5'),
    t('ai_question_6')
  ], [t]);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Memoize status calculations
  const currentStatus = useMemo(() => 
    isAISpeaking ? 'speaking' : isListening ? 'listening' : 'standby'
  , [isAISpeaking, isListening]);

  const statusText = useMemo(() => 
    isAISpeaking ? t('professor_terra_speaking') : 
    isListening ? t('listening_to_your_answer') : 
    t('ready_to_start_interview')
  , [isAISpeaking, isListening, t]);

  // Optimized timer with longer intervals when not visible
  useEffect(() => {
    if (!isVisible) return;
    
    const interval = setInterval(() => {
      setTimer(prev => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [isVisible]);

  // Optimized animation sequence with longer delays
  useEffect(() => {
    if (!isVisible) return;
    
    if (currentQuestionIndex >= questions.length) {
      const resetTimeout = setTimeout(() => {
        setCurrentQuestionIndex(0);
        setIsAISpeaking(false);
        setIsListening(false);
        setTimer(0);
      }, 10000);
      return () => clearTimeout(resetTimeout);
    }

    const speakTimeout = setTimeout(() => {
      setIsAISpeaking(true);
      setIsListening(false);
      
      setTimeout(() => {
        setIsAISpeaking(false);
        setIsListening(true);
        
        setTimeout(() => {
          setIsListening(false);
          setCurrentQuestionIndex(prev => prev + 1);
        }, 6000);
      }, 3000);
    }, currentQuestionIndex === 0 ? 2000 : 2000);

    return () => clearTimeout(speakTimeout);
  }, [currentQuestionIndex, questions.length, isVisible]);

  // Intersection Observer for performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0.1 }
    );
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    return () => observer.disconnect();
  }, []);

  // Simplified GSAP animation
  useEffect(() => {
    if (!isVisible) return;
    
    const ctx = () => {
      if (typeof window !== 'undefined' && window.gsap) {
        window.gsap.fromTo(
          sectionRef.current,
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            duration: 0.6,
            ease: "power2.out"
          }
        );
      }
    };

    const timer = setTimeout(ctx, 100);
    return () => clearTimeout(timer);
  }, [isVisible]);

  const handleStartInterview = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/available-interviews';
    }
  }, []);

  const handleTryDemo = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/ai-interview-lpdp';
    }
  }, []);

  const currentQuestion = useMemo(() => 
    currentQuestionIndex < questions.length 
      ? `"${questions[currentQuestionIndex]}"` 
      : `"${t('interview_session_restarting')}"`
  , [currentQuestionIndex, questions, t]);

  return (
    <Section ref={sectionRef} id="ai-interview">
      {/* Reduced background decorations */}
      <BackgroundDecor />
      <BackgroundDecor />
      
      <Container>
        <ContentSection>
          <Badge>
            <img src="https://cdn.terang.ai/landingpage-assets/voice-recognition.svg" alt="Voice AI" />
            {t('voice_ai_technology')}
          </Badge>
          
          <Title>
            {t('natural_voice_interview')}
            <br />
            {t('with_professor_terra')}
          </Title>
          
          <Subtitle>
            {t('voice_interview_description')}
          </Subtitle>
          
          <FeatureList>
            <FeatureItem>
              <img src="https://cdn.terang.ai/landingpage-assets/voice-recognition.svg" alt="Real-time Analysis" />
              {t('voice_recognition')}
            </FeatureItem>
            <FeatureItem>
              <img src="https://cdn.terang.ai/landingpage-assets/natural-language-processing.svg" alt="Real-time Analysis" />
              {t('natural_language_processing')}
            </FeatureItem>
            <FeatureItem>
              <img src="https://cdn.terang.ai/landingpage-assets/instant.svg" alt="Real-time Analysis" />
              {t('realtime_speech_analysis')}
            </FeatureItem>
            <FeatureItem>
              <img src="https://cdn.terang.ai/landingpage-assets/consulting.svg" alt="Instant Feedback" />
              {t('instant_feedback_system')}
            </FeatureItem>
          </FeatureList>
          
          <ButtonContainer>
            <CTAButton onClick={handleStartInterview}>
              <img src="https://cdn.terang.ai/landingpage-assets/microphone2.svg" alt="Microphone" />
              {t('start_voice_interview')}
            </CTAButton>
            <CTAButton variant="secondary" onClick={handleTryDemo}>
              <img src="https://cdn.terang.ai/landingpage-assets/rocket.svg" alt="Rocket" />
              {t('read_more')}
            </CTAButton>
          </ButtonContainer>
        </ContentSection>

        <VoiceSection>
          <VoiceContainer>
            <SessionInfo>
              <span>
                <img src="https://cdn.terang.ai/landingpage-assets/consulting.svg" alt="LPDP" />
                {t('lpdp_interview_simulation')}
              </span>
              <span>
                <img src="https://cdn.terang.ai/landingpage-assets/instant.svg" alt="Live" />
                {t('live')}
              </span>
            </SessionInfo>

            <AvatarContainer>
              <RippleEffect $isActive={isAISpeaking && isVisible} />
              <Avatar $isActive={isAISpeaking && isVisible}>
                <LottieContainer>
                  {isVisible && (
                    <DotLottie
                      src="https://cdn.terang.ai/dotlotties/prof-terra.lottie"
                      autoplay
                      loop
                      width="100%"
                      height="100%"
                      devicePixelRatio={1.5}
                    />
                  )}
                </LottieContainer>
              </Avatar>
            </AvatarContainer>

            <AvatarInfo>
              <h3>{t('professor_terra')}</h3>
              <p>{t('ai_interview_specialist')}</p>
            </AvatarInfo>

            <StatusDisplay status={currentStatus}>
              {statusText}
            </StatusDisplay>

            <CurrentQuestion>
              <p>{currentQuestion}</p>
            </CurrentQuestion>

            {isVisible && (
              <AudioVisualizerComponent 
                isActive={isAISpeaking || isListening}
                barCount={8}
                intensity={isAISpeaking ? 'high' : 'medium'}
              />
            )}

            <ControlsArea>
              <MicButton $isActive={isListening}>
                <img src="https://cdn.terang.ai/landingpage-assets/microphone2.svg" alt="Microphone" />
              </MicButton>
              <Timer>{formatTime(timer)} / 45:00</Timer>
            </ControlsArea>
          </VoiceContainer>
        </VoiceSection>
      </Container>
    </Section>
  );
};

export default AIInterview;