name: Build and Deploy Go Application

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: ************
  PROJECT_NAME: terang-ai
  GCR_REGION: asia-southeast2
  IMAGE_NAME: backend-service
  GKE_CLUSTER: terang-ai-autopilot-cluster
  GKE_ZONE: asia-southeast2

jobs:
  build-and-push-deploy:
    runs-on: ubicloud-standard-2
    
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
    - uses: actions/checkout@v4

    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v1'
      with:
        workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/pool/providers/pool'
        service_account: '<EMAIL>'

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v1'

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    - name: Build Docker image
      run: docker build -t asia-southeast2-docker.pkg.dev/terang-ai/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }} .

    - name: Configure Docker to use gcloud as a credential helper
      run: |
        gcloud auth configure-docker asia-southeast2-docker.pkg.dev

    - name: Push image to Google Container Registry
      run: docker push asia-southeast2-docker.pkg.dev/terang-ai/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}

    - name: 'Install GKE Auth Plugin'
      run: |
        gcloud components install gke-gcloud-auth-plugin
        echo "CLOUDSDK_AUTH_PLUGIN_GKE_GCLOUD_AUTH_PLUGIN=true" >> $GITHUB_ENV

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --region ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_NAME }}

    - name: Deploy to GKE
      run: |
        kubectl set image deployment/backend-service backend-service=asia-southeast2-docker.pkg.dev/terang-ai/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}
        kubectl rollout status deployment/backend-service