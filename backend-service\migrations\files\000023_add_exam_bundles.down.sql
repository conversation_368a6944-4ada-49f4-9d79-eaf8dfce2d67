-- Drop indexes
DROP INDEX IF EXISTS idx_orders_bundle_id;
DROP INDEX IF EXISTS idx_exam_bundle_items_exam_id;
DROP INDEX IF EXISTS idx_exam_bundle_items_bundle_id;
DROP INDEX IF EXISTS idx_exam_bundles_metadata;
DROP INDEX IF EXISTS idx_exam_bundles_valid_dates;
DROP INDEX IF EXISTS idx_exam_bundles_price;
DROP INDEX IF EXISTS idx_exam_bundles_visibility;
DROP INDEX IF EXISTS idx_exam_bundles_user_id;

-- Drop triggers
DROP TRIGGER IF EXISTS exam_bundle_items_modified_at ON public.exam_bundle_items;
DROP TRIGGER IF EXISTS exam_bundles_modified_at ON public.exam_bundles;

-- Remove bundle_id from orders table
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_bundle_id_fkey;
ALTER TABLE orders DROP COLUMN IF EXISTS bundle_id;

-- Drop the exam bundle items table
DROP TABLE IF EXISTS exam_bundle_items;

-- Drop the exam bundles table
DROP TABLE IF EXISTS exam_bundles;