package custom

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

// MidtransNotification represents the incoming notification payload from Midtrans
type MidtransNotification struct {
	TransactionType   string `json:"transaction_type,omitempty"`
	TransactionTime   string `json:"transaction_time"`
	TransactionStatus string `json:"transaction_status"`
	TransactionID     string `json:"transaction_id"`
	StatusMessage     string `json:"status_message"`
	StatusCode        string `json:"status_code"`
	SignatureKey      string `json:"signature_key"`
	PaymentType       string `json:"payment_type"`
	OrderID           string `json:"order_id"`
	MerchantID        string `json:"merchant_id"`
	GrossAmount       string `json:"gross_amount"`
	FraudStatus       string `json:"fraud_status,omitempty"`
	ExpiryTime        string `json:"expiry_time"`
	Currency          string `json:"currency"`
	VaNumbers         []struct {
		VaNumber string `json:"va_number"`
		Bank     string `json:"bank"`
	} `json:"va_numbers,omitempty"`
	PaymentAmounts []struct {
		PaidAt string `json:"paid_at"`
		Amount string `json:"amount"`
	} `json:"payment_amounts,omitempty"`
	SettlementTime string `json:"settlement_time,omitempty"`
	Issuer         string `json:"issuer,omitempty"`
	Acquirer       string `json:"acquirer,omitempty"`
}

// OrderSource indicates whether the order is from orders table or user_subscriptions table
type OrderSource string

const (
	OrderSourceOrder        OrderSource = "orders"
	OrderSourceSubscription OrderSource = "subscriptions"
	OrderSourceNotFound     OrderSource = "not_found"
)

// RegisterPaymentReconciliationRoutes registers the payment reconciliation route
func RegisterPaymentReconciliationRoutes(r *gin.Engine, dbx *sqlx.DB) {
	r.POST("/v0/payment/reconciliation", HandleMidtransReconciliation(dbx))
}

// HandleMidtransReconciliation processes the Midtrans payment notification webhook
func HandleMidtransReconciliation(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var notification MidtransNotification
		if err := c.ShouldBindJSON(&notification); err != nil {
			log.Printf("Error parsing notification: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Invalid notification format",
			})
			return
		}

		// Log the incoming notification for debugging
		log.Printf("Received Midtrans notification for order %s with status %s",
			notification.OrderID, notification.TransactionStatus)

		// Check if order exists in orders or user_subscriptions table
		orderSource, err := checkOrderSource(dbx, notification.OrderID)
		if err != nil {
			log.Printf("Error checking order source: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Error processing notification",
			})
			return
		}

		// If order not found in either table
		if orderSource == OrderSourceNotFound {
			log.Printf("Order ID %s not found in any table", notification.OrderID)
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "Order not found",
			})
			return
		}

		// Process based on order source
		if orderSource == OrderSourceSubscription {
			// Update subscription with transaction_id and payment_status
			err = updateSubscriptionTransaction(dbx, notification)
			if err != nil {
				log.Printf("Error updating subscription: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "Failed to update subscription",
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"success":  true,
				"message":  "Subscription payment updated successfully",
				"source":   "subscription",
				"order_id": notification.OrderID,
				"status":   notification.TransactionStatus,
			})
			return
		} else {
			// For regular orders, update order status and payment
			err = updateOrderAndPayment(dbx, notification)
			if err != nil {
				log.Printf("Error updating order and payment: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "Failed to update order and payment",
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"success":  true,
				"message":  "Order and payment updated successfully",
				"source":   "order",
				"order_id": notification.OrderID,
				"status":   notification.TransactionStatus,
			})
			return
		}
	}
}

// checkOrderSource checks if the order ID exists in orders or user_subscriptions table
func checkOrderSource(dbx *sqlx.DB, orderID string) (OrderSource, error) {
	// First, check if it exists in user_subscriptions
	var subscriptionCount int
	err := dbx.Get(&subscriptionCount, "SELECT COUNT(*) FROM user_subscriptions WHERE id = $1", orderID)
	if err != nil {
		return OrderSourceNotFound, err
	}
	if subscriptionCount > 0 {
		return OrderSourceSubscription, nil
	}

	// Then check if it exists in orders
	var orderCount int
	err = dbx.Get(&orderCount, "SELECT COUNT(*) FROM orders WHERE id = $1", orderID)
	if err != nil {
		return OrderSourceNotFound, err
	}
	if orderCount > 0 {
		return OrderSourceOrder, nil
	}

	// If not found in either table
	return OrderSourceNotFound, nil
}

// updateSubscriptionTransaction updates the subscription with transaction_id and payment status
func updateSubscriptionTransaction(dbx *sqlx.DB, notification MidtransNotification) error {
	// First, check the current payment status
	var currentStatus string
	err := dbx.Get(&currentStatus, "SELECT payment_status FROM user_subscriptions WHERE id = $1", notification.OrderID)
	if err != nil {
		return fmt.Errorf("failed to check current subscription status: %v", err)
	}

	// Map Midtrans transaction status to payment_status enum
	var paymentStatus string
	switch notification.TransactionStatus {
	case "settlement", "capture":
		paymentStatus = "PAID"
	case "pending":
		paymentStatus = "PENDING"
	case "deny", "cancel", "expire", "failure":
		paymentStatus = "FAILED"
	case "refund", "partial_refund":
		paymentStatus = "REFUNDED"
	default:
		paymentStatus = "PENDING" // Default case
	}

	// Don't downgrade from PAID to any other status
	if currentStatus == "PAID" && paymentStatus != "PAID" {
		log.Printf("Subscription %s already has PAID status, ignoring update to %s status",
			notification.OrderID, paymentStatus)
		return nil
	}

	// Parse settlement time if exists
	var lastPaymentDate *time.Time
	if notification.SettlementTime != "" {
		parsedTime, err := time.Parse("2006-01-02 15:04:05", notification.SettlementTime)
		if err == nil {
			lastPaymentDate = &parsedTime
		}
	}

	// Update the subscription in the database
	query := `
		UPDATE user_subscriptions 
		SET 
			transaction_id = $1,
			payment_status = $2,
			last_payment_date = COALESCE($3, last_payment_date),
			modified_at = CURRENT_TIMESTAMP
		WHERE id = $4
		RETURNING id, user_id, tier_id, payment_status
	`

	var updatedSubscription UserSubscription
	err = dbx.Get(&updatedSubscription, query,
		notification.TransactionID,
		paymentStatus,
		lastPaymentDate,
		notification.OrderID)

	if err != nil {
		return fmt.Errorf("failed to update subscription: %v", err)
	}

	// Log the successful update
	log.Printf("Updated subscription %s with transaction %s and status %s",
		notification.OrderID, notification.TransactionID, paymentStatus)

	return nil
}

// updateOrderAndPayment updates the order status, payment and invoice when a payment notification is received
func updateOrderAndPayment(dbx *sqlx.DB, notification MidtransNotification) error {
	// Start a transaction
	tx, err := dbx.Beginx()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	// Prepare to rollback in case of error
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// First check the current status of the order
	var currentOrderStatus string
	err = tx.Get(&currentOrderStatus, "SELECT status FROM orders WHERE id = $1", notification.OrderID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check current order status: %v", err)
	}

	// Get invoice ID for this order
	var invoiceID string
	err = tx.Get(&invoiceID, "SELECT id FROM invoices WHERE order_id = $1", notification.OrderID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to find invoice for order: %v", err)
	}

	// Check the current status of the invoice
	var currentInvoiceStatus string
	err = tx.Get(&currentInvoiceStatus, "SELECT status FROM invoices WHERE id = $1", invoiceID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check current invoice status: %v", err)
	}

	// Check the current status of the payment
	var currentPaymentStatus string
	err = tx.Get(&currentPaymentStatus, "SELECT status FROM payments WHERE invoice_id = $1", invoiceID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to check current payment status: %v", err)
	}

	// Only proceed with updates if payment is successful
	if notification.TransactionStatus == "settlement" || notification.TransactionStatus == "capture" {
		// 1. Update order status to COMPLETED - only if not already COMPLETED
		if currentOrderStatus != "COMPLETED" {
			orderQuery := `
				UPDATE orders
				SET 
					status = 'COMPLETED',
					modified_at = CURRENT_TIMESTAMP
				WHERE id = $1
			`
			_, err = tx.Exec(orderQuery, notification.OrderID)
			if err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update order status: %v", err)
			}
		} else {
			log.Printf("Order %s already has COMPLETED status, not updating", notification.OrderID)
		}

		// 2. Update invoice status to PAID - only if not already PAID
		if currentInvoiceStatus != "PAID" {
			invoiceQuery := `
				UPDATE invoices
				SET 
					status = 'PAID',
					modified_at = CURRENT_TIMESTAMP
				WHERE id = $1
			`
			_, err = tx.Exec(invoiceQuery, invoiceID)
			if err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update invoice status: %v", err)
			}
		} else {
			log.Printf("Invoice %s already has PAID status, not updating", invoiceID)
		}

		// 3. Update payment status to PAID - only if not already PAID
		if currentPaymentStatus != "PAID" {
			// Format payment method string
			var paymentMethod string
			if notification.PaymentType != "" {
				components := []string{notification.PaymentType}
				if notification.Issuer != "" {
					components = append(components, notification.Issuer)
				}
				if notification.Acquirer != "" {
					components = append(components, notification.Acquirer)
				}
				paymentMethod = strings.Join(components, "_")
			} else {
				paymentMethod = "midtrans"
			}

			paymentQuery := `
				UPDATE payments
				SET 
					status = 'PAID',
					payment_method = $1,
					transaction_id = $2,
					payment_end_date = CURRENT_TIMESTAMP,
					modified_at = CURRENT_TIMESTAMP
				WHERE invoice_id = $3
			`
			_, err = tx.Exec(paymentQuery, paymentMethod, notification.TransactionID, invoiceID)
			if err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update payment status: %v", err)
			}
		} else {
			log.Printf("Payment for invoice %s already has PAID status, not updating", invoiceID)
		}
	} else {
		// Handle other transaction statuses if needed
		var paymentStatus string
		switch notification.TransactionStatus {
		case "pending":
			paymentStatus = "PENDING"
		case "deny", "cancel", "expire", "failure":
			paymentStatus = "FAILED"
		case "refund", "partial_refund":
			paymentStatus = "REFUNDED"
		default:
			paymentStatus = "PENDING"
		}

		// Only update payment status if it wouldn't downgrade from PAID
		if currentPaymentStatus != "PAID" || paymentStatus == "PAID" {
			// Update payment status based on transaction status
			paymentQuery := `
				UPDATE payments
				SET 
					status = $1,
					transaction_id = $2,
					modified_at = CURRENT_TIMESTAMP
				WHERE invoice_id = $3
			`
			_, err = tx.Exec(paymentQuery, paymentStatus, notification.TransactionID, invoiceID)
			if err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update payment status: %v", err)
			}
		} else {
			log.Printf("Payment for invoice %s already has PAID status, not downgrading to %s",
				invoiceID, paymentStatus)
		}
	}

	// Commit the transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("Successfully updated order and payment for order %s with status %s",
		notification.OrderID, notification.TransactionStatus)

	return nil
}
