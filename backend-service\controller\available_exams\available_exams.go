package controller

import (
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/available_exams"
	repository "github.com/terang-ai/backend-service/repository/available_exams"
)

type AvailableExamController struct {
	Dbx   *sqlx.DB
	Redis *redis.Client
	Repo  repository.AvailableExamsRepositoryInterface
}

func NewAvailableExamsController(dbx *sqlx.DB, redis *redis.Client) *AvailableExamController {
	return &AvailableExamController{
		Dbx:   dbx,
		Redis: redis,
		Repo:  repository.NewAvailableExamRepository(dbx, redis),
	}
}

func (c *AvailableExamController) DeleteAvailableExam(ctx *gin.Context) {
	var uri model.AvailableExamUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete AvailableExam
	deleted, err := c.Repo.DeleteAvailableExam(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete AvailableExam failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "AvailableExam not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete AvailableExam successfully"})
}

func (c *AvailableExamController) GetAllAvailableExams(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAllAvailableExams(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exams retrieved successfully", "_pagination": paginationInfo})
}

func (c *AvailableExamController) GetOneAvailableExam(ctx *gin.Context) {
	var uri model.AvailableExamUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to retrieve one AvailableExam by ID
	exam, err := c.Repo.GetOneAvailableExam(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "AvailableExam not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": exam, "msg": "get AvailableExam successfully"})
}

func (c *AvailableExamController) InsertAvailableExam(ctx *gin.Context) {
	var post model.PostAvailableExam
	// var entity model.AvailableExam
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.InsertAvailableExam(post.UserId, post)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert AvailableExam failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert AvailableExam successfully"})
}

func (c *AvailableExamController) UpdateAvailableExam(ctx *gin.Context) {
	var uri model.AvailableExamUri
	var post model.UpdateAvailableExam

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind JSON body to struct
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update AvailableExam
	updatedExam, err := c.Repo.UpdateAvailableExam(uri.ID, post)
	if err != nil {
		if err.Error() == fmt.Sprintf("availableExam with ID %s does not exist", uri.ID) {
			ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "AvailableExam not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "update AvailableExam failed"})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": updatedExam, "msg": "update AvailableExam successfully"})
}

func (c *AvailableExamController) GetAllAvailableExamsWithCache(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// Call the repository method to get paginated data, including cache status
	entities, paginationInfo, cacheStatus, err := c.Repo.GetAllAvailableExamsWithCache(ctx.Request.Context(), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	// Return the response including cache status
	ctx.JSON(http.StatusOK, gin.H{
		"status":       "success",
		"data":         entities,
		"is_cache_hit": cacheStatus,
		"msg":          "Categories retrieved successfully",
		"_pagination":  paginationInfo,
	})
}

func (c *AvailableExamController) GetAllAvailableExamsV2(ctx *gin.Context) {
	var uri model.AvailableExamUserEmailUri
	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	examType := model.ExamType(ctx.DefaultQuery("examType", string(model.TypeExam))) // Default to "EXAM"

	log.Println(uri.USEREMAIL)

	// Call the repository method with examType as a model.ExamType
	entities, paginationInfo, err := c.Repo.GetAvailableExamBundlesV2(uri.USEREMAIL, page, pageSize, examType)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exams retrieved successfully", "_pagination": paginationInfo})
}

func (c *AvailableExamController) GetPublicAvailableExamsV2(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	examType := model.ExamType(ctx.DefaultQuery("examType", string(model.TypeExam))) // Default to "EXAM"

	// Call the repository method with examType as a model.ExamType
	entities, paginationInfo, err := c.Repo.GetPublicAvailableExamBundles(page, pageSize, examType)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exams retrieved successfully", "_pagination": paginationInfo})
}

func (c *AvailableExamController) GetAllPurchasedExamsByEmail(ctx *gin.Context) {
	var uri model.AvailableExamUserEmailUri
	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// Get examType from query parameter if provided
	examTypeStr := ctx.Query("examType")

	log.Println(uri.USEREMAIL)

	var entities []model.AvailableExamV2
	var paginationInfo repository.AvailableExamPaginationInfo
	var err error

	// Call repository with or without examType based on whether it was provided
	if examTypeStr != "" {
		examType := model.ExamType(examTypeStr)
		entities, paginationInfo, err = c.Repo.GetAllPurchasedExamsIncludingBundles(uri.USEREMAIL, page, pageSize, examType)
	} else {
		entities, paginationInfo, err = c.Repo.GetAllPurchasedExamsIncludingBundles(uri.USEREMAIL, page, pageSize)
	}

	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exams retrieved successfully", "_pagination": paginationInfo})
}

func (c *AvailableExamController) GetAllExamsTaken(ctx *gin.Context) {
	var uri model.AvailableExamUserEmailUri
	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	// Get examType from query parameter if provided
	examTypeStr := ctx.Query("examType")

	log.Println(uri.USEREMAIL)

	var entities []model.ExamSessionData
	var paginationInfo repository.AvailableExamPaginationInfo
	var err error

	// Call repository with or without examType based on whether it was provided
	if examTypeStr != "" {
		examType := model.ExamType(examTypeStr)
		entities, paginationInfo, err = c.Repo.GetAllExamsTaken(uri.USEREMAIL, page, pageSize, examType)
	} else {
		entities, paginationInfo, err = c.Repo.GetAllExamsTaken(uri.USEREMAIL, page, pageSize)
	}

	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exams retrieved successfully", "_pagination": paginationInfo})
}

// InsertExamQuestionAnswerHintHandler handles the insertion of a new exam question answer hint
func (c *AvailableExamController) InsertExamQuestionAnswerHint(ctx *gin.Context) {
	var hint model.PostExamQuestionAnswerHint

	// Bind JSON request body to struct
	if err := ctx.ShouldBindJSON(&hint); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to insert the hint
	insertedData, err := c.Repo.InsertExamQuestionAnswerHint(hint)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "Insert exam question answer hint failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": insertedData, "msg": "Hint inserted successfully"})
}

// GetExamQuestionAnswerHintsHandler handles fetching hints for a specific exam
func (c *AvailableExamController) GetExamQuestionAnswerHints(ctx *gin.Context) {
	examId := ctx.Param("exam_id")
	// Set default value to "LIMIT" if fetchType is empty
	fetchType := ctx.DefaultQuery("fetchType", "LIMIT")

	// Call repository method to get the hints
	hints, err := c.Repo.GetExamQuestionAnswerHints(examId, fetchType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "Failed to fetch exam question answer hints"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": hints, "msg": "Hints retrieved successfully"})
}

// GetExamQuestionAnswerHintsHandler handles fetching hints for a specific exam
func (c *AvailableExamController) GetExamQuestionAnswerHintsBySessionId(ctx *gin.Context) {
	examId := ctx.Param("session_id")

	// Call repository method to get the hints
	hints, err := c.Repo.GetExamQuestionAnswerHintsBySessionId(examId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "Failed to fetch exam question answer hints"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": hints, "msg": "Hints retrieved successfully"})
}

// GetExamQuestionAnswerHintsHandler handles fetching hints for a specific exam
func (c *AvailableExamController) GetExamQuestionAnswerHintsBySessionIdBySubject(ctx *gin.Context) {
	sessionId := ctx.Param("session_id")

	var hint model.PostExamQuestionAnswerHintSubject

	// Bind JSON request body to struct
	if err := ctx.ShouldBindJSON(&hint); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to get the hints
	hints, err := c.Repo.GetExamQuestionAnswerHintsBySessionIdBySubject(sessionId, hint.Subject)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "Failed to fetch exam question answer hints"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": hints, "msg": "Hints retrieved successfully"})
}
