package notifications

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"io"
	"log"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/mailgun/mailgun-go/v4"
	"gopkg.in/yaml.v2"
)

type Config struct {
	Mailgun struct {
		Environment string   `yaml:"environment"`
		Domain      string   `yaml:"domain"`
		APIKey      string   `yaml:"api_key"`
		Sender      string   `yaml:"sender"`
		Recipients  []string `yaml:"recipients"`
	} `yaml:"mailgun"`
}

type SchemaChange struct {
	Type        string // "table", "column", "index", "type", etc.
	Name        string // name of the object being changed
	Action      string // "create", "alter", "drop", etc.
	ParentTable string // parent table if applicable (for columns, indices)
	DDL         string // the actual SQL statement
	Timestamp   time.Time
}

type MigrationSummary struct {
	StartTime    time.Time
	EndTime      time.Time
	InitialState struct {
		Version uint
		IsDirty bool
	}
	FinalState struct {
		Version uint
		IsDirty bool
		Tables  []string
	}
	Events        []MigrationEvent
	Error         error
	SchemaChanges []string
	MigratedFiles []string
	// Add this field:
	SQLStatements []struct {
		Action string
		SQL    string
	}
}

type MigrationNotifier struct {
	mg         *mailgun.MailgunImpl
	sender     string
	recipients []string
	summary    MigrationSummary
	// Add these new fields:
	currentMigrationFile   string
	migrationSQLContents   map[string]string
	migrationSQLStatements []struct {
		Number   int
		SQL      string
		Filename string
	}
}

func ReadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func GetMailgunConfig() (*Config, error) {
	config := &Config{}

	// Check if environment variables exist
	env := os.Getenv("APP_ENV")
	domain := os.Getenv("MAILGUN_DOMAIN")
	apiKey := os.Getenv("MAILGUN_API_KEY")
	sender := os.Getenv("MIGRATION_EMAIL_SENDER")
	recipients := os.Getenv("MIGRATION_EMAIL_RECIPIENTS")

	if domain != "" && apiKey != "" && sender != "" && recipients != "" {
		// Use environment variables
		config.Mailgun.Environment = env
		config.Mailgun.Domain = domain
		config.Mailgun.APIKey = apiKey
		config.Mailgun.Sender = sender
		config.Mailgun.Recipients = []string{recipients}
	} else {
		// Use config.yaml as a fallback
		var err error
		config, err = ReadConfig("configs/config.yaml")
		if err != nil {
			return nil, err
		}
	}

	return config, nil
}

func NewMigrationNotifier(domain, apiKey, sender string, recipients []string) *MigrationNotifier {
	mg := mailgun.NewMailgun(domain, apiKey)
	// Set EU endpoint
	mg.SetAPIBase(mailgun.APIBaseEU)

	return &MigrationNotifier{
		mg:         mg,
		sender:     sender,
		recipients: recipients,
		summary: MigrationSummary{
			StartTime:     time.Now(),
			MigratedFiles: []string{},
			SchemaChanges: []string{},
		},
	}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func (n *MigrationNotifier) SetInitialState(version uint, isDirty bool) {
	n.summary.InitialState.Version = version
	n.summary.InitialState.IsDirty = isDirty
}

func (n *MigrationNotifier) SetFinalState(version uint, isDirty bool, tables []string, err error) {
	n.summary.FinalState.Version = version
	n.summary.FinalState.IsDirty = isDirty
	n.summary.FinalState.Tables = tables
	n.summary.Error = err
	n.summary.EndTime = time.Now()
}

// Improved LogEvent function to better capture SQL statements and migration files
func (n *MigrationNotifier) LogEvent(eventType string, message string, err error) {

	// Add this to the LogEvent function to specifically track files being executed
	if eventType == "Migration" && strings.Contains(message, "Processing migration file:") {
		// Extract the filename
		parts := strings.SplitN(message, "Processing migration file:", 2)
		if len(parts) > 1 {
			filename := strings.TrimSpace(parts[1])
			// Add to MigratedFiles list directly
			if !contains(n.summary.MigratedFiles, filename) {
				n.summary.MigratedFiles = append(n.summary.MigratedFiles, filename)
			}
		}
	}

	// Extract migration file information
	if eventType == "MigrationFile" && strings.HasPrefix(message, "MIGRATION_SQL_FILE:") {
		parts := strings.SplitN(message, "MIGRATION_SQL_FILE:", 2)
		if len(parts) > 1 {
			n.currentMigrationFile = strings.TrimSpace(parts[1])
			log.Printf("[Migration] Set current migration file to: %s", n.currentMigrationFile)

			// Also add to MigratedFiles if not already there
			if !contains(n.summary.MigratedFiles, n.currentMigrationFile) {
				n.summary.MigratedFiles = append(n.summary.MigratedFiles, n.currentMigrationFile)
			}
		}
	}

	// Extract SQL content
	if eventType == "MigrationSQL" && strings.HasPrefix(message, "MIGRATION_SQL_CONTENT:") {
		parts := strings.SplitN(message, "MIGRATION_SQL_CONTENT:", 2)
		if len(parts) > 1 {
			sqlContent := strings.TrimSpace(parts[1])

			// Initialize map if needed
			if n.migrationSQLContents == nil {
				n.migrationSQLContents = make(map[string]string)
			}

			// Store SQL content by filename
			if n.currentMigrationFile != "" {
				log.Printf("[Migration] Storing SQL content for file: %s", n.currentMigrationFile)
				n.migrationSQLContents[n.currentMigrationFile] = sqlContent
			} else if len(n.summary.MigratedFiles) > 0 {
				// Fallback to first migrated file if currentMigrationFile is empty
				fileToUse := n.summary.MigratedFiles[0]
				log.Printf("[Migration] Storing SQL content using fallback file: %s", fileToUse)
				n.migrationSQLContents[fileToUse] = sqlContent
			} else {
				log.Printf("[Migration] WARNING: Cannot store SQL content - no migration file specified")
			}
		}
	}

	// Extract SQL statements
	if eventType == "MigrationStmt" && strings.HasPrefix(message, "MIGRATION_SQL_STMT_") {
		// Extract statement number and SQL
		re := regexp.MustCompile(`MIGRATION_SQL_STMT_(\d+): (.+)`)
		matches := re.FindStringSubmatch(message)
		if len(matches) >= 3 {
			stmtNum, _ := strconv.Atoi(matches[1])
			sqlText := matches[2]

			// Add to statements list
			n.migrationSQLStatements = append(n.migrationSQLStatements,
				struct {
					Number   int
					SQL      string
					Filename string
				}{
					Number:   stmtNum,
					SQL:      sqlText,
					Filename: n.currentMigrationFile,
				})
		}
	}

	// Extract DDL statements
	if eventType == "MigrationDDL" && strings.HasPrefix(message, "MIGRATION_DDL_STMT:") {
		parts := strings.SplitN(message, "MIGRATION_DDL_STMT:", 2)
		if len(parts) > 1 {
			ddlStmt := strings.TrimSpace(parts[1])

			// Add to the summary SQL statements
			action := determineSQLAction(ddlStmt)
			n.summary.SQLStatements = append(n.summary.SQLStatements, struct {
				Action string
				SQL    string
			}{
				Action: action,
				SQL:    ddlStmt,
			})
		}
	}

	// Extract schema changes
	if eventType == "SchemaChange" && strings.HasPrefix(message, "MIGRATION_SCHEMA_CHANGE:") {
		parts := strings.SplitN(message, "MIGRATION_SCHEMA_CHANGE:", 2)
		if len(parts) > 1 {
			schemaChange := strings.TrimSpace(parts[1])
			if !contains(n.summary.SchemaChanges, schemaChange) {
				n.summary.SchemaChanges = append(n.summary.SchemaChanges, schemaChange)
			}
		}
	}

	// Continue with regular event processing...
	// Create event and add to summary
	event := MigrationEvent{
		Type:         eventType,
		Message:      message,
		Timestamp:    time.Now(),
		Error:        err,
		Filename:     n.currentMigrationFile,
		SQLStatement: extractSQLFromMessage(eventType, message),
		Category:     determineCategoryFromType(eventType, message),
	}

	// Add this to the LogEvent function
	if strings.Contains(message, "SQL Content") {
		log.Printf("[Migration] Captured SQL content for file: %s", n.currentMigrationFile)
	}

	n.summary.Events = append(n.summary.Events, event)
}

// Helper function to extract SQL from message
func extractSQLFromMessage(eventType, message string) string {
	if eventType == "MigrationStmt" && strings.HasPrefix(message, "MIGRATION_SQL_STMT_") {
		re := regexp.MustCompile(`MIGRATION_SQL_STMT_\d+: (.+)`)
		matches := re.FindStringSubmatch(message)
		if len(matches) >= 2 {
			return matches[1]
		}
	} else if eventType == "MigrationDDL" && strings.HasPrefix(message, "MIGRATION_DDL_STMT:") {
		parts := strings.SplitN(message, "MIGRATION_DDL_STMT:", 2)
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	} else if eventType == "SQL" && strings.Contains(message, "SQL Statement") {
		parts := strings.SplitN(message, ":", 3)
		if len(parts) > 2 {
			return strings.TrimSpace(parts[2])
		}
	} else if eventType == "DDL" && strings.Contains(message, "DDL Statement:") {
		parts := strings.SplitN(message, "DDL Statement:", 2)
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	}
	return ""
}

// Helper function to determine category
func determineCategoryFromType(eventType, message string) string {
	switch eventType {
	case "MigrationFile", "Migration":
		return "Migration"
	case "MigrationSQL", "MigrationStmt", "MigrationDDL", "SQL", "DDL":
		return "SQL"
	case "SchemaChange":
		return "Schema"
	case "ERROR", "WARNING":
		return "Error"
	default:
		if strings.Contains(message, "version") {
			return "Version"
		}
		return "Info"
	}
}

func getCategoryFromType(eventType, message string) string {
	if eventType == "SQL" || eventType == "DDL" {
		return "SQL"
	} else if eventType == "Migration" || strings.Contains(message, "File:") {
		return "Migration"
	} else if strings.Contains(message, "Schema") || strings.Contains(message, "table") ||
		strings.Contains(message, "column") || strings.Contains(message, "index") {
		return "Schema"
	} else if strings.Contains(message, "version") {
		return "Version"
	}
	return "Info"
}

// Helper function to check if a specific SQL statement is already in the events list
func containsSQLStatement(events []MigrationEvent, sql string) bool {
	for _, event := range events {
		if event.SQLStatement == sql {
			return true
		}
	}
	return false
}

// Extract table name from SQL statements like "CREATE TABLE table_name" or "ALTER TABLE table_name"
func extractTableName(sql, prefix string) string {
	// Convert to uppercase for case-insensitive matching
	sqlUpper := strings.ToUpper(sql)
	prefixUpper := strings.ToUpper(prefix)

	// Find the prefix position
	startPos := strings.Index(sqlUpper, prefixUpper)
	if startPos < 0 {
		return ""
	}

	// Skip the prefix
	startPos += len(prefixUpper)

	// Skip "IF EXISTS" or "IF NOT EXISTS"
	if strings.HasPrefix(strings.ToUpper(sql[startPos:]), " IF NOT EXISTS ") {
		startPos += len(" IF NOT EXISTS ")
	} else if strings.HasPrefix(strings.ToUpper(sql[startPos:]), " IF EXISTS ") {
		startPos += len(" IF EXISTS ")
	} else {
		// Skip any whitespace
		for startPos < len(sql) && unicode.IsSpace(rune(sql[startPos])) {
			startPos++
		}
	}

	// Find the end of the table name (first whitespace, parenthesis or semicolon)
	endPos := startPos
	for endPos < len(sql) &&
		!unicode.IsSpace(rune(sql[endPos])) &&
		sql[endPos] != '(' &&
		sql[endPos] != ';' {
		endPos++
	}

	if startPos < endPos {
		tableName := sql[startPos:endPos]
		// Remove any quotes
		return strings.Trim(tableName, "\"'`[]")
	}

	return ""
}

// Extract object name from SQL statements like "CREATE INDEX index_name" or "CREATE TYPE type_name"
func extractObjectName(sql, prefix string) string {
	// Convert to uppercase for case-insensitive matching
	sqlUpper := strings.ToUpper(sql)
	prefixUpper := strings.ToUpper(prefix)

	// Find the prefix position
	startPos := strings.Index(sqlUpper, prefixUpper)
	if startPos < 0 {
		return ""
	}

	// Skip the prefix
	startPos += len(prefixUpper)

	// Skip "UNIQUE" in "CREATE UNIQUE INDEX"
	if strings.HasPrefix(prefixUpper, "CREATE") && strings.HasPrefix(strings.ToUpper(sql[startPos:]), " UNIQUE ") {
		startPos += len(" UNIQUE ")
	}

	// Skip "IF EXISTS" or "IF NOT EXISTS"
	if strings.HasPrefix(strings.ToUpper(sql[startPos:]), " IF NOT EXISTS ") {
		startPos += len(" IF NOT EXISTS ")
	} else if strings.HasPrefix(strings.ToUpper(sql[startPos:]), " IF EXISTS ") {
		startPos += len(" IF EXISTS ")
	} else {
		// Skip any whitespace
		for startPos < len(sql) && unicode.IsSpace(rune(sql[startPos])) {
			startPos++
		}
	}

	// Find the end of the object name (first whitespace, ON for indexes, or parenthesis)
	endPos := startPos
	for endPos < len(sql) &&
		!unicode.IsSpace(rune(sql[endPos])) &&
		sql[endPos] != '(' &&
		sql[endPos] != ';' {
		endPos++
	}

	if startPos < endPos {
		objectName := sql[startPos:endPos]
		// Remove any quotes
		return strings.Trim(objectName, "\"'`[]")
	}

	return ""
}

// Improved extractSQLStatements to better display actual DDL in emails
func (n *MigrationNotifier) extractSQLStatements() []struct {
	Action string
	SQL    string
} {
	var statements []struct {
		Action string
		SQL    string
	}

	seen := make(map[string]bool)

	// First extract any direct DDL events which have the clearest SQL
	for _, event := range n.summary.Events {
		if event.Type == "DDL" && event.SQLStatement != "" && !seen[event.SQLStatement] {
			seen[event.SQLStatement] = true

			// Determine action based on the SQL content
			action := determineSQLAction(event.SQLStatement)

			statements = append(statements, struct {
				Action string
				SQL    string
			}{
				Action: action,
				SQL:    event.SQLStatement,
			})
		}
	}

	// Then look for SQL statements in regular SQL events
	for _, event := range n.summary.Events {
		if event.Category == "SQL" && event.SQLStatement != "" &&
			!seen[event.SQLStatement] && isImportantSQLStatement(event.SQLStatement) {
			seen[event.SQLStatement] = true

			// Determine action based on the SQL content
			action := determineSQLAction(event.SQLStatement)

			statements = append(statements, struct {
				Action string
				SQL    string
			}{
				Action: action,
				SQL:    event.SQLStatement,
			})
		}
	}

	return statements
}

// Helper function to capitalize first letter of string
func capitalize(s string) string {
	if s == "" {
		return ""
	}
	return strings.ToUpper(s[:1]) + s[1:]
}

// Helper function to check if SQL statement is a schema change
func isSchemaChangeSQL(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "CREATE TABLE") ||
		strings.Contains(sqlUpper, "ALTER TABLE") ||
		strings.Contains(sqlUpper, "CREATE INDEX") ||
		strings.Contains(sqlUpper, "DROP TABLE") ||
		strings.Contains(sqlUpper, "CREATE TYPE")
}

// Extract the action part from an SQL log message
func extractActionFromSQL(message string) string {
	// Common patterns: "Creating table", "Adding column", etc.
	patterns := []string{"Creating table", "Adding column", "Creating index", "Dropping", "Altering"}

	for _, pattern := range patterns {
		if strings.Contains(message, pattern) {
			return pattern
		}
	}

	// Fallback to the beginning of the message
	words := strings.Split(message, " ")
	if len(words) > 0 {
		return words[0]
	}

	return ""
}

// Extract the object being changed from an SQL log message
func extractObjectFromSQL(message string) string {
	// Try to extract content between single quotes
	re := regexp.MustCompile(`'([^']+)'`)
	matches := re.FindAllStringSubmatch(message, -1)

	if len(matches) > 0 && len(matches[0]) > 1 {
		return matches[0][1]
	}

	return ""
}

// Updated MigrationEvent struct to include SQL info
type MigrationEvent struct {
	Type         string
	Message      string
	Timestamp    time.Time
	Error        error
	Filename     string
	Changes      []SchemaChange
	SQLStatement string // Added for SQL query logging
	Category     string // Added for better filtering
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

func (n *MigrationNotifier) SendMigrationSummary() error {
	// Get config for environment check
	config, err := GetMailgunConfig()
	if err != nil {
		return fmt.Errorf("failed to get mailgun config: %v", err)
	}

	// Skip sending email if in local environment
	if config.Mailgun.Environment == "local" {
		log.Printf("[Migration] Skipping email notification - running in local environment")
		return nil
	}

	// Skip sending email if no version change and no error
	if n.summary.InitialState.Version == n.summary.FinalState.Version && n.summary.Error == nil {
		log.Printf("[Migration] Skipping email notification - no version change detected (version remains at %d)",
			n.summary.InitialState.Version)
		return nil
	}

	// Clean up events and remove duplicates
	n.cleanupEvents()

	log.Printf("[Migration] SQL contents map has %d entries", len(n.migrationSQLContents))
	for k, v := range n.migrationSQLContents {
		log.Printf("[Migration] SQL content for %s: %s...", k, truncateString(v, 40))
	}
	if len(n.summary.MigratedFiles) > 0 {
		log.Printf("[Migration] First migrated file: %s", n.summary.MigratedFiles[0])
		content, exists := n.migrationSQLContents[n.summary.MigratedFiles[0]]
		log.Printf("[Migration] SQL content lookup result: exists=%v, content=%s...",
			exists, truncateString(content, 40))
	}

	// Extract tables before and after migration
	var beforeTables, afterTables []string

	// Look for "GetTables" events to find tables before migration
	for _, event := range n.summary.Events {
		if strings.Contains(event.Message, "[GetTables] Found") &&
			strings.Contains(event.Message, "tables:") {
			// Extract the table list
			parts := strings.Split(event.Message, "tables:")
			if len(parts) > 1 {
				tablesPart := strings.TrimSpace(parts[1])
				// Remove brackets
				tablesPart = strings.Trim(tablesPart, "[]")
				// Split by spaces and commas
				tableParts := strings.Split(tablesPart, " ")

				// Clean up each table name
				for _, part := range tableParts {
					tableName := strings.Trim(part, ", ")
					if tableName != "" {
						beforeTables = append(beforeTables, tableName)
					}
				}

				// We found the tables, so break out of the loop
				break
			}
		}
	}

	// If we still don't have before tables, try a different approach
	if len(beforeTables) == 0 {
		// Look for any message containing "Found X tables:"
		for _, event := range n.summary.Events {
			if match, _ := regexp.MatchString(`Found \d+ tables:`, event.Message); match {
				// Extract the table list
				parts := strings.SplitN(event.Message, "tables:", 2)
				if len(parts) > 1 {
					tablesPart := strings.TrimSpace(parts[1])
					// Remove brackets
					tablesPart = strings.Trim(tablesPart, "[]")
					// Split by commas
					tableParts := strings.Split(tablesPart, ",")

					// Clean up each table name
					for _, part := range tableParts {
						tableName := strings.TrimSpace(part)
						if tableName != "" {
							beforeTables = append(beforeTables, tableName)
						}
					}

					// We found the tables, so break out of the loop
					break
				}
			}
		}
	}

	// Use final state tables for after tables
	afterTables = n.summary.FinalState.Tables

	// Rest of the email template and sending logic...
	const emailTemplate = `
	<!DOCTYPE html>
	<html>
	<head>
		<style>
			body { 
				font-family: Arial, sans-serif; 
				color: #333;
				line-height: 1.6;
				margin: 0;
				padding: 20px;
			}
			.success { color: #4caf50; font-weight: bold; }
			.error { color: #f44336; font-weight: bold; }
			.warning { color: #ff9800; font-weight: bold; }
			.info { color: #2196f3; font-weight: bold; }
			.migration-file { color: #673ab7; font-weight: bold; }
			.schema-change { color: #009688; }
			.container {
				max-width: 900px;
				margin: 0 auto;
				background-color: #fff;
				box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
				border-radius: 8px;
				overflow: hidden;
			}
			.header {
				background-color: #0d47a1; /* Bright blue that works in both light/dark modes */
				color: #ffffff; /* Bright white */
				padding: 20px;
				text-align: center;
			}
			.header h2 {
				margin: 0;
				font-size: 24px;
				font-weight: bold; /* Bold for improved visibility */
				text-shadow: 1px 1px 2px rgba(0,0,0,0.5); /* Stronger text shadow for better contrast */
				letter-spacing: 0.5px; /* Slightly increased letter spacing */
			}
			.content {
				padding: 20px;
			}
			.event-log { 
				background-color: #f8f9fa; 
				padding: 15px; 
				border-radius: 6px;
				margin: 10px 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.12);
				overflow: auto;
			}
			.status-box {
				padding: 15px;
				border-radius: 6px;
				margin: 15px 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.12);
			}
			.error-box {
				background-color: #ffebee;
				padding: 15px;
				border-radius: 6px;
				margin: 10px 0;
				border-left: 4px solid #f44336;
			}
			.env-info {
				background-color: #e3f2fd;
				padding: 15px;
				border-radius: 6px;
				margin: 10px 0;
				border-left: 4px solid #2196f3;
			}
			.migration-files {
				background-color: #f3e5f5;
				padding: 15px;
				border-radius: 6px;
				margin: 10px 0;
				border-left: 4px solid #9c27b0;
			}
			.schema-changes {
				background-color: #e0f2f1;
				padding: 15px;
				border-radius: 6px;
				margin: 10px 0;
				border-left: 4px solid #009688;
			}
			.sql-changes {
				background-color: #FFF8E1;
				padding: 15px;
				border-radius: 6px;
				margin: 10px 0;
				border-left: 4px solid #FFC107;
				font-family: monospace;
			}
			.sql-code {
				background-color: #f5f5f5;
				padding: 8px 12px;
				border-radius: 4px;
				font-family: 'Courier New', monospace;
				margin: 5px 0;
				border-left: 3px solid #9c27b0;
				white-space: pre-wrap;
				word-break: break-all;
			}
			table {
				width: 100%;
				border-collapse: collapse;
			}
			table, th, td {
				border: 1px solid #ddd;
			}
			th, td {
				padding: 12px;
				text-align: left;
			}
			th {
				background-color: #f2f2f2;
			}
			h2, h3 {
				color: #333;
				margin-top: 25px;
				border-bottom: 1px solid #eee;
				padding-bottom: 10px;
			}
			.badge {
				display: inline-block;
				padding: 3px 7px;
				border-radius: 3px;
				color: white;
				font-size: 12px;
				font-weight: bold;
			}
			.badge-success {
				background-color: #2e7d32; /* Darkened for better contrast */
			}
			.badge-error {
				background-color: #d32f2f; /* Darkened for better contrast */
			}
			.badge-warning {
				background-color: #ef6c00; /* Darkened for better contrast */
			}
			.badge-info {
				background-color: #0d47a1; /* Darkened for better contrast */
			}
			.summary {
				margin-top: 20px;
				padding: 15px;
				background-color: #f9f9f9;
				border-radius: 6px;
				border-left: 4px solid #673ab7;
			}
			.footer {
				background-color: #f5f5f5;
				padding: 15px;
				text-align: center;
				font-size: 12px;
				color: #666;
				border-top: 1px solid #ddd;
			}
			.change-icon {
				margin-right: 5px;
				font-weight: bold;
			}
			.no-changes {
				color: #666;
				font-style: italic;
			}
			/* SQL content styles */
			.sql-content {
				background-color: #f8f9fa;
				padding: 15px;
				border-radius: 6px;
				margin: 15px 0;
				border-left: 4px solid #2196f3;
				font-family: monospace;
				white-space: pre-wrap;
				overflow-x: auto;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<div class="header">
				<h2>Database Migration Summary</h2>
			</div>
			
			<div class="content">
				<div class="env-info">
					<h3>Environment Information</h3>
					<table>
						<tr>
							<th width="30%">Environment</th>
							<td>{{.Environment}}</td>
						</tr>
						<tr>
							<th>Start Time</th>
							<td>{{.MigrationSummary.StartTime.Format "Jan 02, 2006 15:04:05 MST"}}</td>
						</tr>
						<tr>
							<th>End Time</th>
							<td>{{.MigrationSummary.EndTime.Format "Jan 02, 2006 15:04:05 MST"}}</td>
						</tr>
						<tr>
							<th>Duration</th>
							<td>{{.Duration}}</td>
						</tr>
					</table>
				</div>
	
				<div class="status-box" style="background-color: {{if .Error}}#ffebee{{else}}#e8f5e9{{end}}">
					<h3>Migration Status: 
						<span class="badge {{if .Error}}badge-error{{else}}badge-success{{end}}">
							{{if .Error}}Failed{{else}}Success{{end}}
						</span>
					</h3>
					<table>
						<tr>
							<th width="30%">Initial Version</th>
							<td>{{.InitialState.Version}} {{if .InitialState.IsDirty}}<span class="badge badge-warning">Dirty</span>{{end}}</td>
						</tr>
						<tr>
							<th>Final Version</th>
							<td>{{.FinalState.Version}} {{if .FinalState.IsDirty}}<span class="badge badge-error">Dirty</span>{{end}}</td>
						</tr>
					</table>
				</div>
				
				{{if hasChanges .InitialState.Version .FinalState.Version}}
				<div class="summary">
					<h3>Migration Summary</h3>
					<p>
						{{if gt .FinalState.Version .InitialState.Version}}
						<span class="success">✅ Successfully migrated database from version {{.InitialState.Version}} to {{.FinalState.Version}}</span>
						{{else if lt .FinalState.Version .InitialState.Version}}
						<span class="warning">⚠️ Database rolled back from version {{.InitialState.Version}} to {{.FinalState.Version}}</span>
						{{else}}
						<span class="info">ℹ️ No version change, database remains at version {{.InitialState.Version}}</span>
						{{end}}
					</p>
					
					{{if .MigratedFiles}}
					<p class="migration-file">
						<strong>Executed Migration File:</strong> {{index .MigratedFiles 0}}
					</p>
					{{end}}
					
					{{if .SQLContent}}
					<div class="sql-code">{{.SQLContent}}</div>
					{{end}}
					
					{{if .FinalState.Tables}}
					<p>Current database has {{len .FinalState.Tables}} tables.</p>
					{{end}}
				</div>
				{{end}}
				
				{{if .MigratedFiles}}
				<div class="migration-files">
					<h3>Migration Files Executed</h3>
					<ul>
						{{range .MigratedFiles}}
						<li class="migration-file">{{.}}</li>
						{{end}}
					</ul>
				</div>
				{{end}}
				
				{{if .SQLStatements}}
				<div class="sql-changes">
					<h3>SQL Statements Executed</h3>
					{{range .SQLStatements}}
					<div>
						<strong>{{.Action}}:</strong>
						<div class="sql-code">{{.SQL}}</div>
					</div>
					{{end}}
				</div>
				{{end}}
				
				{{if or .BeforeTables .AfterTables}}
				<div class="schema-changes">
					<h3>Database Tables Comparison</h3>
					<table>
						<tr>
							<th width="50%">Tables Before Migration ({{len .BeforeTables}})</th>
							<th width="50%">Tables After Migration ({{len .AfterTables}})</th>
						</tr>
						<tr>
							<td style="vertical-align: top;">
								{{if .BeforeTables}}
								<ul style="margin: 0; padding-left: 20px;">
									{{range .BeforeTables}}
									<li>{{.}}</li>
									{{end}}
								</ul>
								{{else}}
								<em>No tables data available</em>
								{{end}}
							</td>
							<td style="vertical-align: top;">
								{{if .AfterTables}}
								<ul style="margin: 0; padding-left: 20px;">
									{{range .AfterTables}}
									<li>{{.}}</li>
									{{end}}
								</ul>
								{{else}}
								<em>No tables data available</em>
								{{end}}
							</td>
						</tr>
					</table>
				</div>
				{{end}}
				
				{{if .SQLContent}}
				<div class="sql-content">
					<h3>Migration SQL Content</h3>
					<pre>{{.SQLContent}}</pre>
				</div>
				{{end}}
				
				{{if .SQLStmts}}
				<div class="sql-changes">
					<h3>Individual SQL Statements</h3>
					{{range .SQLStmts}}
					<div>
						<strong>Statement {{.Number}}:</strong>
						<div class="sql-code">{{.SQL}}</div>
					</div>
					{{end}}
				</div>
				{{end}}
				
				{{if .SchemaChanges}}
				<div class="schema-changes">
					<h3>Schema Changes</h3>
					<ul>
						{{range .SchemaChanges}}
						<li class="schema-change">
							{{if hasPrefix . "Creating"}}
								<span class="change-icon">➕</span>
							{{else if hasPrefix . "Dropping"}}
								<span class="change-icon">❌</span>
							{{else if hasPrefix . "Altering"}}
								<span class="change-icon">🔄</span>
							{{else if hasPrefix . "Adding"}}
								<span class="change-icon">➕</span>
							{{else}}
								<span class="change-icon">⚙️</span>
							{{end}}
							{{.}}
						</li>
						{{end}}
					</ul>
				</div>
				{{end}}
			
				{{if .Error}}
				<h3>Error Details</h3>
				<div class="error-box">
					{{.Error}}
				</div>
				{{end}}
				
				{{if hasChanges .InitialState.Version .FinalState.Version}}
				<h3>Important Migration Events</h3>
				<div class="event-log">
					<table>
						<tr>
							<th>Time</th>
							<th>Type</th>
							<th>Message</th>
						</tr>
						{{range .FilteredEvents}}
						<tr>
							<td>{{.Timestamp.Format "15:04:05"}}</td>
							<td>
								<span class="badge 
									{{if eq .Type "ERROR"}}badge-error
									{{else if eq .Type "WARNING"}}badge-warning
									{{else if eq .Type "SQL"}}badge-info
									{{else if eq .Type "Migration"}}badge-success
									{{else}}badge-info{{end}}">
									{{.Type}}
								</span>
							</td>
							<td>
								{{.Message}}
								{{if .Error}}<br><span class="error">Error: {{.Error}}</span>{{end}}
								{{if .Filename}}<br><span class="migration-file">File: {{.Filename}}</span>{{end}}
								{{if .SQLStatement}}<br><div class="sql-code">{{.SQLStatement}}</div>{{end}}
							</td>
						</tr>
						{{end}}
					</table>
				</div>
				{{end}}
			</div>
			
			<div class="footer">
				<p>Database Migration Service | Generated on {{.MigrationSummary.EndTime.Format "Jan 02, 2006 15:04:05 MST"}}</p>
			</div>
		</div>
	</body>
	</html>
	`
	// Extract SQL statements
	var sqlStmts []struct {
		Number int
		SQL    string
	}

	for _, event := range n.summary.Events {
		if strings.Contains(event.Message, "SQL Statement") {
			parts := strings.Split(event.Message, ":")
			if len(parts) >= 3 {
				numberStr := strings.TrimSpace(strings.TrimPrefix(parts[0], "SQL Statement"))
				number, err := strconv.Atoi(numberStr)
				if err == nil {
					sqlStmts = append(sqlStmts, struct {
						Number int
						SQL    string
					}{
						Number: number,
						SQL:    strings.TrimSpace(parts[2]),
					})
				}
			}
		}
	}

	// Extract SQL statements with actions
	var sqlStatements []struct {
		Action string
		SQL    string
	}

	// Parse events to find SQL with actions
	for _, event := range n.summary.Events {
		if event.Type == "DDL" && event.SQLStatement != "" {
			action := determineSQLAction(event.SQLStatement)
			sqlStatements = append(sqlStatements, struct {
				Action string
				SQL    string
			}{
				Action: action,
				SQL:    event.SQLStatement,
			})
		}
	}

	// Create custom template functions
	funcMap := template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
		"hasChanges": func(initialVersion, finalVersion uint) bool {
			return initialVersion != finalVersion || n.summary.Error != nil
		},
		"hasPrefix": func(s, prefix string) bool {
			return strings.HasPrefix(s, prefix)
		},
	}

	t, err := template.New("migration").Funcs(funcMap).Parse(emailTemplate)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %v", err)
	}

	var buf bytes.Buffer

	// Filter events for better email content
	filteredEvents := n.filterImportantEvents()

	// Extract the raw SQL statements from migration file events
	type FileExecution struct {
		Filename   string
		Statements []string
	}

	var fileExecutions []FileExecution

	// Extract file and SQL information from events
	fileMap := make(map[string][]string)

	for _, event := range n.summary.Events {
		// Extract filename from Migration events
		if event.Type == "Migration" && event.Message != "" &&
			strings.HasPrefix(event.Message, "File:") {
			filename := strings.TrimSpace(strings.TrimPrefix(event.Message, "File:"))
			if filename != "" && len(fileMap[filename]) == 0 {
				fileMap[filename] = []string{}
			}
		}

		// Extract SQL statements from SQL events and attach to latest file
		if event.Type == "SQL" && event.SQLStatement != "" {
			// Find the latest file
			var latestFile string
			for filename := range fileMap {
				if latestFile == "" || filename > latestFile {
					latestFile = filename
				}
			}

			if latestFile != "" {
				fileMap[latestFile] = append(fileMap[latestFile], event.SQLStatement)
			}
		}
	}

	// Convert map to slice for template
	for filename, statements := range fileMap {
		fileExecutions = append(fileExecutions, FileExecution{
			Filename:   filename,
			Statements: statements,
		})
	}

	data := struct {
		MigrationSummary
		Duration       string
		Environment    string
		Domain         string
		Sender         string
		MigratedFiles  []string
		SchemaChanges  []string
		FilteredEvents []MigrationEvent
		SQLStatements  []struct {
			Action string
			SQL    string
		}
		MigrationSQLContents   map[string]string
		MigrationSQLStatements []struct {
			Number   int
			SQL      string
			Filename string
		}
		SQLContent string
		SQLStmts   []struct {
			Number int
			SQL    string
		}
		BeforeTables []string // Add this field
		AfterTables  []string // Add this field
	}{
		MigrationSummary:       n.summary,
		Duration:               n.summary.EndTime.Sub(n.summary.StartTime).String(),
		Environment:            config.Mailgun.Environment,
		Domain:                 config.Mailgun.Domain,
		Sender:                 config.Mailgun.Sender,
		MigratedFiles:          n.summary.MigratedFiles,
		SchemaChanges:          n.summary.SchemaChanges,
		FilteredEvents:         filteredEvents,
		SQLStatements:          n.summary.SQLStatements,
		MigrationSQLContents:   n.migrationSQLContents,
		MigrationSQLStatements: n.migrationSQLStatements,
		SQLContent:             getSQLContent(n.migrationSQLContents, n.summary.MigratedFiles),
		SQLStmts:               sqlStmts,
		BeforeTables:           beforeTables, // Add before tables
		AfterTables:            afterTables,  // Add after tables
	}

	if err := t.Execute(&buf, data); err != nil {
		return fmt.Errorf("failed to execute template: %v", err)
	}

	subject := fmt.Sprintf("[%s] Database Migration %s - v%d",
		strings.ToUpper(config.Mailgun.Environment),
		map[bool]string{true: "Failed", false: "Completed"}[n.summary.Error != nil],
		n.summary.FinalState.Version,
	)

	message := mailgun.NewMessage(n.sender, subject, "", n.recipients...)
	message.SetHTML(buf.String())

	// CRITICAL: Make sure we're using the EU endpoint for Mailgun
	n.mg.SetAPIBase(mailgun.APIBaseEU)

	// Log the attempt
	log.Printf("[Migration] Attempting to send email to %v", n.recipients)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, id, err := n.mg.Send(ctx, message)
	if err != nil {
		log.Printf("[Migration] Failed to send email: %v", err)
		return fmt.Errorf("failed to send email: %v", err)
	}

	log.Printf("[Migration] Email sent successfully! Response: %s, ID: %s", resp, id)
	return nil
}

// Helper function to get the SQL content of the first migrated file
func getSQLContent(contentMap map[string]string, files []string) string {
	if len(files) == 0 {
		return ""
	}

	// Try direct lookup first
	content, exists := contentMap[files[0]]
	if exists {
		return content
	}

	// Try checking keys without path
	for k, v := range contentMap {
		if strings.HasSuffix(k, files[0]) {
			return v
		}
	}

	// If we get here, we didn't find the content
	return ""
}

// Helper function to determine SQL action from statement - add if missing
func determineSQLAction(sql string) string {
	upperSQL := strings.ToUpper(sql)

	if strings.Contains(upperSQL, "CREATE TABLE") {
		return "Create Table"
	} else if strings.Contains(upperSQL, "ALTER TABLE") {
		if strings.Contains(upperSQL, "ADD COLUMN") {
			return "Add Column"
		} else if strings.Contains(upperSQL, "DROP COLUMN") {
			return "Drop Column"
		} else if strings.Contains(upperSQL, "ALTER COLUMN") {
			return "Alter Column"
		} else {
			return "Alter Table"
		}
	} else if strings.Contains(upperSQL, "DROP TABLE") {
		return "Drop Table"
	} else if strings.Contains(upperSQL, "CREATE INDEX") {
		if strings.Contains(upperSQL, "UNIQUE") {
			return "Create Unique Index"
		} else {
			return "Create Index"
		}
	} else if strings.Contains(upperSQL, "DROP INDEX") {
		return "Drop Index"
	} else if strings.Contains(upperSQL, "CREATE TYPE") {
		return "Create Type"
	} else if strings.Contains(upperSQL, "ALTER TYPE") {
		return "Alter Type"
	} else if strings.Contains(upperSQL, "DROP TYPE") {
		return "Drop Type"
	}

	return "SQL Statement"
}

func prepareFileExecutions(events []MigrationEvent) []struct {
	Filename   string
	Statements []string
} {
	fileMap := make(map[string][]string)
	var result []struct {
		Filename   string
		Statements []string
	}

	// First group SQL statements by file
	var currentFile string
	for _, event := range events {
		if event.Filename != "" {
			currentFile = event.Filename
			if fileMap[currentFile] == nil {
				fileMap[currentFile] = []string{}
			}
		}

		if currentFile != "" && event.SQLStatement != "" {
			fileMap[currentFile] = append(fileMap[currentFile], event.SQLStatement)
		}
	}

	// Convert map to slice
	for file, stmts := range fileMap {
		if len(stmts) > 0 {
			result = append(result, struct {
				Filename   string
				Statements []string
			}{
				Filename:   file,
				Statements: stmts,
			})
		}
	}

	return result
}

// Clean up events to remove duplicates and noise
func (n *MigrationNotifier) cleanupEvents() {
	// Deduplicate schema changes
	uniqueChanges := make(map[string]bool)
	var cleanChanges []string

	for _, change := range n.summary.SchemaChanges {
		if !uniqueChanges[change] && !isIgnoredChange(change) {
			uniqueChanges[change] = true
			cleanChanges = append(cleanChanges, change)
		}
	}

	n.summary.SchemaChanges = cleanChanges

	// Deduplicate migrated files
	uniqueFiles := make(map[string]bool)
	var cleanFiles []string

	for _, file := range n.summary.MigratedFiles {
		if !uniqueFiles[file] {
			uniqueFiles[file] = true
			cleanFiles = append(cleanFiles, file)
		}
	}

	n.summary.MigratedFiles = cleanFiles
}

// Helper to check if a change should be ignored (generic or redundant)
func isIgnoredChange(change string) bool {
	// Ignore generic messages
	lowChange := strings.ToLower(change)
	return strings.Contains(lowChange, "schema changes summary") ||
		strings.Contains(lowChange, "detailed changes") ||
		strings.Contains(lowChange, "--------------------") ||
		strings.Contains(lowChange, "no tables added") ||
		strings.Contains(lowChange, "taking schema snapshot")
}

// Filter for only important events - creates a concise list of events for the email notification
// Filter for only important events - creates a concise list of events for the email notification
func (n *MigrationNotifier) filterImportantEvents() []MigrationEvent {
	var filtered []MigrationEvent
	seen := make(map[string]bool)

	// Get current and previous version to determine which migrations were actually run
	currentVersion := n.summary.InitialState.Version
	newVersion := n.summary.FinalState.Version

	// Check if there was an actual version change
	versionChanged := currentVersion != newVersion

	for _, event := range n.summary.Events {
		// Always include errors and warnings
		if event.Error != nil || event.Type == "ERROR" || event.Type == "WARNING" {
			filtered = append(filtered, event)
			continue
		}

		// For migration file events, only include ones that match the version that was just migrated
		if event.Type == "Migration" && strings.Contains(event.Message, "File:") {
			// Extract version from filename using regex
			re := regexp.MustCompile(`(\d{6})_`)
			matches := re.FindStringSubmatch(event.Message)

			if len(matches) >= 2 {
				fileVersion, err := strconv.ParseUint(matches[1], 10, 32)

				// Only include if the file version matches our new version
				// and only if we actually changed versions
				if err == nil && versionChanged && uint(fileVersion) == newVersion {
					key := fmt.Sprintf("file-%s", event.Message)
					if !seen[key] {
						seen[key] = true
						filtered = append(filtered, event)
						continue
					}
				}
			}
		}

		// Include SQL statements specifically related to the migration
		if event.Category == "SQL" && event.SQLStatement != "" {
			// Only include important SQL statements
			if isImportantSQLStatement(event.SQLStatement) {
				key := fmt.Sprintf("sql-%s", event.SQLStatement)
				if !seen[key] {
					seen[key] = true
					filtered = append(filtered, event)
					continue
				}
			}
		}

		// Include version statements only for the initial and final versions
		if event.Category == "Version" && strings.Contains(event.Message, "version") {
			// Make sure we capture the before and after versions
			if strings.Contains(event.Message, fmt.Sprintf("Current version: %d", currentVersion)) ||
				strings.Contains(event.Message, fmt.Sprintf("Current version: %d", newVersion)) {
				key := fmt.Sprintf("version-%s", event.Message)
				if !seen[key] {
					seen[key] = true
					filtered = append(filtered, event)
					continue
				}
			}
		}

		// Include specific migration success messages
		if versionChanged &&
			strings.Contains(event.Message, "Successfully migrated from version") &&
			strings.Contains(event.Message, fmt.Sprintf("%d to %d", currentVersion, newVersion)) {
			key := fmt.Sprintf("success-%s", event.Message)
			if !seen[key] {
				seen[key] = true
				filtered = append(filtered, event)
				continue
			}
		}

		// Include schema changes
		if event.Category == "Schema" && isSubstantiveSchemaChange(event.Message) {
			key := fmt.Sprintf("schema-%s", event.Message)
			if !seen[key] {
				seen[key] = true
				filtered = append(filtered, event)
				continue
			}
		}
	}

	// Sort events chronologically
	sort.Slice(filtered, func(i, j int) bool {
		return filtered[i].Timestamp.Before(filtered[j].Timestamp)
	})

	return filtered
}

// Helper function to determine if a schema change message represents a substantive change
func isSubstantiveSchemaChange(message string) bool {
	lowerMsg := strings.ToLower(message)
	return strings.Contains(lowerMsg, "creat") ||
		strings.Contains(lowerMsg, "alter") ||
		strings.Contains(lowerMsg, "drop") ||
		strings.Contains(lowerMsg, "add") ||
		strings.HasPrefix(message, "➕") || // Added item indicator
		strings.HasPrefix(message, "❌") || // Removed item indicator
		strings.HasPrefix(message, "🔄") // Modified item indicator
}

// Check if an SQL statement is important enough to include
func isImportantSQLStatement(sql string) bool {
	upperSQL := strings.ToUpper(sql)
	return strings.Contains(upperSQL, "CREATE TABLE") ||
		strings.Contains(upperSQL, "ALTER TABLE") ||
		strings.Contains(upperSQL, "CREATE INDEX") ||
		strings.Contains(upperSQL, "DROP TABLE") ||
		strings.Contains(upperSQL, "DROP INDEX") ||
		strings.Contains(upperSQL, "ADD COLUMN") ||
		strings.Contains(upperSQL, "DROP COLUMN") ||
		strings.Contains(upperSQL, "CREATE TYPE")
}

// Determine if an event is important enough to include in email
func isImportantEvent(event MigrationEvent) bool {
	// Include all error and warning events
	if event.Type == "ERROR" || event.Type == "WARNING" {
		return true
	}

	// Include SQL statements
	if event.SQLStatement != "" {
		return true
	}

	// Include migration file events
	if event.Filename != "" {
		return true
	}

	// Include schema change events
	if event.Category == "Schema" {
		// But only those with actual changes
		if strings.Contains(event.Message, "create") ||
			strings.Contains(event.Message, "alter") ||
			strings.Contains(event.Message, "drop") ||
			strings.Contains(event.Message, "add") ||
			strings.HasPrefix(event.Message, "➕") ||
			strings.HasPrefix(event.Message, "❌") {
			return true
		}
	}

	// Include migration state changes
	if event.Category == "Version" {
		return true
	}

	// Include migration summary events
	if strings.Contains(event.Message, "migration") &&
		(strings.Contains(event.Message, "complete") || strings.Contains(event.Message, "success")) {
		return true
	}

	// Skip other more verbose events
	return false
}
