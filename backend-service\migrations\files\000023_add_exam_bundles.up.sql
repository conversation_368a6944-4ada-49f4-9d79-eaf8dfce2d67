-- Create exam bundles table
CREATE TABLE IF NOT EXISTS exam_bundles (
    id character varying(26) NOT NULL,
    name character varying(255) NOT NULL,
    description bytea NOT NULL,
    price numeric(18,2) NOT NULL,
    discount_percentage integer,
    thumbnail_url character varying(1000),
    banner_url character varying(1000),
    visibility visibility_status DEFAULT 'DRAFT'::visibility_status,
    valid_from timestamp with time zone,
    valid_until timestamp with time zone,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_user_exam_bundles FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create exam bundle items mapping table
CREATE TABLE IF NOT EXISTS exam_bundle_items (
    id character varying(26) NOT NULL,
    bundle_id character varying(26) NOT NULL,
    exam_id character varying(26) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_bundle FOREIGN KEY (bundle_id) REFERENCES exam_bundles(id) ON DELETE CASCADE,
    CONSTRAINT fk_exam FOREIGN KEY (exam_id) REFERENCES available_exams(id)
);

-- Add bundle_id to orders table
ALTER TABLE orders ADD COLUMN bundle_id character varying(26);
ALTER TABLE orders ADD CONSTRAINT orders_bundle_id_fkey 
    FOREIGN KEY (bundle_id) REFERENCES exam_bundles(id);

-- Create triggers for modified_at fields
CREATE TRIGGER exam_bundles_modified_at 
BEFORE UPDATE ON public.exam_bundles 
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER exam_bundle_items_modified_at 
BEFORE UPDATE ON public.exam_bundle_items 
FOR EACH ROW EXECUTE FUNCTION update_modified_at();

-- Create indexes for performance
CREATE INDEX idx_exam_bundles_user_id ON public.exam_bundles USING btree (user_id);
CREATE INDEX idx_exam_bundles_visibility ON public.exam_bundles USING btree (visibility);
CREATE INDEX idx_exam_bundles_price ON public.exam_bundles USING btree (price);
CREATE INDEX idx_exam_bundles_valid_dates ON public.exam_bundles USING btree (valid_from, valid_until);
CREATE INDEX idx_exam_bundles_metadata ON public.exam_bundles USING gin (metadata);
CREATE INDEX idx_exam_bundle_items_bundle_id ON public.exam_bundle_items USING btree (bundle_id);
CREATE INDEX idx_exam_bundle_items_exam_id ON public.exam_bundle_items USING btree (exam_id);
CREATE INDEX idx_orders_bundle_id ON public.orders USING btree (bundle_id);