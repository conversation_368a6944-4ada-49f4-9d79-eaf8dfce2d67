CREATE TABLE interview_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id VARCHAR(255) NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    interview_id VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    type VA<PERSON>HAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'in_progress',
    recording_url VARCHAR(255),
    transcript_url VARCHAR(255),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_interview_sessions_user_id ON interview_sessions(user_id);
CREATE INDEX idx_interview_sessions_status ON interview_sessions(status);

-- Add foreign key to interview_grading
ALTER TABLE interview_grading 
    ADD COLUMN session_id_fk INTEGER REFERENCES interview_sessions(id),
    ADD CONSTRAINT unique_session_id UNIQUE (session_id);
