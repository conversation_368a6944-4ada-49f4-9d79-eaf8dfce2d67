package repository

import (
	"context"

	model "github.com/terang-ai/backend-service/model/users"
)

type Repository interface {
	Delete(id string) (bool, error)
	GetAll(page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error)
	GetAllBySomethingById(something string, id string, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error)
	GetOneBySomethingById(something string, id string, something2 string, id2 string, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error)
	GetAllWithCache(ctx context.Context, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, bool, error)
	GetOne(id string, entity interface{}) (interface{}, error)
	GetOneBySomething(something string, val string, entity interface{}) (interface{}, error)
	GetOneBySomethingWithCache(ctx context.Context, something string, val string, entity interface{}) (interface{}, bool, error)
	Insert(post interface{}, entity interface{}) (interface{}, error)
	InsertSerial(post interface{}, entity interface{}) (interface{}, error)
	InsertSerialToken(post model.PostUserVerificationRequest, entity interface{}) (interface{}, error)
	Update(id string, updates interface{}, entity interface{}) (interface{}, error)
	UpdateBySomething(something string, val string, updates interface{}, entity interface{}) (interface{}, error)
}
