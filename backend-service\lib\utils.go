package lib

import (
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"regexp"
	"strings"
	"unicode"

	"github.com/iancoleman/strcase"
	"github.com/microcosm-cc/bluemonday"
)

func ToCamelCase(snake string) string {
	return strcase.ToCamel(snake)
}

func ValidateEmail(email string) error {
	// Regular expression pattern for basic email validation
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	match, _ := regexp.MatchString(emailRegex, email)
	if !match {
		return errors.New("invalid email format")
	}
	return nil
}

func EncodeImageToBase64(imageBytes []byte) (string, error) {
	encodedString := base64.StdEncoding.EncodeToString(imageBytes)
	return encodedString, nil
}

func GetMimeAndExtension(imagePath string) (string, string, error) {
	mimeType := mime.TypeByExtension(filepath.Ext(imagePath))
	if mimeType == "" || !strings.HasPrefix(mimeType, "image/") {
		return "", "", fmt.Errorf("unsupported image type. Only image files are allowed")
	}
	extension := filepath.Ext(imagePath)[1:] // Remove the leading dot
	return mimeType, extension, nil
}

func PadBase64(encodedString string) string {
	return encodedString + strings.Repeat("=", (4-len(encodedString)%4)%4)
}

func SendPostRequest(url string, headers map[string]string, data []byte) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == http.StatusInternalServerError {
		return nil, errors.New(string(body))
	}

	return body, nil
}

func ToJSON(data map[string]string) string {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return ""
	}
	return string(jsonData)
}

// Function to convert camelCase to snake_case
func ToSnakeCase(s string) string {
	var buf bytes.Buffer
	for i, r := range s {
		if unicode.IsUpper(r) {
			if i > 0 {
				buf.WriteRune('_')
			}
			buf.WriteRune(unicode.ToLower(r))
		} else {
			buf.WriteRune(r)
		}
	}
	return buf.String()
}

func GenerateToken() (string, error) {
	// Generate a 16-byte random token
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	// Convert to a hexadecimal string
	return hex.EncodeToString(bytes), nil
}

// SanitizeOptionalString sanitizes a string pointer if it is non-nil.
func SanitizeOptionalString(value *string, p *bluemonday.Policy) *string {
	if value != nil {
		sanitized := p.Sanitize(*value)
		return &sanitized
	}
	return nil
}

// MarshalCachedData serializes the data pool and pagination info into a single JSON string.
func MarshalCachedData(dataPool interface{}, paginationInfo interface{}) (string, error) {
	combinedData := map[string]interface{}{
		"dataPool":       dataPool,
		"paginationInfo": paginationInfo,
	}

	// Convert the combined data into a JSON string
	jsonData, err := json.Marshal(combinedData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cache data: %w", err)
	}

	return string(jsonData), nil
}

func UnmarshalCachedData(cachedData string, dataPool interface{}, paginationInfo interface{}) error {
	// Deserialize the JSON string into a map containing the dataPool and paginationInfo
	combinedData := make(map[string]json.RawMessage)

	err := json.Unmarshal([]byte(cachedData), &combinedData)
	if err != nil {
		return fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	// Unmarshal the dataPool
	if err := json.Unmarshal(combinedData["dataPool"], dataPool); err != nil {
		return fmt.Errorf("failed to unmarshal dataPool: %w", err)
	}

	// Unmarshal the paginationInfo
	if err := json.Unmarshal(combinedData["paginationInfo"], paginationInfo); err != nil {
		return fmt.Errorf("failed to unmarshal paginationInfo: %w", err)
	}

	return nil
}

// Helper function if not already defined
func JoinWithComma(strs []string) string {
	if len(strs) == 0 {
		return ""
	}
	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += ", " + strs[i]
	}
	return result
}
