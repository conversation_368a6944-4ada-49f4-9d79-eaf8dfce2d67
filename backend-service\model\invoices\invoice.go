package model

import (
	"time"
)

// InvoiceStatus represents the visibility status of the comsheet
type InvoiceStatus string

const (
	PENDING        InvoiceStatus = "PENDING"
	PAID           InvoiceStatus = "PAID"
	PARTIALLY_PAID InvoiceStatus = "PARTIALLY_PAID"
	CANCELLED      InvoiceStatus = "CANCELLED"
	OVERDUE        InvoiceStatus = "OVERDUE"
)

// Invoice represents a Invoice in the system
type Invoice struct {
	Id            string        `json:"id"`
	InvoiceNumber string        `json:"invoice_number"`
	OrderId       string        `json:"order_id"`
	Status        InvoiceStatus `json:"status"`
	InvoiceDate   *time.Time    `json:"invoice_date"`
	DueDate       *time.Time    `json:"due_date"`
	CreatedAt     time.Time     `json:"created_at"`
	ModifiedAt    time.Time     `json:"modified_at"`
	ClientEmail   *string       `json:"client_email"` // New field for guest email
}

// PostInvoice is used for creating a new Invoice. Fields can be added as necessary.
type PostInvoice struct {
	Id            string        `json:"id"`
	InvoiceNumber string        `json:"invoice_number" binding:"required"`
	OrderId       string        `json:"order_id" binding:"required"`
	Status        InvoiceStatus `json:"status" binding:"required"`
	InvoiceDate   *time.Time    `json:"invoice_date"`
	DueDate       *time.Time    `json:"due_date"`
	ClientEmail   *string       `json:"client_email"` // New field for guest email
}

type UpdateInvoice struct {
	InvoiceNumber *string        `json:"invoice_number,omitempty"`
	OrderId       *string        `json:"order_id,omitempty"`
	Status        *InvoiceStatus `json:"status,omitempty"`
	InvoiceDate   *time.Time     `json:"invoice_date,omitempty"`
	DueDate       *time.Time     `json:"due_date,omitempty"`
	ClientEmail   *string        `json:"client_email,omitempty"` // New field for guest email
}

type InvoiceUri struct {
	ID string `uri:"id" binding:"required"`
}
