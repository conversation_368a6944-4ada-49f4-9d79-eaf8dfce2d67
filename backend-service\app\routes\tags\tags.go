package tags

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	tags "github.com/terang-ai/backend-service/controller/tags"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	controller := tags.NewTagController(db, redis)

	r.POST("/v1/tags", controller.InsertTag)
	r.GET("/v1/tags", controller.GetAllTags)
	r.GET("/v1/tags/:id", controller.GetOneTag)
	r.PUT("/v1/tags/:id", controller.UpdateTag)
	r.DELETE("/v1/tags/:id", controller.DeleteTag)
}
