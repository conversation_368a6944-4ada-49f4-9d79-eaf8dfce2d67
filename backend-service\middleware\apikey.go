package middleware

import (
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"gopkg.in/yaml.v2"
)

func ReadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func getAPIKey() (string, error) {
	// Check if the environment variable exists
	apiKey := os.Getenv("API_KEY")
	if apiKey != "" {
		return apiKey, nil
	}

	// Fall back to the YAML configuration
	config, err := ReadConfig("configs/config.yaml")
	if err != nil {
		return "", err
	}

	return config.API.Key, nil
}

func APIKeyAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		key := c.<PERSON>er("X-API-Key")

		// Get the expected API key from environment variable or YAML
		expectedKey, err := getAPIKey()
		if err != nil {
			log.Fatal(err)
		}

		if key != expectedKey {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		c.Next()
	}
}
