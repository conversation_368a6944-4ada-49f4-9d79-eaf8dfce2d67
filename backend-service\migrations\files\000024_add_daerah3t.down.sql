-- Drop the triggers first
DROP TRIGGER IF EXISTS free_daerah_3t_modified_at ON free_daerah_3t;
DROP TRIGGER IF EXISTS set_free_daerah_3t_ulid ON free_daerah_3t;

-- Drop function for ULID generation (if it's only used for this table)
DROP FUNCTION IF EXISTS insert_free_daerah_3t_with_ulid();

-- Drop indexes
DROP INDEX IF EXISTS idx_free_daerah_3t_user_email;
DROP INDEX IF EXISTS idx_free_daerah_3t_category_id;

-- Drop the table (this will automatically drop all constraints)
DROP TABLE IF EXISTS free_daerah_3t;