package bundle

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

// PurchasedBundleInfo represents a bundle that the user has purchased
type PurchasedBundleInfo struct {
	Bundle             FlattenedBundleWithCategory `json:"bundle"`
	PurchaseDate       time.Time                   `json:"purchaseDate"`
	OrderID            string                      `json:"orderId"`
	OrderStatus        string                      `json:"orderStatus"`
	PaymentStatus      string                      `json:"paymentStatus"`
	AccessExpiresAt    *time.Time                  `json:"accessExpiresAt,omitempty"`
	RemainingDays      *int                        `json:"remainingDays,omitempty"`
	ExamsTotal         int                         `json:"examsTotal"`
	ExamsCompleted     int                         `json:"examsCompleted"`
	ExamsCompletedRate float64                     `json:"examsCompletedRate"`
	ClientEmail        string                      `json:"client_email,omitempty"`
}

// Helper function to get a pointer to ValidUntil time if valid
func getValidUntilPtr(validUntil sql.NullTime) *time.Time {
	if validUntil.Valid {
		return &validUntil.Time
	}
	return nil
}

// getPurchasedBundles handles retrieving all bundles purchased by a user
func getPurchasedBundles(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Support both userId or email as parameter
		userID := c.Param("userId")
		email := c.Query("email")

		// Check if we have at least one identifier
		if userID == "" && email == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Either User ID or email is required"})
			return
		}

		// Parse query parameters
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		if page < 1 {
			page = 1
		}

		pageSize, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
		if pageSize < 1 || pageSize > 100 {
			pageSize = 10
		}

		offset := (page - 1) * pageSize

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		// Create a channel to receive results
		resultCh := make(chan struct {
			Bundles        []PurchasedBundleInfo
			PaginationInfo PaginationInfo
			Error          error
		}, 1)

		go func() {
			// Simple query to get completed orders
			var query string
			var countQuery string
			var args []interface{}
			var countArgs []interface{}

			if userID != "" {
				// For userId
				countQuery = `
					SELECT COUNT(DISTINCT o.bundle_id)
					FROM orders o
					WHERE o.client_id = $1
					AND o.bundle_id IS NOT NULL
					AND o.status = 'COMPLETED'
				`
				countArgs = []interface{}{userID}

				query = `
					SELECT 
						b.id,
						b.name,
						b.description,
						b.price,
						b.discount_percentage,
						b.thumbnail_url,
						b.banner_url,
						b.visibility,
						b.valid_from,
						b.valid_until,
						b.metadata,
						b.created_at,
						b.modified_at,
						b.user_id,
						o.id AS order_id,
						o.status AS order_status,
						'PAID' AS payment_status,
						o.created_at AS purchase_date,
						o.client_email,
						false AS is_free_access,
						(SELECT COUNT(*) FROM exam_bundle_items bi WHERE bi.bundle_id = b.id) AS exams_total,
						(
							SELECT COUNT(DISTINCT es.exam_id)
							FROM exam_sessions es
							JOIN exam_bundle_items bi ON es.exam_id = bi.exam_id
							WHERE bi.bundle_id = b.id
							AND es.user_id = $1
							AND es.status = 'COMPLETED'
						) AS exams_completed
					FROM orders o
					JOIN exam_bundles b ON o.bundle_id = b.id
					WHERE o.client_id = $1
					AND o.bundle_id IS NOT NULL
					AND o.status = 'COMPLETED'
					ORDER BY o.created_at DESC
					LIMIT $2 OFFSET $3
				`
				args = []interface{}{userID, pageSize, offset}
			} else {
				// For email
				countQuery = `
					SELECT COUNT(DISTINCT o.bundle_id)
					FROM orders o
					WHERE (o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1)
					AND o.bundle_id IS NOT NULL
					AND o.status = 'COMPLETED'
				`
				countArgs = []interface{}{email}

				query = `
					SELECT 
						b.id,
						b.name,
						b.description,
						b.price,
						b.discount_percentage,
						b.thumbnail_url,
						b.banner_url,
						b.visibility,
						b.valid_from,
						b.valid_until,
						b.metadata,
						b.created_at,
						b.modified_at,
						b.user_id,
						o.id AS order_id,
						o.status AS order_status,
						'PAID' AS payment_status,
						o.created_at AS purchase_date,
						o.client_email,
						false AS is_free_access,
						(SELECT COUNT(*) FROM exam_bundle_items bi WHERE bi.bundle_id = b.id) AS exams_total,
						(
							SELECT COUNT(DISTINCT es.exam_id)
							FROM exam_sessions es
							JOIN exam_bundle_items bi ON es.exam_id = bi.exam_id
							WHERE bi.bundle_id = b.id
							AND (es.user_id = (SELECT id FROM users WHERE email = $1) OR es.user_email = $1)
							AND es.status = 'COMPLETED'
						) AS exams_completed
					FROM orders o
					JOIN exam_bundles b ON o.bundle_id = b.id
					WHERE (o.client_id = (SELECT id FROM users WHERE email = $1) OR o.client_email = $1)
					AND o.bundle_id IS NOT NULL
					AND o.status = 'COMPLETED'
					ORDER BY o.created_at DESC
					LIMIT $2 OFFSET $3
				`
				args = []interface{}{email, pageSize, offset}
			}

			// Execute count query
			var totalBundles int
			err := dbx.GetContext(ctx, &totalBundles, countQuery, countArgs...)
			if err != nil {
				resultCh <- struct {
					Bundles        []PurchasedBundleInfo
					PaginationInfo PaginationInfo
					Error          error
				}{
					Error: err,
				}
				return
			}

			// Execute main query
			rows, err := dbx.QueryxContext(ctx, query, args...)
			if err != nil {
				resultCh <- struct {
					Bundles        []PurchasedBundleInfo
					PaginationInfo PaginationInfo
					Error          error
				}{
					Error: err,
				}
				return
			}
			defer rows.Close()

			// Process results
			purchasedBundles := []PurchasedBundleInfo{}
			for rows.Next() {
				var bundle struct {
					ID                 string         `db:"id"`
					Name               string         `db:"name"`
					Description        []byte         `db:"description"`
					Price              float64        `db:"price"`
					DiscountPercentage sql.NullInt32  `db:"discount_percentage"`
					ThumbnailURL       sql.NullString `db:"thumbnail_url"`
					BannerURL          sql.NullString `db:"banner_url"`
					Visibility         string         `db:"visibility"`
					ValidFrom          sql.NullTime   `db:"valid_from"`
					ValidUntil         sql.NullTime   `db:"valid_until"`
					Metadata           string         `db:"metadata"`
					CreatedAt          time.Time      `db:"created_at"`
					ModifiedAt         time.Time      `db:"modified_at"`
					UserID             string         `db:"user_id"`
					OrderID            string         `db:"order_id"`
					OrderStatus        string         `db:"order_status"`
					PaymentStatus      string         `db:"payment_status"`
					PurchaseDate       time.Time      `db:"purchase_date"`
					ClientEmail        sql.NullString `db:"client_email"`
					IsFreeAccess       bool           `db:"is_free_access"`
					ExamsTotal         int            `db:"exams_total"`
					ExamsCompleted     int            `db:"exams_completed"`
				}

				if err := rows.StructScan(&bundle); err != nil {
					resultCh <- struct {
						Bundles        []PurchasedBundleInfo
						PaginationInfo PaginationInfo
						Error          error
					}{
						Error: err,
					}
					return
				}

				// Get category info
				var category CategoryInfo
				var hasCategory bool

				categoryQuery := `
					WITH FirstExam AS (
						SELECT exam_id
						FROM exam_bundle_items
						WHERE bundle_id = $1
						LIMIT 1
					)
					SELECT c.id, c.name
					FROM exam_categories ec
					JOIN categories c ON ec.category_id = c.id
					WHERE ec.exam_id = (SELECT exam_id FROM FirstExam)
					LIMIT 1
				`

				err = dbx.GetContext(ctx, &category, categoryQuery, bundle.ID)
				if err != nil && err != sql.ErrNoRows {
					resultCh <- struct {
						Bundles        []PurchasedBundleInfo
						PaginationInfo PaginationInfo
						Error          error
					}{
						Error: err,
					}
					return
				}
				hasCategory = err != sql.ErrNoRows

				// Calculate completion rate
				var completionRate float64 = 0
				if bundle.ExamsTotal > 0 {
					completionRate = float64(bundle.ExamsCompleted) / float64(bundle.ExamsTotal) * 100
				}

				// Create purchased bundle info
				purchasedBundle := PurchasedBundleInfo{
					Bundle: FlattenedBundleWithCategory{
						ID:                 bundle.ID,
						Name:               bundle.Name,
						Description:        string(bundle.Description),
						Price:              bundle.Price,
						DiscountPercentage: bundle.DiscountPercentage,
						ThumbnailURL:       bundle.ThumbnailURL,
						BannerURL:          bundle.BannerURL,
						Visibility:         bundle.Visibility,
						ValidFrom:          bundle.ValidFrom,
						ValidUntil:         bundle.ValidUntil,
						Metadata:           bundle.Metadata,
						CreatedAt:          bundle.CreatedAt,
						ModifiedAt:         bundle.ModifiedAt,
						UserID:             bundle.UserID,
						IsFreeAccess:       bundle.IsFreeAccess,
					},
					PurchaseDate:       bundle.PurchaseDate,
					OrderID:            bundle.OrderID,
					OrderStatus:        bundle.OrderStatus,
					PaymentStatus:      bundle.PaymentStatus,
					ExamsTotal:         bundle.ExamsTotal,
					ExamsCompleted:     bundle.ExamsCompleted,
					ExamsCompletedRate: completionRate,
					ClientEmail:        bundle.ClientEmail.String,
				}

				// Add category if exists
				if hasCategory {
					purchasedBundle.Bundle.Category = category
				}

				purchasedBundles = append(purchasedBundles, purchasedBundle)
			}

			// Check for row scan errors
			if err = rows.Err(); err != nil {
				resultCh <- struct {
					Bundles        []PurchasedBundleInfo
					PaginationInfo PaginationInfo
					Error          error
				}{
					Error: err,
				}
				return
			}

			// Pagination info
			totalPages := (totalBundles + pageSize - 1) / pageSize
			paginationInfo := PaginationInfo{
				TotalData:   totalBundles,
				TotalPages:  totalPages,
				CurrentPage: page,
				PageSize:    pageSize,
			}

			// Send result
			resultCh <- struct {
				Bundles        []PurchasedBundleInfo
				PaginationInfo PaginationInfo
				Error          error
			}{
				Bundles:        purchasedBundles,
				PaginationInfo: paginationInfo,
				Error:          nil,
			}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			// Format response for client
			flattenedBundles := make([]map[string]interface{}, len(result.Bundles))
			for i, bundle := range result.Bundles {
				// Create flattened bundle structure
				flattenedBundle := map[string]interface{}{
					// Bundle fields
					"id":                 bundle.Bundle.ID,
					"name":               bundle.Bundle.Name,
					"description":        bundle.Bundle.Description,
					"price":              bundle.Bundle.Price,
					"discountPercentage": bundle.Bundle.DiscountPercentage,
					"thumbnailUrl":       bundle.Bundle.ThumbnailURL,
					"bannerUrl":          bundle.Bundle.BannerURL,
					"visibility":         bundle.Bundle.Visibility,
					"validFrom":          bundle.Bundle.ValidFrom,
					"validUntil":         bundle.Bundle.ValidUntil,
					"metadata":           bundle.Bundle.Metadata,
					"createdAt":          bundle.Bundle.CreatedAt,
					"modifiedAt":         bundle.Bundle.ModifiedAt,
					"userId":             bundle.Bundle.UserID,
					"isFreeAccess":       bundle.Bundle.IsFreeAccess,
					// Category information if exists
					"categoryId":   bundle.Bundle.Category.ID,
					"categoryName": bundle.Bundle.Category.Name,
					// Purchase information
					"purchaseDate":       bundle.PurchaseDate,
					"orderId":            bundle.OrderID,
					"orderStatus":        bundle.OrderStatus,
					"paymentStatus":      bundle.PaymentStatus,
					"examsTotal":         bundle.ExamsTotal,
					"examsCompleted":     bundle.ExamsCompleted,
					"examsCompletedRate": bundle.ExamsCompletedRate,
					"clientEmail":        bundle.ClientEmail,
				}

				flattenedBundles[i] = flattenedBundle
			}

			c.JSON(http.StatusOK, gin.H{
				"purchasedBundles": flattenedBundles,
				"pagination":       result.PaginationInfo,
			})
		}
	}
}

// getPurchasedBundleDetails handles retrieving details of a specific purchased bundle
func getPurchasedBundleDetails(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		bundleID := c.Param("bundleId")

		if userID == "" || bundleID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "User ID and Bundle ID are required"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		// Create a channel to receive results
		resultCh := make(chan struct {
			Bundle PurchasedBundleInfo `json:"bundle"`
			Exams  []struct {
				ID          string     `json:"id"`
				ExamID      string     `json:"examId"`
				Name        string     `json:"name"`
				CompletedAt *time.Time `json:"completedAt"`
				Score       *float64   `json:"score"`
				IsCompleted bool       `json:"isCompleted"`
			} `json:"exams"`
			Error error `json:"error"`
		}, 1)

		go func() {
			// First verify that the user has purchased this bundle
			var purchaseExists bool
			verifyQuery := `
				SELECT EXISTS (
					-- Check for regular paid purchases
					SELECT 1
					FROM orders o
					JOIN invoices i ON i.order_id = o.id
					JOIN payments p ON p.invoice_id = i.id
					WHERE o.client_id = $1
					AND o.bundle_id = $2
					AND p.status = 'PAID'
					
					UNION ALL
					
					-- Check for free bundles
					SELECT 1
					FROM orders o
					WHERE o.client_id = $1
					AND o.bundle_id = $2
					AND o.status = 'COMPLETED'
					AND NOT EXISTS (
						SELECT 1 FROM payments p
						JOIN invoices i ON p.invoice_id = i.id
						WHERE i.order_id = o.id AND p.status = 'PAID'
					)
				)
			`

			err := dbx.GetContext(ctx, &purchaseExists, verifyQuery, userID, bundleID)
			if err != nil {
				resultCh <- struct {
					Bundle PurchasedBundleInfo `json:"bundle"`
					Exams  []struct {
						ID          string     `json:"id"`
						ExamID      string     `json:"examId"`
						Name        string     `json:"name"`
						CompletedAt *time.Time `json:"completedAt"`
						Score       *float64   `json:"score"`
						IsCompleted bool       `json:"isCompleted"`
					} `json:"exams"`
					Error error `json:"error"`
				}{
					Error: err,
				}
				return
			}

			if !purchaseExists {
				resultCh <- struct {
					Bundle PurchasedBundleInfo `json:"bundle"`
					Exams  []struct {
						ID          string     `json:"id"`
						ExamID      string     `json:"examId"`
						Name        string     `json:"name"`
						CompletedAt *time.Time `json:"completedAt"`
						Score       *float64   `json:"score"`
						IsCompleted bool       `json:"isCompleted"`
					} `json:"exams"`
					Error error `json:"error"`
				}{
					Error: fmt.Errorf("bundle not purchased by this user"),
				}
				return
			}

			// Get the purchased bundle details
			bundleQuery := `
				WITH AllBundlePurchases AS (
					-- Regular purchased bundles with payment
					SELECT 
						b.id,
						b.name,
						b.description,
						b.price,
						b.discount_percentage,
						b.thumbnail_url,
						b.banner_url,
						b.visibility,
						b.valid_from,
						b.valid_until,
						b.metadata,
						b.created_at,
						b.modified_at,
						b.user_id,
						o.id AS order_id,
						o.status AS order_status,
						p.status AS payment_status,
						COALESCE(p.payment_end_date, o.created_at) AS purchase_date,
						false AS is_free_access
					FROM orders o
					JOIN exam_bundles b ON o.bundle_id = b.id
					JOIN invoices i ON i.order_id = o.id
					JOIN payments p ON p.invoice_id = i.id
					WHERE o.client_id = $1
					AND o.bundle_id = $2
					AND p.status = 'PAID'
					
					UNION ALL
					
					-- Free bundles (with COMPLETED status but no payment or free via daerah 3T)
					SELECT
						b.id,
						b.name,
						b.description,
						b.price,
						b.discount_percentage,
						b.thumbnail_url,
						b.banner_url,
						b.visibility,
						b.valid_from,
						b.valid_until,
						b.metadata,
						b.created_at,
						b.modified_at,
						b.user_id,
						o.id AS order_id,
						o.status AS order_status,
						'FREE_ACCESS' AS payment_status,
						o.created_at AS purchase_date,
						true AS is_free_access
					FROM orders o
					JOIN exam_bundles b ON o.bundle_id = b.id
					WHERE o.client_id = $1
					AND o.bundle_id = $2
					AND o.status = 'COMPLETED'
					AND NOT EXISTS (
						SELECT 1 FROM payments p
						JOIN invoices i ON p.invoice_id = i.id
						WHERE i.order_id = o.id AND p.status = 'PAID'
					)
				),
				PurchaseDetails AS (
					SELECT DISTINCT ON (id)
						id, name, description, price, discount_percentage, thumbnail_url,
						banner_url, visibility, valid_from, valid_until, metadata,
						created_at, modified_at, user_id,
						order_id, order_status, payment_status, purchase_date, is_free_access,
						(
							SELECT COUNT(bi.id)
							FROM exam_bundle_items bi
							WHERE bi.bundle_id = id
						) AS exams_total,
						(
							SELECT COUNT(DISTINCT es.exam_id)
							FROM exam_sessions es
							JOIN exam_bundle_items bi ON es.exam_id = bi.exam_id
							WHERE bi.bundle_id = id
							AND es.user_id = $1
							AND es.status = 'COMPLETED'
						) AS exams_completed
					FROM AllBundlePurchases
					ORDER BY id, purchase_date DESC
				)
				SELECT 
					id, name, description, price, discount_percentage, thumbnail_url,
					banner_url, visibility, valid_from, valid_until, metadata,
					created_at, modified_at, user_id,
					order_id, order_status, payment_status, purchase_date, is_free_access,
					exams_total, exams_completed
				FROM PurchaseDetails
				LIMIT 1
			`

			var bundle struct {
				ID                 string         `db:"id"`
				Name               string         `db:"name"`
				Description        []byte         `db:"description"`
				Price              float64        `db:"price"`
				DiscountPercentage sql.NullInt32  `db:"discount_percentage"`
				ThumbnailURL       sql.NullString `db:"thumbnail_url"`
				BannerURL          sql.NullString `db:"banner_url"`
				Visibility         string         `db:"visibility"`
				ValidFrom          sql.NullTime   `db:"valid_from"`
				ValidUntil         sql.NullTime   `db:"valid_until"`
				Metadata           string         `db:"metadata"`
				CreatedAt          time.Time      `db:"created_at"`
				ModifiedAt         time.Time      `db:"modified_at"`
				UserID             string         `db:"user_id"`
				OrderID            string         `db:"order_id"`
				OrderStatus        string         `db:"order_status"`
				PaymentStatus      string         `db:"payment_status"`
				PurchaseDate       time.Time      `db:"purchase_date"`
				IsFreeAccess       bool           `db:"is_free_access"`
				ExamsTotal         int            `db:"exams_total"`
				ExamsCompleted     int            `db:"exams_completed"`
			}

			err = dbx.GetContext(ctx, &bundle, bundleQuery, userID, bundleID)
			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- struct {
						Bundle PurchasedBundleInfo `json:"bundle"`
						Exams  []struct {
							ID          string     `json:"id"`
							ExamID      string     `json:"examId"`
							Name        string     `json:"name"`
							CompletedAt *time.Time `json:"completedAt"`
							Score       *float64   `json:"score"`
							IsCompleted bool       `json:"isCompleted"`
						} `json:"exams"`
						Error error `json:"error"`
					}{
						Error: fmt.Errorf("bundle not found"),
					}
					return
				}
				resultCh <- struct {
					Bundle PurchasedBundleInfo `json:"bundle"`
					Exams  []struct {
						ID          string     `json:"id"`
						ExamID      string     `json:"examId"`
						Name        string     `json:"name"`
						CompletedAt *time.Time `json:"completedAt"`
						Score       *float64   `json:"score"`
						IsCompleted bool       `json:"isCompleted"`
					} `json:"exams"`
					Error error `json:"error"`
				}{
					Error: err,
				}
				return
			}

			// Get category information
			var category CategoryInfo
			var hasCategory bool

			categoryQuery := `
				WITH FirstExam AS (
					SELECT exam_id
					FROM exam_bundle_items
					WHERE bundle_id = $1
					LIMIT 1
				)
				SELECT c.id, c.name
				FROM exam_categories ec
				JOIN categories c ON ec.category_id = c.id
				WHERE ec.exam_id = (SELECT exam_id FROM FirstExam)
				LIMIT 1
			`

			err = dbx.GetContext(ctx, &category, categoryQuery, bundleID)
			if err != nil && err != sql.ErrNoRows {
				resultCh <- struct {
					Bundle PurchasedBundleInfo `json:"bundle"`
					Exams  []struct {
						ID          string     `json:"id"`
						ExamID      string     `json:"examId"`
						Name        string     `json:"name"`
						CompletedAt *time.Time `json:"completedAt"`
						Score       *float64   `json:"score"`
						IsCompleted bool       `json:"isCompleted"`
					} `json:"exams"`
					Error error `json:"error"`
				}{
					Error: err,
				}
				return
			}
			hasCategory = err != sql.ErrNoRows

			// Calculate remaining days if valid_until is set
			var remainingDays *int
			if bundle.ValidUntil.Valid {
				now := time.Now()
				if now.Before(bundle.ValidUntil.Time) {
					days := int(bundle.ValidUntil.Time.Sub(now).Hours() / 24)
					remainingDays = &days
				} else {
					days := 0
					remainingDays = &days
				}
			}

			// Calculate completion rate
			var completionRate float64 = 0
			if bundle.ExamsTotal > 0 {
				completionRate = float64(bundle.ExamsCompleted) / float64(bundle.ExamsTotal) * 100
			}

			// Get exams in the bundle with completion status
			examsQuery := `
				WITH BundleExams AS (
					SELECT 
						bi.id,
						bi.exam_id,
						ae.name,
						(
							SELECT es.end_time
							FROM exam_sessions es
							WHERE es.exam_id = bi.exam_id
							AND es.user_id = $1
							AND es.status = 'COMPLETED'
							ORDER BY es.end_time DESC
							LIMIT 1
						) AS completed_at,
						(
							SELECT es.score
							FROM exam_scores es
							JOIN exam_sessions s ON es.session_id = s.session_id
							WHERE s.exam_id = bi.exam_id
							AND s.user_id = $1
							AND s.status = 'COMPLETED'
							ORDER BY es.created_at DESC
							LIMIT 1
						) AS score
					FROM exam_bundle_items bi
					JOIN available_exams ae ON bi.exam_id = ae.id
					WHERE bi.bundle_id = $2
				)
				SELECT 
					id,
					exam_id,
					name,
					completed_at,
					score,
					completed_at IS NOT NULL AS is_completed
				FROM BundleExams
				ORDER BY name
			`

			var exams []struct {
				ID          string     `db:"id"`
				ExamID      string     `db:"exam_id"`
				Name        string     `db:"name"`
				CompletedAt *time.Time `db:"completed_at"`
				Score       *float64   `db:"score"`
				IsCompleted bool       `db:"is_completed"`
			}

			err = dbx.SelectContext(ctx, &exams, examsQuery, userID, bundleID)
			if err != nil {
				resultCh <- struct {
					Bundle PurchasedBundleInfo `json:"bundle"`
					Exams  []struct {
						ID          string     `json:"id"`
						ExamID      string     `json:"examId"`
						Name        string     `json:"name"`
						CompletedAt *time.Time `json:"completedAt"`
						Score       *float64   `json:"score"`
						IsCompleted bool       `json:"isCompleted"`
					} `json:"exams"`
					Error error `json:"error"`
				}{
					Error: err,
				}
				return
			}

			// Create the purchased bundle object
			purchasedBundle := PurchasedBundleInfo{
				Bundle: FlattenedBundleWithCategory{
					ID:                 bundle.ID,
					Name:               bundle.Name,
					Description:        string(bundle.Description),
					Price:              bundle.Price,
					DiscountPercentage: bundle.DiscountPercentage,
					ThumbnailURL:       bundle.ThumbnailURL,
					BannerURL:          bundle.BannerURL,
					Visibility:         bundle.Visibility,
					ValidFrom:          bundle.ValidFrom,
					ValidUntil:         bundle.ValidUntil,
					Metadata:           bundle.Metadata,
					CreatedAt:          bundle.CreatedAt,
					ModifiedAt:         bundle.ModifiedAt,
					UserID:             bundle.UserID,
					IsFreeAccess:       bundle.IsFreeAccess, // Include IsFreeAccess flag
				},
				PurchaseDate:       bundle.PurchaseDate,
				OrderID:            bundle.OrderID,
				OrderStatus:        bundle.OrderStatus,
				PaymentStatus:      bundle.PaymentStatus,
				AccessExpiresAt:    getValidUntilPtr(bundle.ValidUntil),
				RemainingDays:      remainingDays,
				ExamsTotal:         bundle.ExamsTotal,
				ExamsCompleted:     bundle.ExamsCompleted,
				ExamsCompletedRate: completionRate,
			}

			// Add category if it exists
			if hasCategory {
				purchasedBundle.Bundle.Category = category
			}

			// Transform exams for JSON response
			examResults := make([]struct {
				ID          string     `json:"id"`
				ExamID      string     `json:"examId"`
				Name        string     `json:"name"`
				CompletedAt *time.Time `json:"completedAt"`
				Score       *float64   `json:"score"`
				IsCompleted bool       `json:"isCompleted"`
			}, len(exams))

			for i, exam := range exams {
				examResults[i] = struct {
					ID          string     `json:"id"`
					ExamID      string     `json:"examId"`
					Name        string     `json:"name"`
					CompletedAt *time.Time `json:"completedAt"`
					Score       *float64   `json:"score"`
					IsCompleted bool       `json:"isCompleted"`
				}{
					ID:          exam.ID,
					ExamID:      exam.ExamID,
					Name:        exam.Name,
					CompletedAt: exam.CompletedAt,
					Score:       exam.Score,
					IsCompleted: exam.IsCompleted,
				}
			}

			// Send the result
			resultCh <- struct {
				Bundle PurchasedBundleInfo `json:"bundle"`
				Exams  []struct {
					ID          string     `json:"id"`
					ExamID      string     `json:"examId"`
					Name        string     `json:"name"`
					CompletedAt *time.Time `json:"completedAt"`
					Score       *float64   `json:"score"`
					IsCompleted bool       `json:"isCompleted"`
				} `json:"exams"`
				Error error `json:"error"`
			}{
				Bundle: purchasedBundle,
				Exams:  examResults,
				Error:  nil,
			}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				if result.Error.Error() == "bundle not found" || result.Error.Error() == "bundle not purchased by this user" {
					c.JSON(http.StatusNotFound, gin.H{"error": result.Error.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"purchasedBundle": result.Bundle,
				"exams":           result.Exams,
			})
		}
	}
}
