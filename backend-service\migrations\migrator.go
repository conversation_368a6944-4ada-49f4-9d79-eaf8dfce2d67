package migrations

import (
	"database/sql"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/terang-ai/backend-service/datastores/db"
)

type Logger interface {
	Debug(format string, args ...interface{})
	Info(format string, args ...interface{})
	Error(format string, args ...interface{})
}

type SchemaChange struct {
	Type        string // "table", "column", "index", "type", etc.
	Name        string // name of the object being changed
	Action      string // "create", "alter", "drop", etc.
	ParentTable string // parent table if applicable (for columns, indices)
	DDL         string // the actual SQL statement
}

type Migrator struct {
	migrate     *migrate.Migrate
	db          *sql.DB
	logger      Logger
	migrateDir  string
	schemaCache map[string][]string // for tracking schema before/after migration
}

func NewMigrator(db *sql.DB, logger Logger) (*Migrator, error) {
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return nil, fmt.Errorf("could not create postgres driver: %v", err)
	}

	migrateDir := "file://migrations/files"
	m, err := migrate.NewWithDatabaseInstance(
		migrateDir,
		"postgres",
		driver,
	)
	if err != nil {
		return nil, fmt.Errorf("could not create migrator: %v", err)
	}

	return &Migrator{
		migrate:     m,
		db:          db,
		logger:      logger,
		migrateDir:  "migrations/files", // Store local path for file reading
		schemaCache: make(map[string][]string),
	}, nil
}

func (m *Migrator) logMigrationStep(step, msg string) {
	m.logger.Info("[%s] %s", step, msg)
}

func (m *Migrator) logMigrationError(step string, err error) {
	m.logger.Error("[%s] %v", step, err)
}

// GetMigrationFiles returns a list of all migration files
func (m *Migrator) GetMigrationFiles() ([]string, error) {
	m.logMigrationStep("Files", "Scanning migration files...")

	var files []string

	// Walk the migrations directory
	err := filepath.Walk(m.migrateDir, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and non-sql files
		if info.IsDir() || !strings.HasSuffix(info.Name(), ".sql") {
			return nil
		}

		files = append(files, info.Name())
		return nil
	})

	if err != nil {
		m.logMigrationError("Files", fmt.Errorf("failed to scan migration files: %v", err))
		return nil, err
	}

	m.logMigrationStep("Files", fmt.Sprintf("Found %d migration files", len(files)))
	return files, nil
}

// ParseMigrationContent examines a migration file and extracts schema changes
func (m *Migrator) ParseMigrationContent(filename string) ([]SchemaChange, error) {
	// Read the file content
	content, err := os.ReadFile(filepath.Join(m.migrateDir, filename))
	if err != nil {
		return nil, fmt.Errorf("failed to read migration file %s: %v", filename, err)
	}

	fileContent := string(content)
	var changes []SchemaChange

	// Extract all statements (separated by semicolons)
	statements := extractStatements(fileContent)

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		// Skip comments and empty lines
		if strings.HasPrefix(stmt, "--") || strings.HasPrefix(stmt, "/*") {
			continue
		}

		change := parseStatement(stmt)
		if change.Type != "" {
			changes = append(changes, change)
		}
	}

	return changes, nil
}

// Extract SQL statements from content (splitting by semicolons)
func extractStatements(content string) []string {
	// First, temporarily replace semicolons in quotes or comments
	var tempContent string
	inQuote := false
	inLineComment := false
	inBlockComment := false
	inDollarQuote := false
	dollarQuoteTag := ""
	bracketLevel := 0

	// Preprocess content to handle DO blocks properly
	for i := 0; i < len(content); i++ {
		char := content[i]

		// Handle dollar-quoted strings (used in PostgreSQL DO blocks)
		if i < len(content)-1 && char == '$' && content[i+1] == '$' && !inQuote && !inLineComment && !inBlockComment {
			if !inDollarQuote {
				inDollarQuote = true
				dollarQuoteTag = "$$"
				tempContent += "$$"
				i++
				continue
			} else if dollarQuoteTag == "$$" {
				inDollarQuote = false
				dollarQuoteTag = ""
				tempContent += "$$"
				i++
				continue
			}
		}

		// Handle more complex dollar quotes with tags, e.g. $tag$
		if char == '$' && !inQuote && !inLineComment && !inBlockComment && !inDollarQuote {
			// Look ahead to see if this is a dollar quote start
			isTagStart := true
			tagEnd := i + 1
			for tagEnd < len(content) && content[tagEnd] != '$' {
				if !isAlphaNumeric(content[tagEnd]) {
					isTagStart = false
					break
				}
				tagEnd++
			}

			if isTagStart && tagEnd < len(content) && content[tagEnd] == '$' {
				tagContent := content[i : tagEnd+1]
				if !inDollarQuote {
					inDollarQuote = true
					dollarQuoteTag = tagContent
					tempContent += tagContent
					i = tagEnd
					continue
				} else if dollarQuoteTag == tagContent {
					inDollarQuote = false
					dollarQuoteTag = ""
					tempContent += tagContent
					i = tagEnd
					continue
				}
			}
		}

		if inDollarQuote {
			tempContent += string(char)
			continue
		}

		if inLineComment {
			if char == '\n' {
				inLineComment = false
			}
			tempContent += string(char)
			continue
		}

		if inBlockComment {
			if i > 0 && content[i-1] == '*' && char == '/' {
				inBlockComment = false
			}
			tempContent += string(char)
			continue
		}

		if char == '\'' && (i == 0 || content[i-1] != '\\') && !inDollarQuote {
			inQuote = !inQuote
		}

		if !inQuote && !inBlockComment && i < len(content)-1 {
			if char == '-' && content[i+1] == '-' {
				inLineComment = true
			} else if char == '/' && content[i+1] == '*' {
				inBlockComment = true
			}
		}

		// Track bracket levels for procedural blocks
		if !inQuote && !inLineComment && !inBlockComment && !inDollarQuote {
			if char == '(' {
				bracketLevel++
			} else if char == ')' {
				bracketLevel--
			}
		}

		// Replace semicolons in quotes or in nested blocks
		if char == ';' && (inQuote || inDollarQuote || bracketLevel > 0) {
			tempContent += "@@SEMICOLON@@"
		} else {
			tempContent += string(char)
		}
	}

	// Special handling for DO blocks
	// First, identify DO blocks and keep them as single statements
	var processedContent string
	doBlockPattern := regexp.MustCompile(`(?i)DO\s+(\$\$[\s\S]*?\$\$);`)
	processedContent = doBlockPattern.ReplaceAllString(tempContent, "DO $1@@STATEMENT_END@@")

	// Split by real semicolons
	parts := strings.Split(processedContent, ";")

	// Process each part to restore semicolons and combine DO blocks
	var statements []string
	var currentStatement string
	inDoBlock := false

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// Restore semicolons in quotes
		part = strings.ReplaceAll(part, "@@SEMICOLON@@", ";")

		// Check if this is a DO block start
		if strings.HasPrefix(strings.ToUpper(part), "DO") && !strings.Contains(part, "@@STATEMENT_END@@") {
			inDoBlock = true
			currentStatement = part + ";"
		} else if inDoBlock && !strings.Contains(part, "@@STATEMENT_END@@") {
			// Continue collecting parts of the DO block
			currentStatement += part + ";"
		} else if inDoBlock {
			// End of DO block
			part = strings.ReplaceAll(part, "@@STATEMENT_END@@", ";")
			currentStatement += part
			statements = append(statements, currentStatement)
			currentStatement = ""
			inDoBlock = false
		} else {
			// Normal statement, not in a DO block
			part = strings.ReplaceAll(part, "@@STATEMENT_END@@", ";")
			statements = append(statements, part)
		}
	}

	// Add any pending statement (should only happen if the file ends in a DO block without a semicolon)
	if currentStatement != "" {
		statements = append(statements, currentStatement)
	}

	return statements
}

// Helper function to check if a character is alphanumeric
func isAlphaNumeric(c byte) bool {
	return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_'
}

// Parse a single SQL statement and extract schema change info
// Parse a single SQL statement and extract schema change info
func parseStatement(stmt string) SchemaChange {
	stmt = strings.TrimSpace(stmt)
	stmtUpper := strings.ToUpper(stmt)

	change := SchemaChange{
		DDL: formatSQLForStorage(stmt),
	}

	// Handle DO blocks that create types
	if strings.HasPrefix(stmtUpper, "DO") {
		// Look for CREATE TYPE inside the DO block
		createTypeRegex := regexp.MustCompile(`(?i)CREATE\s+TYPE\s+([^\s(]+)\s+AS\s+ENUM`)
		matches := createTypeRegex.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			typeName := strings.Trim(matches[1], `"`)
			change.Type = "type"
			change.Name = typeName
			change.Action = "create"
			return change
		}

		// Check for other statements inside the DO block
		if strings.Contains(stmtUpper, "ALTER TABLE") && strings.Contains(stmtUpper, "TYPE") {
			// For ALTER COLUMN TYPE statements embedded in DO blocks
			alterTypeRegex := regexp.MustCompile(`(?i)ALTER\s+TABLE\s+([^\s]+)\s+ALTER\s+COLUMN\s+([^\s]+)\s+TYPE\s+([^\s,]+)`)
			matches := alterTypeRegex.FindStringSubmatch(stmt)
			if len(matches) >= 4 {
				tableName := strings.Trim(matches[1], `"`)
				columnName := strings.Trim(matches[2], `"`)
				// typeName := strings.Trim(matches[3], `"`)

				change.Type = "column_type"
				change.Name = columnName
				change.Action = "alter"
				change.ParentTable = tableName
				return change
			}
		}
	}

	// CREATE TABLE
	if strings.HasPrefix(stmtUpper, "CREATE TABLE") {
		re := regexp.MustCompile(`(?i)CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			tableName := strings.Trim(matches[1], `"`)
			change.Type = "table"
			change.Name = tableName
			change.Action = "create"
		}
		return change
	}

	// Detect triggers
	if strings.HasPrefix(stmtUpper, "CREATE TRIGGER") || strings.HasPrefix(stmtUpper, "CREATE OR REPLACE TRIGGER") {
		re := regexp.MustCompile(`(?i)CREATE\s+(?:OR\s+REPLACE\s+)?TRIGGER\s+([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			triggerName := strings.Trim(matches[1], `"`)
			change.Type = "trigger"
			change.Name = triggerName
			change.Action = "create"

			// Try to extract the table name if possible
			tableRe := regexp.MustCompile(`(?i)ON\s+([^\s(]+)`)
			tableMatches := tableRe.FindStringSubmatch(stmt)
			if len(tableMatches) >= 2 {
				tableName := strings.Trim(tableMatches[1], `"`)
				change.ParentTable = tableName
			}
		}
		return change
	}

	// Detect functions
	if strings.HasPrefix(stmtUpper, "CREATE FUNCTION") || strings.HasPrefix(stmtUpper, "CREATE OR REPLACE FUNCTION") {
		re := regexp.MustCompile(`(?i)CREATE\s+(?:OR\s+REPLACE\s+)?FUNCTION\s+([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			functionName := strings.Trim(matches[1], `"`)
			change.Type = "function"
			change.Name = functionName
			change.Action = "create"
		}
		return change
	}

	// DROP TRIGGER
	if strings.HasPrefix(stmtUpper, "DROP TRIGGER") {
		re := regexp.MustCompile(`(?i)DROP\s+TRIGGER\s+(?:IF\s+EXISTS\s+)?([^\s;]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			triggerName := strings.Trim(matches[1], `"`)
			change.Type = "trigger"
			change.Name = triggerName
			change.Action = "drop"
		}
		return change
	}

	// DROP FUNCTION
	if strings.HasPrefix(stmtUpper, "DROP FUNCTION") {
		re := regexp.MustCompile(`(?i)DROP\s+FUNCTION\s+(?:IF\s+EXISTS\s+)?([^\s(;]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			functionName := strings.Trim(matches[1], `"`)
			change.Type = "function"
			change.Name = functionName
			change.Action = "drop"
		}
		return change
	}

	// In the parseStatement function, add a new case:
	if strings.HasPrefix(stmtUpper, "INSERT INTO") {
		re := regexp.MustCompile(`(?i)INSERT\s+INTO\s+([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			tableName := strings.Trim(matches[1], `"`)
			change.Type = "insert"
			change.Name = tableName // Use table name, not a unique ID
			change.Action = "add"   // "add" for inserts
			change.ParentTable = tableName
		}
		return change
	}

	// DROP TABLE
	if strings.HasPrefix(stmtUpper, "DROP TABLE") {
		re := regexp.MustCompile(`(?i)DROP\s+TABLE\s+(?:IF\s+EXISTS\s+)?([^\s;]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			tableName := strings.Trim(matches[1], `"`)
			change.Type = "table"
			change.Name = tableName
			change.Action = "drop"
		}
		return change
	}

	// ALTER TABLE
	if strings.HasPrefix(stmtUpper, "ALTER TABLE") {
		// Extract table name
		tableRe := regexp.MustCompile(`(?i)ALTER\s+TABLE\s+([^\s]+)`)
		tableMatches := tableRe.FindStringSubmatch(stmt)
		if len(tableMatches) >= 2 {
			tableName := strings.Trim(tableMatches[1], `"`)

			// ADD COLUMN
			if strings.Contains(stmtUpper, "ADD COLUMN") {
				columnRe := regexp.MustCompile(`(?i)ADD\s+COLUMN\s+([^\s]+)`)
				columnMatches := columnRe.FindStringSubmatch(stmt)
				if len(columnMatches) >= 2 {
					columnName := strings.Trim(columnMatches[1], `"`)
					change.Type = "column"
					change.Name = columnName
					change.Action = "add"
					change.ParentTable = tableName
					return change
				}
			}

			// DROP COLUMN
			if strings.Contains(stmtUpper, "DROP COLUMN") {
				columnRe := regexp.MustCompile(`(?i)DROP\s+COLUMN\s+([^\s;]+)`)
				columnMatches := columnRe.FindStringSubmatch(stmt)
				if len(columnMatches) >= 2 {
					columnName := strings.Trim(columnMatches[1], `"`)
					change.Type = "column"
					change.Name = columnName
					change.Action = "drop"
					change.ParentTable = tableName
					return change
				}
			}

			// ALTER COLUMN (NOT NULL)
			notNullRegex := regexp.MustCompile(`(?i)ALTER\s+TABLE\s+([^\s]+)\s+ALTER\s+COLUMN\s+([^\s]+)\s+SET\s+NOT\s+NULL`)
			matches := notNullRegex.FindStringSubmatch(stmt)
			if len(matches) >= 3 {
				columnName := strings.Trim(matches[2], `"`)
				change.Type = "column"
				change.Name = columnName
				change.Action = "alter"
				change.ParentTable = tableName
				return change
			}

			// ALTER COLUMN TYPE
			if strings.Contains(stmtUpper, "TYPE") {
				typeRegex := regexp.MustCompile(`(?i)ALTER\s+COLUMN\s+([^\s]+)\s+TYPE\s+([^\s]+)`)
				typeMatches := typeRegex.FindStringSubmatch(stmt)
				if len(typeMatches) >= 3 {
					columnName := strings.Trim(typeMatches[1], `"`)
					change.Type = "column_type"
					change.Name = columnName
					change.Action = "alter"
					change.ParentTable = tableName
					return change
				}
			}

			// ALTER COLUMN (other)
			alterColumnRegex := regexp.MustCompile(`(?i)ALTER\s+COLUMN\s+([^\s]+)`)
			alterMatches := alterColumnRegex.FindStringSubmatch(stmt)
			if len(alterMatches) >= 2 {
				columnName := strings.Trim(alterMatches[1], `"`)
				change.Type = "column"
				change.Name = columnName
				change.Action = "alter"
				change.ParentTable = tableName
				return change
			}
		}
	}

	// CREATE INDEX
	if strings.HasPrefix(stmtUpper, "CREATE INDEX") || strings.HasPrefix(stmtUpper, "CREATE UNIQUE INDEX") {
		re := regexp.MustCompile(`(?i)CREATE\s+(?:UNIQUE\s+)?INDEX\s+([^\s]+)\s+ON\s+([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 3 {
			indexName := strings.Trim(matches[1], `"`)
			tableName := strings.Trim(matches[2], `"`)

			change.Type = "index"
			change.Name = indexName
			change.Action = "create"
			change.ParentTable = tableName
		}
		return change
	}

	// DROP INDEX
	if strings.HasPrefix(stmtUpper, "DROP INDEX") {
		re := regexp.MustCompile(`(?i)DROP\s+INDEX\s+(?:IF\s+EXISTS\s+)?([^\s;]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			indexName := strings.Trim(matches[1], `"`)
			// Note: In some DBs like PostgreSQL, index names are unique across the schema
			// so the parent table might not be specified in the DROP INDEX statement
			change.Type = "index"
			change.Name = indexName
			change.Action = "drop"
		}
		return change
	}

	// CREATE TYPE - Direct method
	if strings.HasPrefix(stmtUpper, "CREATE TYPE") {
		re := regexp.MustCompile(`(?i)CREATE\s+TYPE\s+([^\s(]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			typeName := strings.Trim(matches[1], `"`)
			change.Type = "type"
			change.Name = typeName
			change.Action = "create"
		}
		return change
	}

	// ALTER TYPE
	if strings.HasPrefix(stmtUpper, "ALTER TYPE") {
		re := regexp.MustCompile(`(?i)ALTER\s+TYPE\s+([^\s]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			typeName := strings.Trim(matches[1], `"`)
			change.Type = "type"
			change.Name = typeName
			change.Action = "alter"
		}
		return change
	}

	// DROP TYPE
	if strings.HasPrefix(stmtUpper, "DROP TYPE") {
		re := regexp.MustCompile(`(?i)DROP\s+TYPE\s+(?:IF\s+EXISTS\s+)?([^\s;]+)`)
		matches := re.FindStringSubmatch(stmt)
		if len(matches) >= 2 {
			typeName := strings.Trim(matches[1], `"`)
			change.Type = "type"
			change.Name = typeName
			change.Action = "drop"
		}
		return change
	}

	// Default return if no patterns are matched
	return change
}

func formatSQLForStorage(sql string) string {
	// Just clean up whitespace but don't truncate
	return regexp.MustCompile(`\s+`).ReplaceAllString(sql, " ")
}

// CaptureSchemaSnapshot takes a snapshot of the current database schema
func (m *Migrator) CaptureSchemaSnapshot(snapshot string) error {
	m.logMigrationStep("Schema", fmt.Sprintf("Taking schema snapshot: %s", snapshot))

	// Query tables
	tables, err := m.GetMigrationTables()
	if err != nil {
		return err
	}

	var allObjects []string
	allObjects = append(allObjects, fmt.Sprintf("Tables (%d):", len(tables)))
	allObjects = append(allObjects, tables...)

	// Query columns for each table
	allObjects = append(allObjects, "\nColumns by table:")
	for _, table := range tables {
		columns, err := m.getTableColumns(table)
		if err != nil {
			m.logMigrationError("Schema", fmt.Errorf("failed to get columns for table %s: %v", table, err))
			continue
		}

		allObjects = append(allObjects, fmt.Sprintf("  %s: %s", table, strings.Join(columns, ", ")))
	}

	// Query indices
	indices, err := m.getIndices()
	if err == nil && len(indices) > 0 {
		allObjects = append(allObjects, "\nIndices:")
		allObjects = append(allObjects, indices...)
	}

	// Store the snapshot
	m.schemaCache[snapshot] = allObjects
	return nil
}

// getTableColumns retrieves all columns for a specific table
func (m *Migrator) getTableColumns(tableName string) ([]string, error) {
	query := `
		SELECT column_name, data_type 
		FROM information_schema.columns 
		WHERE table_schema = 'public' 
		AND table_name = $1
		ORDER BY ordinal_position;
	`

	rows, err := m.db.Query(query, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []string
	for rows.Next() {
		var columnName, dataType string
		if err := rows.Scan(&columnName, &dataType); err != nil {
			return nil, err
		}
		columns = append(columns, fmt.Sprintf("%s (%s)", columnName, dataType))
	}

	return columns, nil
}

// getIndices retrieves all indices in the database
func (m *Migrator) getIndices() ([]string, error) {
	// Query to get index definitions with proper CREATE INDEX statements
	query := `
        SELECT 
            pg_get_indexdef(i.indexrelid) AS index_definition
        FROM 
            pg_index i
        JOIN 
            pg_class c ON i.indexrelid = c.oid
        JOIN 
            pg_class t ON i.indrelid = t.oid
        JOIN 
            pg_namespace n ON t.relnamespace = n.oid
        WHERE 
            n.nspname = 'public' AND
            t.relkind = 'r' AND
            c.relname NOT LIKE 'pg_%' AND
            NOT i.indisprimary
        ORDER BY
            c.relname;
    `

	rows, err := m.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var indices []string
	for rows.Next() {
		var indexDef string
		if err := rows.Scan(&indexDef); err != nil {
			return nil, err
		}
		indices = append(indices, indexDef)
	}

	return indices, nil
}

// GetSchemaDiff returns the differences between before and after snapshots
func (m *Migrator) GetSchemaDiff() []string {
	before, hasBefore := m.schemaCache["before"]
	after, hasAfter := m.schemaCache["after"]

	if !hasBefore || !hasAfter {
		return []string{"Schema comparison unavailable"}
	}

	// For simplicity, just show both schemas
	diff := []string{
		"Schema Changes Summary:",
		"--------------------",
	}

	// Compare tables count
	beforeTableCount := 0
	afterTableCount := 0

	// Extract table counts
	for _, line := range before {
		if strings.HasPrefix(line, "Tables (") {
			fmt.Sscanf(line, "Tables (%d):", &beforeTableCount)
			break
		}
	}

	for _, line := range after {
		if strings.HasPrefix(line, "Tables (") {
			fmt.Sscanf(line, "Tables (%d):", &afterTableCount)
			break
		}
	}

	tableDiff := afterTableCount - beforeTableCount
	if tableDiff > 0 {
		diff = append(diff, fmt.Sprintf("✅ Added %d new tables", tableDiff))
	} else if tableDiff < 0 {
		diff = append(diff, fmt.Sprintf("❌ Removed %d tables", -tableDiff))
	} else {
		diff = append(diff, "No tables added or removed")
	}

	// Add detailed section for finding specific differences (implementation simplified)
	diff = append(diff, "\nDetailed Changes:")
	diff = append(diff, "--------------------")

	// Find new tables
	beforeTables := extractElementsAfter(before, "Tables")
	afterTables := extractElementsAfter(after, "Tables")

	for _, table := range afterTables {
		if !contains(beforeTables, table) && !strings.HasPrefix(table, "Tables") {
			diff = append(diff, fmt.Sprintf("➕ New table: %s", table))
		}
	}

	// Look for column changes (simplified approach)
	columnsBefore := filterLines(before, "  ")
	columnsAfter := filterLines(after, "  ")

	for _, col := range columnsAfter {
		if !contains(columnsBefore, col) {
			parts := strings.Split(col, ":")
			if len(parts) >= 2 {
				tableName := strings.TrimSpace(parts[0])
				diff = append(diff, fmt.Sprintf("➕ Modified table: %s", tableName))
			}
		}
	}

	return diff
}

// Helper function to extract elements after a section header
func extractElementsAfter(lines []string, header string) []string {
	var result []string
	inSection := false

	for _, line := range lines {
		if strings.HasPrefix(line, header) {
			inSection = true
			result = append(result, line)
			continue
		}

		if inSection {
			if line == "" || strings.HasPrefix(line, "\n") {
				inSection = false
				continue
			}
			result = append(result, line)
		}
	}

	return result
}

// Helper function to check if a slice contains a string
func contains(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// Helper function to filter lines with a specific prefix
func filterLines(lines []string, prefix string) []string {
	var result []string
	for _, line := range lines {
		if strings.HasPrefix(line, prefix) {
			result = append(result, line)
		}
	}
	return result
}

func (m *Migrator) ResetMigrations() error {
	start := time.Now()
	m.logMigrationStep("Reset", "Starting migration reset process...")

	// Log current tables before dropping
	tables, err := m.GetMigrationTables()
	if err != nil {
		m.logMigrationError("Reset", fmt.Errorf("failed to get current tables: %v", err))
	} else {
		m.logMigrationStep("Reset", fmt.Sprintf("Current tables to be dropped: %v", tables))
	}

	// Drop all tables
	m.logMigrationStep("Reset", "Dropping all tables...")
	_, err = m.db.Exec(`
		DO $$ 
		DECLARE 
			r RECORD;
		BEGIN
			SET session_replication_role = 'replica';
			FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
				EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
			END LOOP;
			SET session_replication_role = 'origin';
		END $$;
	`)
	if err != nil {
		m.logMigrationError("Reset", fmt.Errorf("failed to drop tables: %v", err))
		return err
	}

	// Drop custom types
	m.logMigrationStep("Reset", "Dropping custom types...")
	_, err = m.db.Exec(`
		DO $$ 
		DECLARE 
			t RECORD;
		BEGIN
			FOR t IN (
				SELECT typname 
				FROM pg_type 
				JOIN pg_namespace ON pg_type.typnamespace = pg_namespace.oid 
				WHERE pg_namespace.nspname = 'public' 
				AND pg_type.typtype = 'e'
			) LOOP
				EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(t.typname) || ' CASCADE';
			END LOOP;
		END $$;
	`)
	if err != nil {
		m.logMigrationError("Reset", fmt.Errorf("failed to drop custom types: %v", err))
		return err
	}

	// Create new migrator instance
	m.logMigrationStep("Reset", "Reinitializing migrator...")
	driver, err := postgres.WithInstance(m.db, &postgres.Config{})
	if err != nil {
		m.logMigrationError("Reset", fmt.Errorf("failed to create new postgres driver: %v", err))
		return err
	}

	newMigrate, err := migrate.NewWithDatabaseInstance(
		"file://migrations/files",
		"postgres",
		driver,
	)
	if err != nil {
		m.logMigrationError("Reset", fmt.Errorf("failed to create new migrator: %v", err))
		return err
	}

	m.migrate = newMigrate
	duration := time.Since(start)
	m.logMigrationStep("Reset", fmt.Sprintf("Migration reset completed successfully in %v", duration))
	return nil
}

// Improved Up method for Migrator
func (m *Migrator) Up() error {
	start := time.Now()
	m.logMigrationStep("Up", "Starting migration process...")

	// Capture schema before migration (only if debug mode)
	m.captureSchemaSnapshot("before")

	// Get current version before migration
	currentVersion, dirty, err := m.migrate.Version()
	if err != nil && !errors.Is(err, migrate.ErrNilVersion) {
		m.logMigrationError("Up", fmt.Errorf("failed to get current version: %v", err))
		return err
	}

	if dirty {
		m.logMigrationStep("Up", fmt.Sprintf("Found dirty migration at version %d, attempting recovery...", currentVersion))
		if err := m.RecoverDirtyState(); err != nil {
			m.logMigrationError("Up", fmt.Errorf("failed to recover from dirty state: %v", err))
			return err
		}
	}

	// Run migrations
	err = m.migrate.Up()

	// Get new version after migration attempt
	newVersion, _, versionErr := m.migrate.Version()
	if versionErr != nil && !errors.Is(versionErr, migrate.ErrNilVersion) {
		m.logMigrationError("Up", fmt.Errorf("failed to get new version: %v", versionErr))
	}

	// Only analyze migration files if actual changes were made
	if !errors.Is(err, migrate.ErrNoChange) && err == nil {
		// If actual changes were made, log the migration details
		m.logMigrationStep("Up", fmt.Sprintf("✨ Schema changes detected, migrating from version %d to %d", currentVersion, newVersion))

		// Get migration files and analyze, but only for versions between current and new
		migrationFiles, filesErr := m.GetMigrationFiles()
		if filesErr == nil {
			relevantMigrations := filterMigrationsByVersion(migrationFiles, currentVersion, newVersion)

			if len(relevantMigrations) > 0 {
				m.logMigrationStep("Up", fmt.Sprintf("Processing %d migration files", len(relevantMigrations)))

				// Only analyze and log relevant migrations
				for _, file := range relevantMigrations {
					if isUpMigration(file) {
						m.analyzeAndLogMigration(file)
					}
				}
			}
		}

		// Capture schema after changes
		m.captureSchemaSnapshot("after")

		// Generate and log actual schema differences
		schemaDiff := m.GetSchemaDiff()
		if len(schemaDiff) > 0 {
			m.logMigrationStep("Changes", "Summary of schema changes:")
			for _, change := range schemaDiff {
				if strings.HasPrefix(change, "➕") || strings.HasPrefix(change, "❌") {
					// Only log actual changes, not summary headers
					m.logMigrationStep("Changes", change)
				}
			}
		}
	} else if errors.Is(err, migrate.ErrNoChange) {
		m.logMigrationStep("Up", fmt.Sprintf("✓ No schema changes needed - database already at version %d", currentVersion))
		err = nil // Convert ErrNoChange to nil as it's not a real error
	}

	if err != nil {
		m.logMigrationError("Up", fmt.Errorf("failed to run migrations: %v", err))
		return err
	}

	// Log created tables (only if there were changes)
	if currentVersion != newVersion {
		tables, err := m.GetMigrationTables()
		if err == nil {
			m.logMigrationStep("Up", fmt.Sprintf("Database now has %d tables", len(tables)))
		}
	}

	duration := time.Since(start)
	m.logMigrationStep("Up", fmt.Sprintf("Migration process completed in %v", duration))

	return nil
}

// Filter migrations by version range
func filterMigrationsByVersion(files []string, fromVersion, toVersion uint) []string {
	var relevant []string

	for _, file := range files {
		version := extractVersionFromFilename(file)
		if version > fromVersion && version <= toVersion {
			relevant = append(relevant, file)
		}
	}

	return relevant
}

// Extract version number from migration filename
func extractVersionFromFilename(filename string) uint {
	parts := strings.Split(filename, "_")
	if len(parts) > 0 {
		versionStr := parts[0]
		version, err := strconv.ParseUint(versionStr, 10, 32)
		if err == nil {
			return uint(version)
		}
	}
	return 0
}

// Check if a migration file is an "up" migration
func isUpMigration(filename string) bool {
	return strings.HasSuffix(filename, ".up.sql")
}

// Analyze and log a single migration file
func (m *Migrator) analyzeAndLogMigration(filename string) {
	// First, log the migration file clearly
	m.logMigrationStep("Migration", fmt.Sprintf("Processing migration file: %s", filename))

	// Read the file content
	content, err := os.ReadFile(filepath.Join(m.migrateDir, filename))
	if err != nil {
		m.logMigrationError("Parse", fmt.Errorf("Error reading migration file %s: %v", filename, err))
		return
	}

	fileContent := string(content)
	m.logMigrationStep("Migration", fmt.Sprintf("File: %s", filename))

	// Extract and log each SQL statement separately
	statements := extractStatements(fileContent)
	if len(statements) == 0 {
		m.logMigrationStep("SQL", "No SQL statements found in the file")
		return
	}

	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") || strings.HasPrefix(stmt, "/*") {
			continue // Skip comments and empty lines
		}

		// Log the raw SQL statement with a specific prefix for notification capture
		sqlLogMsg := fmt.Sprintf("Statement %d: %s", i+1, stmt)
		m.logMigrationStep("SQL", sqlLogMsg)

		// Always log DDL statements with special prefix to ensure they're captured
		stmtUpper := strings.ToUpper(stmt)
		if strings.Contains(stmtUpper, "CREATE TABLE") ||
			strings.Contains(stmtUpper, "ALTER TABLE") ||
			strings.Contains(stmtUpper, "DROP TABLE") ||
			strings.Contains(stmtUpper, "CREATE INDEX") ||
			strings.Contains(stmtUpper, "DROP INDEX") ||
			strings.Contains(stmtUpper, "CREATE TYPE") ||
			strings.Contains(stmtUpper, "DROP TYPE") {
			// Log with a clear DDL prefix
			m.logMigrationStep("DDL", stmt)
		}

		// Analyze what the statement is changing and use clearer wording
		change := parseStatement(stmt)
		if change.Type != "" {
			if change.Type == "table" && change.Action == "create" {
				m.logMigrationStep("Schema", fmt.Sprintf("Creating table '%s'", change.Name))
			} else if change.Type == "table" && change.Action == "drop" {
				m.logMigrationStep("Schema", fmt.Sprintf("Dropping table '%s'", change.Name))
			} else if change.Type == "column" && change.Action == "add" {
				m.logMigrationStep("Schema", fmt.Sprintf("Adding column '%s' to table '%s'",
					change.Name, change.ParentTable))
			} else if change.Type == "column" && change.Action == "alter" {
				m.logMigrationStep("Schema", fmt.Sprintf("Altering column '%s' on table '%s'",
					change.Name, change.ParentTable))
			} else if change.Type == "column" && change.Action == "drop" {
				m.logMigrationStep("Schema", fmt.Sprintf("Dropping column '%s' from table '%s'",
					change.Name, change.ParentTable))
			} else if change.Type == "index" && change.Action == "create" {
				m.logMigrationStep("Schema", fmt.Sprintf("Creating index '%s' on table '%s'",
					change.Name, change.ParentTable))
			} else if change.Type == "index" && change.Action == "drop" {
				if change.ParentTable != "" {
					m.logMigrationStep("Schema", fmt.Sprintf("Dropping index '%s' from table '%s'",
						change.Name, change.ParentTable))
				} else {
					m.logMigrationStep("Schema", fmt.Sprintf("Dropping index '%s'", change.Name))
				}
			} else if change.Type == "type" && change.Action == "create" {
				m.logMigrationStep("Schema", fmt.Sprintf("Creating type '%s'", change.Name))
			} else if change.Type == "type" && change.Action == "alter" {
				m.logMigrationStep("Schema", fmt.Sprintf("Altering type '%s'", change.Name))
			} else if change.Type == "type" && change.Action == "drop" {
				m.logMigrationStep("Schema", fmt.Sprintf("Dropping type '%s'", change.Name))
			} else {
				m.logMigrationStep("Schema", fmt.Sprintf("%s %s '%s'",
					capitalize(change.Action), change.Type, change.Name))
			}
		}
	}
}

// Capitalize first letter
func capitalize(s string) string {
	if s == "" {
		return ""
	}
	return strings.ToUpper(s[:1]) + s[1:]
}

// Capture schema snapshot with reduced logging
func (m *Migrator) captureSchemaSnapshot(snapshot string) {
	// Skip detailed logging for the snapshot process
	err := m.CaptureSchemaSnapshot(snapshot)
	if err != nil {
		m.logger.Debug("Failed to capture schema snapshot: %v", err)
	}
}

// GetMigrationTables gets all tables that should exist based on migrations
func (m *Migrator) GetMigrationTables() ([]string, error) {
	m.logMigrationStep("GetTables", "Retrieving current database tables...")

	query := `
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_type = 'BASE TABLE'
		AND table_name != 'schema_migrations'
		ORDER BY table_name;
	`
	rows, err := m.db.Query(query)
	if err != nil {
		m.logMigrationError("GetTables", fmt.Errorf("failed to query tables: %v", err))
		return nil, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			m.logMigrationError("GetTables", fmt.Errorf("failed to scan table name: %v", err))
			return nil, err
		}
		tables = append(tables, tableName)
	}

	m.logMigrationStep("GetTables", fmt.Sprintf("Found %d tables: %v", len(tables), tables))
	return tables, nil
}

// CheckTables verifies if all tables exist
func (m *Migrator) CheckTables() (bool, []string) {
	m.logMigrationStep("CheckTables", "Verifying table existence...")

	expectedTables, err := m.GetMigrationTables()
	if err != nil {
		m.logMigrationError("CheckTables", fmt.Errorf("failed to get expected tables: %v", err))
		return false, nil
	}

	var missingTables []string
	for _, table := range expectedTables {
		var exists bool
		query := `
			SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_schema = 'public' 
				AND table_name = $1
			);`
		err := m.db.QueryRow(query, table).Scan(&exists)
		if err != nil || !exists {
			missingTables = append(missingTables, table)
		}
	}

	if len(missingTables) > 0 {
		m.logMigrationStep("CheckTables", fmt.Sprintf("Found missing tables: %v", missingTables))
	} else {
		m.logMigrationStep("CheckTables", "All expected tables exist")
	}

	return len(missingTables) == 0, missingTables
}

func (m *Migrator) RecoverDirtyState() error {
	m.logMigrationStep("Recover", "Starting dirty state recovery...")

	version, dirty, err := m.migrate.Version()
	if err != nil && !errors.Is(err, migrate.ErrNilVersion) {
		m.logMigrationError("Recover", fmt.Errorf("failed to get migration version: %v", err))
		return err
	}

	if dirty {
		m.logMigrationStep("Recover", fmt.Sprintf("Attempting to recover from dirty state at version %d", version))

		if err := m.migrate.Force(int(version)); err != nil {
			m.logMigrationError("Recover", fmt.Errorf("failed to force version %d: %v", version, err))
			return err
		}

		// Verify the fix
		newVersion, newDirty, err := m.migrate.Version()
		if err != nil {
			m.logMigrationError("Recover", fmt.Errorf("failed to verify version after recovery: %v", err))
			return err
		}

		if newDirty {
			m.logMigrationError("Recover", errors.New("database is still in dirty state after recovery attempt"))
			return fmt.Errorf("database is still in dirty state after recovery attempt")
		}

		m.logMigrationStep("Recover", fmt.Sprintf("Successfully recovered. Current version: %d", newVersion))
	} else {
		m.logMigrationStep("Recover", "Database is not in dirty state, no recovery needed")
	}

	return nil
}

func (m *Migrator) Down() error {
	start := time.Now()
	m.logMigrationStep("Down", "Starting migration rollback...")

	currentVersion, _, err := m.migrate.Version()
	if err != nil && !errors.Is(err, migrate.ErrNilVersion) {
		m.logMigrationError("Down", fmt.Errorf("failed to get current version: %v", err))
	} else {
		m.logMigrationStep("Down", fmt.Sprintf("Current version before rollback: %d", currentVersion))
	}

	if err := m.migrate.Down(); err != nil {
		m.logMigrationError("Down", fmt.Errorf("failed to roll back migrations: %v", err))
		return err
	}

	duration := time.Since(start)
	m.logMigrationStep("Down", fmt.Sprintf("Successfully rolled back all migrations in %v", duration))
	return nil
}

func (m *Migrator) Version() (uint, bool, error) {
	version, dirty, err := m.migrate.Version()
	if err != nil && !errors.Is(err, migrate.ErrNilVersion) {
		m.logMigrationError("Version", fmt.Errorf("failed to get version: %v", err))
	} else {
		m.logMigrationStep("Version", fmt.Sprintf("Current version: %d, Dirty: %v", version, dirty))
	}
	return version, dirty, err
}

func (m *Migrator) Force(version int) error {
	start := time.Now()
	m.logMigrationStep("Force", fmt.Sprintf("Forcing migration version to %d", version))

	if err := m.migrate.Force(version); err != nil {
		m.logMigrationError("Force", fmt.Errorf("failed to force version %d: %v", version, err))
		return err
	}

	duration := time.Since(start)
	m.logMigrationStep("Force", fmt.Sprintf("Successfully forced version to %d in %v", version, duration))
	return nil
}

func (m *Migrator) Close() error {
	m.logMigrationStep("Close", "Closing migrator connections...")

	sourceErr, dbErr := m.migrate.Close()
	if sourceErr != nil {
		m.logMigrationError("Close", fmt.Errorf("error closing source: %v", sourceErr))
		return fmt.Errorf("error closing source: %v", sourceErr)
	}
	if dbErr != nil {
		m.logMigrationError("Close", fmt.Errorf("error closing db: %v", dbErr))
		return fmt.Errorf("error closing db: %v", dbErr)
	}

	m.logMigrationStep("Close", "Successfully closed all connections")
	return nil
}

// GenerateLatestSchema uses pg_dump to export the complete schema
// This ensures all schema elements are captured automatically
func (m *Migrator) GenerateLatestSchema(outputPath string) error {
	m.logMigrationStep("Schema", "Generating consolidated schema file using pg_dump...")

	// Get database connection parameters from the existing DB config package
	config, err := db.GetDBConfig()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get database configuration: %v", err))
		return m.GenerateLatestSchemaAlt(outputPath)
	}

	// Create a temporary file to store the output
	tmpFile, err := os.CreateTemp("", "schema_dump_*.sql")
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to create temporary file: %v", err))
		return err
	}
	defer os.Remove(tmpFile.Name()) // Clean up when done
	tmpFile.Close()                 // Close it as pg_dump will write to it

	// Construct the pg_dump command with appropriate options
	// --schema-only: Dump only the schema, not data
	// --no-owner: Don't output commands to set ownership of objects
	// --no-privileges: Don't include commands to set privileges
	// -c: Include commands to clean (drop) database objects before creating them
	cmd := exec.Command("pg_dump",
		"--schema-only",
		"--no-owner",
		"--no-privileges",
		"-c",
		"--if-exists",
		"-f", tmpFile.Name(),
		"-h", config.DB.Host,
		"-p", config.DB.Port,
		"-U", config.DB.Username,
		config.DB.DBName,
	)

	// Set PGPASSWORD environment variable for the command
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.DB.Password))

	// Run pg_dump
	output, err := cmd.CombinedOutput()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("pg_dump failed: %v\nOutput: %s", err, output))

		// Fall back to alternative method if pg_dump fails
		m.logMigrationStep("Schema", "pg_dump failed, falling back to alternative schema generation method...")
		return m.GenerateLatestSchemaAlt(outputPath)
	}

	// Read the dumped schema
	dumpedSchema, err := os.ReadFile(tmpFile.Name())
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to read dumped schema: %v", err))
		return err
	}

	// Process the schema to improve formatting and organization
	processedSchema := processSchema(string(dumpedSchema))

	// Write to the final output file
	err = os.WriteFile(outputPath, []byte(processedSchema), 0644)
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to write processed schema to output file: %v", err))
		return err
	}

	if err := validateSchema(outputPath); err != nil {
		m.logMigrationError("Schema", fmt.Errorf("schema validation failed: %v", err))
		return err
	}

	m.logMigrationStep("Schema", fmt.Sprintf("Successfully generated schema file: %s", outputPath))
	return nil
}

// New validation function
func validateSchema(schemaPath string) error {
	content, err := os.ReadFile(schemaPath)
	if err != nil {
		return err
	}

	schemaStr := string(content)

	// Check for duplicate constraints
	for _, table := range strings.Split(schemaStr, "CREATE TABLE") {
		if !strings.Contains(table, "CONSTRAINT") {
			continue
		}

		// Extract constraint names
		constraints := make(map[string]int)
		matches := regexp.MustCompile(`CONSTRAINT\s+(\w+)\s`).FindAllStringSubmatch(table, -1)

		for _, match := range matches {
			if len(match) > 1 {
				name := match[1]
				constraints[name]++
				if constraints[name] > 1 {
					return fmt.Errorf("duplicate constraint found: %s", name)
				}
			}
		}
	}

	return nil
}

// processSchema improves formatting and organization of the pg_dump output
// Improved processSchema function to handle duplicate constraints
func processSchema(schema string) string {
	// Split the schema into separate statements
	statements := strings.Split(schema, ";\n")

	// Categorize and organize statements
	var (
		drops      []string
		extensions []string
		types      []string
		doBlocks   []string
		tables     []string
		indices    []string
		triggers   []string
		functions  []string
		others     []string
	)

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		stmtUpper := strings.ToUpper(stmt)

		// Categorize the statement
		switch {
		case strings.HasPrefix(stmtUpper, "DROP "):
			drops = append(drops, stmt)
		case strings.HasPrefix(stmtUpper, "CREATE EXTENSION"):
			extensions = append(extensions, stmt)
		case strings.HasPrefix(stmtUpper, "CREATE TYPE"):
			types = append(types, stmt)
		case strings.HasPrefix(stmtUpper, "DO "):
			doBlocks = append(doBlocks, stmt)
		case strings.HasPrefix(stmtUpper, "CREATE TABLE"):
			// Process table definition to remove duplicate constraints
			processedTable := processDuplicateConstraints(stmt)
			tables = append(tables, processedTable)
		case strings.HasPrefix(stmtUpper, "CREATE INDEX") || strings.HasPrefix(stmtUpper, "CREATE UNIQUE INDEX"):
			indices = append(indices, stmt)
		case strings.HasPrefix(stmtUpper, "CREATE TRIGGER"):
			triggers = append(triggers, stmt)
		case strings.HasPrefix(stmtUpper, "CREATE FUNCTION") || strings.HasPrefix(stmtUpper, "CREATE OR REPLACE FUNCTION"):
			functions = append(functions, stmt)
		default:
			others = append(others, stmt)
		}
	}

	// Create the final schema with better organization
	var sections []string

	// Build header
	headerText := fmt.Sprintf("-- Consolidated Schema from Database\n"+
		"-- Generated on %s\n"+
		"-- This file represents the complete database schema\n\n",
		time.Now().Format("2006-01-02 15:04:05"))
	sections = append(sections, headerText)

	// Add extensions
	if len(extensions) > 0 {
		sections = append(sections, "-- Extensions")
		sections = append(sections, strings.Join(extensions, ";\n")+";\n")
	}

	// Add types and DO blocks (for ENUMs)
	if len(types) > 0 || len(doBlocks) > 0 {
		sections = append(sections, "\n-- Custom Types")

		// First do blocks that create types
		if len(doBlocks) > 0 {
			sections = append(sections, strings.Join(doBlocks, ";\n")+";\n")
		}

		// Then direct type definitions
		if len(types) > 0 {
			sections = append(sections, strings.Join(types, ";\n")+";\n")
		}
	}

	// Add tables
	if len(tables) > 0 {
		sections = append(sections, "\n-- Tables")
		sections = append(sections, strings.Join(tables, ";\n")+";\n")
	}

	// Add indices
	if len(indices) > 0 {
		sections = append(sections, "\n-- Indices")
		sections = append(sections, strings.Join(indices, ";\n")+";\n")
	}

	// Add functions
	if len(functions) > 0 {
		sections = append(sections, "\n-- Functions")
		sections = append(sections, strings.Join(functions, ";\n")+";\n")
	}

	// Add triggers
	if len(triggers) > 0 {
		sections = append(sections, "\n-- Triggers")
		sections = append(sections, strings.Join(triggers, ";\n")+";\n")
	}

	// Add other statements
	if len(others) > 0 {
		sections = append(sections, "\n-- Other Statements")
		sections = append(sections, strings.Join(others, ";\n")+";\n")
	}

	return strings.Join(sections, "\n")
}

// New function to process and deduplicate constraints in table definitions
func processDuplicateConstraints(tableStmt string) string {
	// If the table statement doesn't contain multiple constraints, return it as is
	if !strings.Contains(tableStmt, "CONSTRAINT") ||
		strings.Count(tableStmt, "CONSTRAINT") <= 1 {
		return tableStmt
	}

	// Find the opening and closing parentheses of the table definition
	openParenIndex := strings.Index(tableStmt, "(")
	closeParenIndex := strings.LastIndex(tableStmt, ")")

	if openParenIndex == -1 || closeParenIndex == -1 {
		return tableStmt // Malformed statement, return as is
	}

	// Extract the table header and footer
	tableHeader := tableStmt[:openParenIndex+1]
	tableFooter := tableStmt[closeParenIndex:]

	// Extract the column and constraint definitions
	columnsPart := tableStmt[openParenIndex+1 : closeParenIndex]

	// Split by comma, but be careful with commas in functions or nested parentheses
	parts := splitByTopLevelComma(columnsPart)

	// Track seen constraints by name only
	seenConstraints := make(map[string]bool)
	var uniqueParts []string

	for _, part := range parts {
		part = strings.TrimSpace(part)

		// Check if this is a constraint definition
		if strings.HasPrefix(strings.ToUpper(part), "CONSTRAINT") {
			// Extract constraint name
			constraintParts := strings.SplitN(part, " ", 3)
			if len(constraintParts) >= 2 {
				constraintName := constraintParts[1]

				// Skip if we've seen this constraint name before
				if seenConstraints[constraintName] {
					continue
				}

				seenConstraints[constraintName] = true
			}
		}

		uniqueParts = append(uniqueParts, part)
	}

	// Reconstruct the table definition with unique constraints
	return tableHeader + strings.Join(uniqueParts, ",\n    ") + tableFooter
}

// Helper function to split a string by commas at the top level only
// This handles nested structures like function calls with commas
func splitByTopLevelComma(s string) []string {
	var result []string
	var current string
	level := 0
	inQuote := false

	for i := 0; i < len(s); i++ {
		char := s[i]

		// Handle quotes
		if char == '\'' && (i == 0 || s[i-1] != '\\') {
			inQuote = !inQuote
		}

		// Skip processing if in quotes
		if inQuote {
			current += string(char)
			continue
		}

		// Track parenthesis nesting level
		if char == '(' {
			level++
		} else if char == ')' {
			level--
		}

		// Split on comma only at the top level
		if char == ',' && level == 0 {
			result = append(result, strings.TrimSpace(current))
			current = ""
		} else {
			current += string(char)
		}
	}

	// Add the last part if not empty
	if strings.TrimSpace(current) != "" {
		result = append(result, strings.TrimSpace(current))
	}

	return result
}

// Alternative implementation that doesn't rely on pg_dump being available
func (m *Migrator) GenerateLatestSchemaAlt(outputPath string) error {
	m.logMigrationStep("Schema", "Generating consolidated schema file from database metadata...")

	// Open the output file
	file, err := os.Create(outputPath)
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to create output file: %v", err))
		return err
	}
	defer file.Close()

	// Write header
	headerText := fmt.Sprintf("-- Consolidated Schema from Database Metadata\n"+
		"-- Generated on %s\n"+
		"-- This file represents the complete database schema\n\n",
		time.Now().Format("2006-01-02 15:04:05"))
	if _, err := file.WriteString(headerText); err != nil {
		return err
	}

	// 1. Get enum types
	types, err := m.getEnumTypes()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get enum types: %v", err))
	} else if len(types) > 0 {
		if _, err := file.WriteString("-- Custom Types (ENUMs)\n"); err != nil {
			return err
		}
		for _, typeSQL := range types {
			if _, err := file.WriteString(typeSQL + ";\n\n"); err != nil {
				return err
			}
		}
	}

	// 2. Get tables and their columns
	tables, err := m.GetMigrationTables()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get tables: %v", err))
	} else {
		if _, err := file.WriteString("-- Tables\n"); err != nil {
			return err
		}

		for _, tableName := range tables {
			tableSQL, err := m.getTableDefinition(tableName)
			if err != nil {
				m.logMigrationError("Schema", fmt.Errorf("failed to get definition for table %s: %v", tableName, err))
				continue
			}

			if _, err := file.WriteString(tableSQL + ";\n\n"); err != nil {
				return err
			}
		}
	}

	// 3. Get indices
	indices, err := m.getIndices()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get indices: %v", err))
	} else if len(indices) > 0 {
		if _, err := file.WriteString("-- Indices\n"); err != nil {
			return err
		}
		for _, indexSQL := range indices {
			if _, err := file.WriteString(indexSQL + ";\n"); err != nil {
				return err
			}
		}
		if _, err := file.WriteString("\n"); err != nil {
			return err
		}
	}

	// 4. Get functions
	functions, err := m.getFunctions()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get functions: %v", err))
	} else if len(functions) > 0 {
		if _, err := file.WriteString("-- Functions\n"); err != nil {
			return err
		}
		for _, funcSQL := range functions {
			if _, err := file.WriteString(funcSQL + ";\n\n"); err != nil {
				return err
			}
		}
	}

	// 5. Get triggers
	triggers, err := m.getTriggers()
	if err != nil {
		m.logMigrationError("Schema", fmt.Errorf("failed to get triggers: %v", err))
	} else if len(triggers) > 0 {
		if _, err := file.WriteString("-- Triggers\n"); err != nil {
			return err
		}
		for _, triggerSQL := range triggers {
			if _, err := file.WriteString(triggerSQL + ";\n\n"); err != nil {
				return err
			}
		}
	}

	m.logMigrationStep("Schema", fmt.Sprintf("Successfully generated schema file: %s", outputPath))
	return nil
}

// Helper methods for database metadata extraction

// Get enum types from the database - completely database-driven, no hardcoded types
func (m *Migrator) getEnumTypes() ([]string, error) {
	query := `
		SELECT 
			n.nspname AS schema_name,
			t.typname AS type_name,
			array_to_string(array_agg(e.enumlabel ORDER BY e.enumsortorder), ', ') AS enum_values
		FROM pg_type t
		JOIN pg_namespace n ON t.typnamespace = n.oid
		JOIN pg_enum e ON t.oid = e.enumtypid
		WHERE n.nspname = 'public'
		GROUP BY schema_name, type_name
		ORDER BY schema_name, type_name;
	`

	rows, err := m.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var types []string
	for rows.Next() {
		var schemaName, typeName, enumValues string
		if err := rows.Scan(&schemaName, &typeName, &enumValues); err != nil {
			return nil, err
		}

		// Split the comma-separated enum values
		values := strings.Split(enumValues, ", ")
		formattedValues := make([]string, len(values))
		for i, v := range values {
			formattedValues[i] = fmt.Sprintf("    '%s'", v)
		}

		// Create DO block for the type
		typeSQL := fmt.Sprintf(`DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = '%s') THEN
        CREATE TYPE %s AS ENUM (
%s
        );
    END IF;
END $$`, typeName, typeName, strings.Join(formattedValues, ",\n"))

		types = append(types, typeSQL)
	}

	return types, nil
}

func (m *Migrator) getTableDefinition(tableName string) (string, error) {
	// Get columns with proper type names for custom types
	columnsQuery := `
        SELECT 
			c.column_name, 
			CASE 
				WHEN c.data_type = 'USER-DEFINED' THEN 
					CASE 
						WHEN t.typtype = 'e' THEN t.typname  -- It's an enum
						ELSE c.udt_name                      -- Other user-defined type
					END
				WHEN c.data_type = 'ARRAY' THEN 
					CASE 
						WHEN et.typname IS NOT NULL THEN et.typname || '[]'  -- Array of enum
						ELSE c.udt_name                                      -- Other array
					END
				ELSE c.data_type
			END AS data_type,
			CASE 
				WHEN c.udt_name = 'varchar' THEN '(' || c.character_maximum_length || ')'
				WHEN c.data_type = 'numeric' THEN '(' || c.numeric_precision || ',' || c.numeric_scale || ')'
				ELSE ''
			END AS type_length,
			c.is_nullable, 
			c.column_default
		FROM information_schema.columns c
		LEFT JOIN pg_type t ON t.typname = c.udt_name
		LEFT JOIN pg_type et ON et.typname = SUBSTRING(c.udt_name FROM 2) AND c.data_type = 'ARRAY'
		WHERE c.table_schema = 'public' AND c.table_name = $1
		ORDER BY c.ordinal_position;
    `

	rows, err := m.db.Query(columnsQuery, tableName)
	if err != nil {
		return "", err
	}
	defer rows.Close()

	// Build column definitions
	var columnDefs []string
	for rows.Next() {
		var colName, dataType, typeLength, isNullable, colDefault sql.NullString
		if err := rows.Scan(&colName, &dataType, &typeLength, &isNullable, &colDefault); err != nil {
			return "", err
		}

		colDef := fmt.Sprintf("    %s %s%s", colName.String, dataType.String, typeLength.String)

		if isNullable.String == "NO" {
			colDef += " NOT NULL"
		}

		if colDefault.Valid {
			colDef += fmt.Sprintf(" DEFAULT %s", colDefault.String)
		}

		columnDefs = append(columnDefs, colDef)
	}

	// Get primary key
	pkQuery := `
		SELECT a.attname
		FROM pg_index i
		JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
		WHERE i.indrelid = $1::regclass AND i.indisprimary;
	`
	pkRows, err := m.db.Query(pkQuery, tableName)
	if err != nil {
		return "", err
	}
	defer pkRows.Close()

	var pkColumns []string
	for pkRows.Next() {
		var colName string
		if err := pkRows.Scan(&colName); err != nil {
			return "", err
		}
		pkColumns = append(pkColumns, colName)
	}

	if len(pkColumns) > 0 {
		columnDefs = append(columnDefs, fmt.Sprintf("    PRIMARY KEY (%s)", strings.Join(pkColumns, ", ")))
	}

	// Get foreign keys
	fkQuery := `
		SELECT
			tc.constraint_name,
			kcu.column_name,
			ccu.table_name AS foreign_table_name,
			ccu.column_name AS foreign_column_name
		FROM
			information_schema.table_constraints AS tc
			JOIN information_schema.key_column_usage AS kcu
			  ON tc.constraint_name = kcu.constraint_name
			  AND tc.table_schema = kcu.table_schema
			JOIN information_schema.constraint_column_usage AS ccu
			  ON ccu.constraint_name = tc.constraint_name
			  AND ccu.table_schema = tc.table_schema
		WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = $1;
	`
	fkRows, err := m.db.Query(fkQuery, tableName)
	if err != nil {
		return "", err
	}
	defer fkRows.Close()

	for fkRows.Next() {
		var constraintName, columnName, foreignTableName, foreignColumnName string
		if err := fkRows.Scan(&constraintName, &columnName, &foreignTableName, &foreignColumnName); err != nil {
			return "", err
		}

		columnDefs = append(columnDefs, fmt.Sprintf("    CONSTRAINT %s FOREIGN KEY (%s) REFERENCES %s(%s)",
			constraintName, columnName, foreignTableName, foreignColumnName))
	}

	// Assemble final CREATE TABLE statement
	tableSQL := fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s (\n%s\n)", tableName, strings.Join(columnDefs, ",\n"))

	// Process table definition to remove duplicate constraints
	processedTableSQL := processDuplicateConstraints(tableSQL)

	return processedTableSQL, nil
}

func (m *Migrator) getFunctions() ([]string, error) {
	query := `
		SELECT 
			routines.routine_name,
			pg_get_functiondef(pg_proc.oid) AS function_definition
		FROM 
			information_schema.routines
		JOIN 
			pg_proc ON routines.routine_name = pg_proc.proname
		JOIN 
			pg_namespace ON pg_proc.pronamespace = pg_namespace.oid
		WHERE 
			routines.routine_schema = 'public' AND
			pg_namespace.nspname = 'public';
	`

	rows, err := m.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var functions []string
	for rows.Next() {
		var routineName, functionDef string
		if err := rows.Scan(&routineName, &functionDef); err != nil {
			return nil, err
		}

		functions = append(functions, functionDef)
	}

	return functions, nil
}

func (m *Migrator) getTriggers() ([]string, error) {
	query := `
		SELECT 
			pg_get_triggerdef(t.oid) AS trigger_definition
		FROM 
			pg_trigger t
		JOIN 
			pg_class c ON t.tgrelid = c.oid
		JOIN 
			pg_namespace n ON c.relnamespace = n.oid
		WHERE 
			n.nspname = 'public' AND
			NOT t.tgisinternal;
	`

	rows, err := m.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var triggers []string
	for rows.Next() {
		var triggerDef string
		if err := rows.Scan(&triggerDef); err != nil {
			return nil, err
		}

		triggers = append(triggers, triggerDef)
	}

	return triggers, nil
}
