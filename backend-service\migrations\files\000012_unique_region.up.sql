-- Modify provinsis table
ALTER TABLE provinsis 
    ALTER COLUMN id SET NOT NULL,
    ADD PRIMARY KEY (id),
    ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY;

-- Modify kab_kotas table
ALTER TABLE kab_kotas 
    ALTER COLUMN id SET NOT NULL,
    ADD PRIMARY KEY (id),
    ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY,
    ALTER COLUMN provinsi_id SET NOT NULL,
    ADD CONSTRAINT fk_kab_kota_provinsi 
        FOREIGN KEY (provinsi_id) 
        REFERENCES provinsis(id);

-- Modify kecamatans table
ALTER TABLE kecamatans 
    ALTER COLUMN id SET NOT NULL,
    ADD PRIMARY KEY (id),
    ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY,
    ALTER COLUMN kab_kota_id SET NOT NULL,
    ADD CONSTRAINT fk_kecamatan_kab_kota 
        FOREIGN KEY (kab_kota_id) 
        REFERENCES kab_kotas(id);

-- Modify kel<PERSON>han_desas table
ALTER TABLE kelurahan_desas 
    ALTER COLUMN id SET NOT NULL,
    ADD PRIMARY KEY (id),
    ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY,
    ALTER COLUMN kecamatan_id SET NOT NULL,
    ADD CONSTRAINT fk_kelurahan_desa_kecamatan 
        FOREIGN KEY (kecamatan_id) 
        REFERENCES kecamatans(id);