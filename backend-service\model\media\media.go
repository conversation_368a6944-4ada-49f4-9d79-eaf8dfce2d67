package model

import (
	"time"
)

// Media represents an image associated with a comsheet in the system
type Media struct {
	Id              string     `json:"id" db:"id"`
	Url             string     `json:"url" db:"url"`
	AvailableExamId string     `json:"available_exam_id" db:"available_exam_id"`
	SizeInBytes     *int       `json:"size_in_bytes" db:"size_in_bytes"`
	MimeType        *string    `json:"mime_type" db:"mime_type"`
	Width           *int       `json:"width" db:"width"`
	Height          *int       `json:"height" db:"height"`
	UploadedAt      *time.Time `json:"uploaded_at" db:"uploaded_at"`
	ModifiedAt      *time.Time `json:"modified_at" db:"modified_at"`
}

// PostMedia is used for creating a new comsheet image
type PostMedia struct {
	Url             string  `json:"url" binding:"required"`
	AvailableExamId string  `json:"available_exam_id" binding:"required"`
	SizeInBytes     *int    `json:"size_in_bytes"`
	MimeType        *string `json:"mime_type"`
	Width           *int    `json:"width"`
	Height          *int    `json:"height"`
}

// UpdateMedia is used for updating an existing comsheet image
type UpdateMedia struct {
	Url             *string    `json:"url,omitempty"`
	AvailableExamId *string    `json:"available_exam_id,omitempty"`
	SizeInBytes     *int       `json:"size_in_bytes,omitempty"`
	MimeType        *string    `json:"mime_type,omitempty"`
	Width           *int       `json:"width,omitempty"`
	Height          *int       `json:"height,omitempty"`
	ModifiedAt      *time.Time `json:"modified_at,omitempty"`
}

// MediaUri is used for binding URI parameters for comsheet image endpoints
type MediaUri struct {
	ID string `uri:"id" binding:"required"`
}
