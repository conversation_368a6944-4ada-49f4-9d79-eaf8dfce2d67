// store/slices/availableExamsSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AvailableExamsType } from '@/components/types';

// Define interfaces for the state
interface AvailableExamsState {
  items: AvailableExamsType[] | null;
  filteredItems: AvailableExamsType[] | null;
  loading: boolean;
  error: string | null;
  examType: string;
}

const initialState: AvailableExamsState = {
  items: null,
  filteredItems: null,
  loading: false,
  error: null,
  examType: 'EXAM',
};

// Create async thunk for fetching exams
export const fetchAvailableExams = createAsyncThunk(
  'availableExams/fetchAvailableExams',
  async (type: string = 'EXAM', { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/available-exams?examType=${type}`);
      
      if (response.status === 401) {
        return rejectWithValue('You need to be logged in to view available exams');
      }
      
      if (response.status === 400) {
        return rejectWithValue('User is not registered');
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to fetch exams');
      }

      const data = await response.json();
      return { data, type };
    } catch (error: any) {
      return rejectWithValue(error.message || 'An unknown error occurred');
    }
  }
);

// We'll remove this separate action creator since we'll get it from the slice's actions

// Create the slice
const availableExamsSlice = createSlice({
  name: 'availableExams',
  initialState,
  reducers: {
    clearExams: (state) => {
      state.items = null;
      state.filteredItems = null;
      state.error = null;
    },
    setExamType: (state, action: PayloadAction<string>) => {
      state.examType = action.payload;
    },
    filterExams: (state, action: PayloadAction<string>) => {
      const searchTerm = action.payload.toLowerCase();
      if (!state.items) return;
      
      if (searchTerm === '') {
        state.filteredItems = state.items;
      } else {
        state.filteredItems = state.items.filter(exam => 
          exam.name.toLowerCase().includes(searchTerm) || 
          (exam.description && exam.description.toLowerCase().includes(searchTerm))
        );
      }
    },
    // Add reducer for initializing with server data
    initializeWithServerData: (state, action: PayloadAction<AvailableExamsType[]>) => {
      state.items = action.payload;
      state.filteredItems = action.payload;
      state.loading = false;
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchAvailableExams pending state
      .addCase(fetchAvailableExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      // Handle fetchAvailableExams fulfilled state
      .addCase(fetchAvailableExams.fulfilled, (state, action: PayloadAction<{data: AvailableExamsType[], type: string}>) => {
        state.items = action.payload.data;
        state.filteredItems = action.payload.data;
        state.examType = action.payload.type;
        state.loading = false;
        state.error = null;
      })
      // Handle fetchAvailableExams rejected state
      .addCase(fetchAvailableExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch exams';
      });
  },
});

export const { clearExams, setExamType, filterExams, initializeWithServerData } = availableExamsSlice.actions;

export default availableExamsSlice.reducer;