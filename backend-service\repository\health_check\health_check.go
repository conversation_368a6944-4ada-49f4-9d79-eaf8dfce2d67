package repository

import (
	"context"
	"database/sql"
	"errors"
	"log"

	"github.com/redis/go-redis/v9"
)

type HealthCheckRepository struct {
	Db    *sql.DB
	Redis *redis.Client
}

func NewHealthCheckRepository(db *sql.DB, redisClient *redis.Client) HealthCheckRepositoryInterface {
	return &HealthCheckRepository{Db: db, Redis: redisClient}
}

// DeleteUser implements UserRepositoryInterface
func (m *HealthCheckRepository) DBHealthCheck() (bool, error) {
	_, err := m.Db.Exec("SELECT 1;")
	if err != nil {
		log.Println(err)
		return false, errors.New("database somehow error or not running")
	}
	return true, nil
}

// REDISHealthCheck checks the health of the Redis instance.
func (m *HealthCheckRepository) REDISHealthCheck() (bool, error) {
	status := m.Redis.Ping(context.Background())
	if status.Err() != nil {
		log.Println(status.Err())
		return false, errors.New("redis is somehow error or not running")
	}
	return true, nil
}
