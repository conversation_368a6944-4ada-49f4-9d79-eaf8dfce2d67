package repository

import (
	"context"

	model "github.com/terang-ai/backend-service/model/available_exams"
)

type AvailableExamsRepositoryInterface interface {
	InsertAvailableExam(string, model.PostAvailableExam) (*model.AvailableExam, error)
	UpdateAvailableExam(string, model.UpdateAvailableExam) (*model.AvailableExam, error)
	GetOneAvailableExam(string) (model.AvailableExam, error)
	GetOneAvailableExamDaerah3T(string, string) (model.AvailableExamV2, bool, error)
	DeleteAvailableExam(string) (bool, error)
	GetAllAvailableExams(int, int) ([]model.AvailableExam, AvailableExamPaginationInfo, error)
	GetAllAvailableExamsWithCache(context.Context, int, int) ([]model.AvailableExam, AvailableExamPaginationInfo, bool, error)
	GetAllAvailableExamsV2(string, int, int, model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error)
	GetAvailableExamBundlesV2(string, int, int, model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error)
	GetPublicAvailableExamBundles(int, int, model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error)
	GetAllPurchasedExamsByEmail(string, int, int, ...model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error)
	GetAllPurchasedExamsIncludingBundles(string, int, int, ...model.ExamType) ([]model.AvailableExamV2, AvailableExamPaginationInfo, error)
	GetAllExamsTaken(string, int, int, ...model.ExamType) ([]model.ExamSessionData, AvailableExamPaginationInfo, error)

	InsertExamQuestionAnswerHint(model.PostExamQuestionAnswerHint) (interface{}, error)
	GetExamQuestionAnswerHints(string, string) ([]model.ExamQuestionAnswerHint, error)
	GetExamQuestionAnswerHintsBySessionId(string) ([]model.ExamQuestionAnswerHint, error)
	GetExamQuestionAnswerHintsBySessionIdBySubject(string, string) ([]model.ExamQuestionAnswerHint, error)
}
