package controller

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/orders"
	"github.com/terang-ai/backend-service/repository"
)

type OrderController struct {
	Db     *sql.DB
	Redis  *redis.Client
	Repo   repository.Repository // Use the Repository interface
	Entity string                // Entity name (e.g., "wip", "user")
}

func NewOrderController(db *sql.DB, redis *redis.Client) *OrderController {
	return &OrderController{
		Db:     db,
		Redis:  redis,
		Repo:   repository.NewBaseRepository(db, redis, "orders", "order"), // Initialize with specific table and entity name
		Entity: "order",
	}
}

func (c *OrderController) DeleteOrder(ctx *gin.Context) {
	var uri model.OrderUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Order
	deleted, err := c.Repo.Delete(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Order failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Order not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Order successfully"})
}

func (c *OrderController) GetAllOrders(ctx *gin.Context) {
	var entity model.Order
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAll(page, pageSize, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "orders retrieved successfully", "_pagination": paginationInfo})
}

func (c *OrderController) GetOneOrder(ctx *gin.Context) {
	var uri model.OrderUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Create a new instance of model.Order
	entity := &model.Order{}

	// Call repository method to retrieve one Order by ID
	result, err := c.Repo.GetOne(uri.ID, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Order not found"})
		return
	}

	// Type assertion to *model.Order
	if wipEntity, ok := result.(*model.Order); ok {
		// Update entity with fetched data
		*entity = *wipEntity

		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "get Order successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Order failed", "msg": "internal error"})
}

func (c *OrderController) InsertOrder(ctx *gin.Context) {
	var post model.PostOrder // Replace with your specific post type
	var entity model.Order
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.Insert(post, entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Order failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Order successfully"})
}

func (c *OrderController) UpdateOrder(ctx *gin.Context) {
	var updates model.UpdateOrder
	var uri model.OrderUri
	var entity model.Order

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind request body (JSON) to updates struct
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Order by ID
	updated, err := c.Repo.Update(uri.ID, updates, &entity)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": "failed to update Order", "error": err.Error()})
		return
	}

	// Type assertion to *model.Order
	if updatedOrder, ok := updated.(*model.Order); ok {
		// Update entity with fetched data after update
		entity = *updatedOrder
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entity, "msg": "Order updated successfully"})
		return
	}

	ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": "type assertion to *model.Order failed", "msg": "internal error"})
}
