// components/sections/OurStorySection.tsx
import React from 'react';
import { <PERSON>rk<PERSON>, ArrowRight } from 'lucide-react';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

interface OurStorySectionProps {
  router: AppRouterInstance;
}

const OurStorySection: React.FC<OurStorySectionProps> = ({ router }) => {
  const storyItems = [
    {
      icon: "https://cdn.terang.ai/landingpage-assets/lightbulb.svg",
      title: "Masalah yang Kami Temui",
      description: "Banyak mahasiswa Indonesia yang bermimpi meraih beasiswa LPDP dan beasiswa internasional, namun gagal di tahap wawancara karena kurangnya persiapan dan feedback yang berkualitas.",
      bgColor: "from-yellow-100 to-orange-100"
    },
    {
      icon: "https://cdn.terang.ai/landingpage-assets/bullseye.svg",
      title: "Solusi yang Kami Cip<PERSON>kan",
      description: "Dengan AI Professor <PERSON>, kami mengh<PERSON>n mentor wawan<PERSON><PERSON> 24/7 yang memberikan simulasi realistis dan feedback mendalam untuk mempersiapkan setiap kandidat dengan optimal.",
      bgColor: "from-green-100 to-emerald-100"
    },
    {
      icon: "https://cdn.terang.ai/landingpage-assets/star.svg",
      title: "Misi Kami",
      description: "\"Democratize Education with AI\" - Membuat persiapan wawancara berkualitas tinggi dapat diakses oleh semua mahasiswa Indonesia, tanpa batasan geografis atau finansial.",
      bgColor: "from-blue-100 to-indigo-100"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-200/30 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-200/30 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-indigo-200/20 rounded-full filter blur-2xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-800 text-sm font-medium mb-6 border border-blue-200">
            <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="Sparkles" className="w-4 h-4 mr-2" />
            Cerita Kami
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-8">
            Mengapa Kami Membuat
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> Terang AI?</span>
          </h2>
        </div>

        <div className="grid md:grid-cols-1 gap-12 items-center">
          <div className="space-y-8">
            {storyItems.map((item, index) => (
              <div 
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 hover:bg-white transition-all shadow-lg hover:shadow-xl"
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 bg-gradient-to-r ${item.bgColor} rounded-xl flex items-center justify-center flex-shrink-0`}>
                    <img src={item.icon} alt={item.title} className="w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-700 leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="text-center mt-16">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <button 
              onClick={() => router.push('/available-interviews')}
              className="group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-5 rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-2xl flex items-center"
            >
              <Sparkles className="w-6 h-6 mr-3 group-hover:animate-spin" />
              Mulai Perjalananmu
              <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurStorySection;