package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/microcosm-cc/bluemonday"
	"github.com/redis/go-redis/v9"
	lib "github.com/terang-ai/backend-service/lib"
	model "github.com/terang-ai/backend-service/model/users"
)

type BaseRepository struct {
	Db         *sql.DB
	Redis      *redis.Client
	TableName  string // Dynamically set table name
	EntityName string // Name of the entity (e.g., "wip", "user")
}

type PaginationInfo struct {
	TotalData   int `json:"total_data"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
}

func NewBaseRepository(db *sql.DB, redis *redis.Client, tableName, entityName string) Repository {
	return &BaseRepository{
		Db:         db,
		Redis:      redis,
		TableName:  tableName,
		EntityName: entityName,
	}
}

func (m *BaseRepository) Delete(id string) (bool, error) {
	// Prepare statement to check if the entity exists
	existsStmt, err := m.Db.Prepare(fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE id = $1)", m.TableName))
	if err != nil {
		log.Printf("Error preparing %s existence check statement: %s\n", m.EntityName, err)
		return false, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.QueryRow(id).Scan(&exists)
	if err != nil {
		log.Printf("Error checking if %s exists: %s\n", m.EntityName, err)
		return false, err
	}

	if !exists {
		return false, fmt.Errorf("%s with ID %s does not exist", m.EntityName, id)
	}

	// Prepare statement to delete the entity
	deleteStmt, err := m.Db.Prepare(fmt.Sprintf("DELETE FROM %s WHERE id = $1", m.TableName))
	if err != nil {
		log.Printf("Error preparing %s deletion statement: %s\n", m.EntityName, err)
		return false, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.Exec(id)
	if err != nil {
		log.Printf("Error deleting %s: %s\n", m.EntityName, err)
		return false, err
	}

	return true, nil
}

func (m *BaseRepository) GetAll(page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, PaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := fmt.Sprintf("SELECT * FROM %s ORDER BY id LIMIT $1 OFFSET $2", m.TableName)
	rows, err := m.Db.Query(query, pageSize, offset)
	if err != nil {
		log.Printf("Error querying %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		log.Printf("Error retrieving column information for %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	var entities []interface{}
	entityType := reflect.TypeOf(entity).Elem()
	for rows.Next() {
		entity := reflect.New(entityType).Interface()
		columnPointers := make([]interface{}, len(columns))
		entityValue := reflect.ValueOf(entity).Elem()

		for i, column := range columns {
			fieldName := lib.ToCamelCase(column)
			field := entityValue.FieldByName(fieldName)
			if field.IsValid() {
				columnPointers[i] = field.Addr().Interface()
			} else {
				var dummy interface{}
				columnPointers[i] = &dummy
			}
		}

		if err := rows.Scan(columnPointers...); err != nil {
			log.Printf("Error scanning %s row: %s\n", m.EntityName, err)
			continue
		}
		entities = append(entities, entity)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating over %s query results: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalDataQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s", m.TableName)
	var totalData int
	err = m.Db.QueryRow(totalDataQuery).Scan(&totalData)
	if err != nil {
		log.Printf("Error retrieving total %s count: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := PaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return entities, paginationInfo, nil
}

func (m *BaseRepository) GetAllBySomethingById(something string, id string, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, PaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := fmt.Sprintf("SELECT * FROM %s WHERE %s = $1 ORDER BY id LIMIT $2 OFFSET $3", m.TableName, something)
	rows, err := m.Db.Query(query, id, pageSize, offset)
	if err != nil {
		log.Printf("Error querying %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		log.Printf("Error retrieving column information for %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	var entities []interface{}
	entityType := reflect.TypeOf(entity).Elem()
	for rows.Next() {
		entity := reflect.New(entityType).Interface()
		columnPointers := make([]interface{}, len(columns))
		entityValue := reflect.ValueOf(entity).Elem()

		for i, column := range columns {
			fieldName := lib.ToCamelCase(column)
			field := entityValue.FieldByName(fieldName)
			if field.IsValid() {
				columnPointers[i] = field.Addr().Interface()
			} else {
				var dummy interface{}
				columnPointers[i] = &dummy
			}
		}

		if err := rows.Scan(columnPointers...); err != nil {
			log.Printf("Error scanning %s row: %s\n", m.EntityName, err)
			continue
		}
		entities = append(entities, entity)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating over %s query results: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalDataQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s = $1", m.TableName, something)
	var totalData int
	err = m.Db.QueryRow(totalDataQuery, id).Scan(&totalData)
	if err != nil {
		log.Printf("Error retrieving total %s count: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := PaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return entities, paginationInfo, nil
}

func (m *BaseRepository) GetOneBySomethingById(something string, id string, something2 string, id2 string, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, PaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := fmt.Sprintf("SELECT * FROM %s WHERE %s = $1 AND %s = $2 ORDER BY id LIMIT $3 OFFSET $4", m.TableName, something, something2)
	rows, err := m.Db.Query(query, id, id2, pageSize, offset)
	if err != nil {
		log.Printf("Error querying %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		log.Printf("Error retrieving column information for %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	var entities []interface{}
	entityType := reflect.TypeOf(entity).Elem()
	for rows.Next() {
		entity := reflect.New(entityType).Interface()
		columnPointers := make([]interface{}, len(columns))
		entityValue := reflect.ValueOf(entity).Elem()

		for i, column := range columns {
			fieldName := lib.ToCamelCase(column)
			field := entityValue.FieldByName(fieldName)
			if field.IsValid() {
				columnPointers[i] = field.Addr().Interface()
			} else {
				var dummy interface{}
				columnPointers[i] = &dummy
			}
		}

		if err := rows.Scan(columnPointers...); err != nil {
			log.Printf("Error scanning %s row: %s\n", m.EntityName, err)
			continue
		}
		entities = append(entities, entity)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating over %s query results: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalDataQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s = $1 AND %s = $2", m.TableName, something, something2)
	var totalData int
	err = m.Db.QueryRow(totalDataQuery, id, id2).Scan(&totalData)
	if err != nil {
		log.Printf("Error retrieving total %s count: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := PaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return entities, paginationInfo, nil
}

// GetAll fetches paginated records from the specified table and caches the result.
func (m *BaseRepository) GetAllWithCache(ctx context.Context, page int, pageSize int, entity interface{}) ([]interface{}, PaginationInfo, bool, error) {
	cacheDuration := 5 * time.Minute
	if page < 1 || pageSize < 1 {
		return nil, PaginationInfo{}, false, errors.New("invalid pagination parameters")
	}

	if m.Redis == nil {
		log.Println("Cache is not initialized")
		return nil, PaginationInfo{}, false, errors.New("cache is not initialized")
	}

	currentHour := time.Now().Format("2006010215") // Format as YYYYMMDDHH
	cacheKey := fmt.Sprintf("%s_page_%d_size_%d_hour_%s", m.TableName, page, pageSize, currentHour)

	cachedResult, err := m.Redis.Get(ctx, cacheKey).Result()
	if err == nil {
		var cachedData struct {
			Entities       []interface{}
			PaginationInfo PaginationInfo
		}
		if err := json.Unmarshal([]byte(cachedResult), &cachedData); err == nil {
			return cachedData.Entities, cachedData.PaginationInfo, true, nil
		}
	}

	offset := (page - 1) * pageSize
	query := fmt.Sprintf("SELECT * FROM %s ORDER BY id LIMIT $1 OFFSET $2", m.TableName)
	rows, err := m.Db.Query(query, pageSize, offset)
	if err != nil {
		log.Printf("Error querying %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, false, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		log.Printf("Error retrieving column information for %s: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, false, err
	}

	var entities []interface{}
	entityType := reflect.TypeOf(entity).Elem()
	for rows.Next() {
		entity := reflect.New(entityType).Interface()
		columnPointers := make([]interface{}, len(columns))
		entityValue := reflect.ValueOf(entity).Elem()

		for i, column := range columns {
			fieldName := lib.ToCamelCase(column)
			field := entityValue.FieldByName(fieldName)
			if field.IsValid() {
				columnPointers[i] = field.Addr().Interface()
			} else {
				var dummy interface{}
				columnPointers[i] = &dummy
			}
		}

		if err := rows.Scan(columnPointers...); err != nil {
			log.Printf("Error scanning %s row: %s\n", m.EntityName, err)
			continue
		}
		entities = append(entities, entity)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating over %s query results: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, false, err
	}

	totalDataQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s", m.TableName)
	var totalData int
	err = m.Db.QueryRow(totalDataQuery).Scan(&totalData)
	if err != nil {
		log.Printf("Error retrieving total %s count: %s\n", m.EntityName, err)
		return nil, PaginationInfo{}, false, err
	}

	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := PaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	cacheData := struct {
		Entities       []interface{}
		PaginationInfo PaginationInfo
	}{
		Entities:       entities,
		PaginationInfo: paginationInfo,
	}
	cacheDataBytes, err := json.Marshal(cacheData)
	if err == nil {
		m.Redis.Set(ctx, cacheKey, cacheDataBytes, cacheDuration).Err()
	}

	return entities, paginationInfo, false, nil
}

func (m *BaseRepository) GetOne(id string, entity interface{}) (interface{}, error) {
	entityType := reflect.TypeOf(entity).Elem()

	var queryFields []string
	fieldMap := make(map[string]string)
	for i := 0; i < entityType.NumField(); i++ {
		field := entityType.Field(i)
		if jsonField := field.Tag.Get("json"); jsonField != "" && jsonField != "-" {
			queryFields = append(queryFields, jsonField)
			fieldMap[jsonField] = field.Name
		}
	}

	query := fmt.Sprintf("SELECT %s FROM %s WHERE id = $1", strings.Join(queryFields, ", "), m.TableName)
	row := m.Db.QueryRow(query, id)

	// Create a new instance of the entity
	newEntity := reflect.New(entityType).Interface()

	scanArgs := make([]interface{}, len(queryFields))
	for i, field := range queryFields {
		fieldName := fieldMap[field]
		scanArgs[i] = reflect.ValueOf(newEntity).Elem().FieldByName(fieldName).Addr().Interface()
	}

	err := row.Scan(scanArgs...)
	if err != nil {
		log.Printf("Error fetching %s: %s\n", m.EntityName, err)
		return nil, err
	}

	return newEntity, nil
}

func (m *BaseRepository) GetOneBySomething(something string, val string, entity interface{}) (interface{}, error) {
	entityType := reflect.TypeOf(entity).Elem()

	var queryFields []string
	fieldMap := make(map[string]string)
	for i := 0; i < entityType.NumField(); i++ {
		field := entityType.Field(i)
		if jsonField := field.Tag.Get("json"); jsonField != "" && jsonField != "-" {
			queryFields = append(queryFields, jsonField)
			fieldMap[jsonField] = field.Name
		}
	}

	query := fmt.Sprintf("SELECT %s FROM %s WHERE %s = $1 ORDER BY id DESC LIMIT 1", strings.Join(queryFields, ", "), m.TableName, something)
	row := m.Db.QueryRow(query, val)

	// Create a new instance of the entity
	newEntity := reflect.New(entityType).Interface()

	scanArgs := make([]interface{}, len(queryFields))
	for i, field := range queryFields {
		fieldName := fieldMap[field]
		scanArgs[i] = reflect.ValueOf(newEntity).Elem().FieldByName(fieldName).Addr().Interface()
	}

	err := row.Scan(scanArgs...)
	if err != nil {
		log.Printf("Error fetching %s: %s\n", m.EntityName, err)
		return nil, err
	}

	return newEntity, nil
}

func (m *BaseRepository) GetOneBySomethingWithCache(ctx context.Context, something string, val string, entity interface{}) (interface{}, bool, error) {
	cacheDuration := 60 * time.Minute

	if m.Redis == nil {
		log.Println("Cache is not initialized")
		return nil, false, errors.New("cache is not initialized")
	}

	// Create cache key using table name, field name, and value
	currentHour := time.Now().Format("2006010215") // Format as YYYYMMDDHH
	cacheKey := fmt.Sprintf("%s:%s_%s_hour_%s", m.TableName, something, val, currentHour)

	// Set up channels for concurrent operations
	type cacheResult struct {
		entity interface{}
		found  bool
		err    error
	}
	cacheChannel := make(chan cacheResult, 1)
	dbChannel := make(chan cacheResult, 1)

	// Concurrently check cache
	go func() {
		// Try to get from cache first
		cachedResult, err := m.Redis.Get(ctx, cacheKey).Result()
		if err == nil {
			// Create a new instance of the same type as the input entity
			entityType := reflect.TypeOf(entity).Elem()
			cachedEntity := reflect.New(entityType).Interface()

			if err := json.Unmarshal([]byte(cachedResult), cachedEntity); err == nil {
				cacheChannel <- cacheResult{cachedEntity, true, nil}
				return
			}
		}
		cacheChannel <- cacheResult{nil, false, err}
	}()

	// Concurrently prepare DB query (don't wait for cache miss)
	go func() {
		entityType := reflect.TypeOf(entity).Elem()

		var queryFields []string
		fieldMap := make(map[string]string)
		for i := 0; i < entityType.NumField(); i++ {
			field := entityType.Field(i)
			if jsonField := field.Tag.Get("json"); jsonField != "" && jsonField != "-" {
				queryFields = append(queryFields, jsonField)
				fieldMap[jsonField] = field.Name
			}
		}

		query := fmt.Sprintf("SELECT %s FROM %s WHERE %s = $1 ORDER BY id DESC LIMIT 1",
			strings.Join(queryFields, ", "),
			m.TableName,
			something)
		row := m.Db.QueryRow(query, val)

		// Create a new instance of the entity
		newEntity := reflect.New(entityType).Interface()

		scanArgs := make([]interface{}, len(queryFields))
		for i, field := range queryFields {
			fieldName := fieldMap[field]
			scanArgs[i] = reflect.ValueOf(newEntity).Elem().FieldByName(fieldName).Addr().Interface()
		}

		err := row.Scan(scanArgs...)
		if err != nil {
			log.Printf("Error fetching %s: %s\n", m.EntityName, err)
			dbChannel <- cacheResult{nil, false, err}
			return
		}

		dbChannel <- cacheResult{newEntity, false, nil}
	}()

	// Check cache result first
	cacheRes := <-cacheChannel
	if cacheRes.found {
		// Just drain the db channel to not leak the goroutine
		go func() {
			<-dbChannel
		}()
		return cacheRes.entity, true, nil
	}

	// Get database result if cache miss
	dbRes := <-dbChannel
	if dbRes.err != nil {
		return nil, false, dbRes.err
	}

	// Cache the result in a separate goroutine
	go func() {
		cacheData, err := json.Marshal(dbRes.entity)
		if err == nil {
			m.Redis.Set(ctx, cacheKey, cacheData, cacheDuration)
		}
	}()

	return dbRes.entity, false, nil
}

// Optional: Add a timeout version to ensure the function doesn't hang
func (m *BaseRepository) GetOneBySomethingWithCacheTimeout(ctx context.Context, something string, val string, entity interface{}, timeout time.Duration) (interface{}, bool, error) {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	return m.GetOneBySomethingWithCache(ctx, something, val, entity)
}

func (m *BaseRepository) Insert(data interface{}, entity interface{}) (interface{}, error) {
	// Reflect to get type and value information of the data to be inserted
	dataType := reflect.TypeOf(data)
	dataValue := reflect.ValueOf(data)

	// Prepare INSERT statement dynamically
	var placeholders []string
	var values []interface{}

	// Generate ULID or any other ID logic
	ulidId := lib.GenerateULID()

	// Add ULID as the first value
	placeholders = append(placeholders, fmt.Sprintf("$%d", len(placeholders)+1))
	values = append(values, ulidId)

	// Initialize bluemonday policy
	policy := bluemonday.UGCPolicy()

	// Add other field values (excluding ID)
	for i := 0; i < dataType.NumField(); i++ {
		field := dataType.Field(i)
		if field.Name == "Id" { // Skip ID field
			continue
		}

		// Get field value and sanitize if it's a string
		fieldValue := dataValue.Field(i)
		var sanitizedValue interface{}

		if fieldValue.Kind() == reflect.String {
			strValue := fieldValue.String()

			// Convert empty strings to nil (NULL in database)
			if strValue == "" {
				sanitizedValue = nil
			} else {
				sanitizedValue = policy.Sanitize(strValue)
			}
		} else {
			// For non-string types, use the value directly
			sanitizedValue = fieldValue.Interface()
		}

		placeholders = append(placeholders, fmt.Sprintf("$%d", len(placeholders)+1))
		values = append(values, sanitizedValue)
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s) RETURNING *",
		m.TableName,
		strings.Join(m.GetColumnNames(dataType), ", "),
		strings.Join(placeholders, ", "),
	)

	log.Println(query)
	// Create a pointer to a new instance of the entity type
	entityType := reflect.TypeOf(entity)
	insertedEntity := reflect.New(entityType).Interface()

	// Perform the database query and scan the result into insertedEntity
	err := m.Db.QueryRow(query, values...).Scan(m.getFieldPointers(insertedEntity)...)
	if err != nil {
		log.Printf("Error executing %s insertion statement: %s\n", m.EntityName, err)
		return nil, err
	}

	return insertedEntity, nil
}

func (m *BaseRepository) InsertSerial(post interface{}, entity interface{}) (interface{}, error) {
	// Get type information of the post
	postType := reflect.TypeOf(post)
	postValue := reflect.ValueOf(post)

	// Prepare INSERT statement dynamically
	var placeholders []string
	var values []interface{}

	// Add other field values (excluding ID)
	for i := 0; i < postType.NumField(); i++ {
		fieldValue := postValue.Field(i).Interface()
		placeholders = append(placeholders, fmt.Sprintf("$%d", len(placeholders)+1))
		values = append(values, fieldValue)
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s) RETURNING *",
		m.TableName,
		strings.Join(m.GetColumnNames(postType), ", "),
		strings.Join(placeholders, ", "),
	)

	// Create a pointer to a new instance of the entity type
	entityType := reflect.TypeOf(entity)
	insertedEntity := reflect.New(entityType).Interface()

	// Perform the database query and scan the result into insertedEntity
	err := m.Db.QueryRow(query, values...).Scan(m.getFieldPointers(insertedEntity)...)
	if err != nil {
		log.Printf("Error executing %s insertion statement: %s\n", m.EntityName, err)
		return nil, err
	}

	return insertedEntity, nil
}

func (m *BaseRepository) InsertSerialToken(post model.PostUserVerificationRequest, entity interface{}) (interface{}, error) {
	var values []interface{}

	token, err0 := lib.GenerateToken()
	if err0 != nil {
		return nil, err0
	}

	values = append(values, post.Email, token)

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s) RETURNING *",
		m.TableName,
		"email, token",
		"$1, $2",
	)

	// Create a pointer to a new instance of the entity type
	entityType := reflect.TypeOf(entity)
	insertedEntity := reflect.New(entityType).Interface()

	// Perform the database query and scan the result into insertedEntity
	err := m.Db.QueryRow(query, values...).Scan(m.getFieldPointers(insertedEntity)...)
	if err != nil {
		log.Printf("Error executing %s insertion statement: %s\n", m.EntityName, err)
		return nil, err
	}

	return insertedEntity, nil
}

// Helper function to get pointers to fields of a struct
func (m *BaseRepository) getFieldPointers(entity interface{}) []interface{} {
	// Get type information of the entity
	entityType := reflect.TypeOf(entity).Elem()

	// Create a slice to hold pointers to each field
	pointers := make([]interface{}, entityType.NumField())

	// Populate pointers with addresses of fields
	for i := 0; i < entityType.NumField(); i++ {
		pointers[i] = reflect.ValueOf(entity).Elem().Field(i).Addr().Interface()
	}

	return pointers
}

// GetColumnNames retrieves column names from a struct type
func (m *BaseRepository) GetColumnNames(entityType reflect.Type) []string {
	var columnNames []string
	for i := 0; i < entityType.NumField(); i++ {
		field := entityType.Field(i)
		// Assuming your PostgreSQL columns are in snake_case
		columnName := lib.ToSnakeCase(field.Name)
		columnNames = append(columnNames, columnName)
	}
	return columnNames
}

func (m *BaseRepository) Update(id string, updates interface{}, entity interface{}) (interface{}, error) {
	valueType := reflect.ValueOf(updates)
	typeValue := valueType.Type()

	var setClauses []string
	var args []interface{}

	// Initialize bluemonday HTML sanitizer
	p := bluemonday.UGCPolicy()

	for i := 0; i < valueType.NumField(); i++ {
		field := valueType.Field(i)
		fieldName := typeValue.Field(i).Tag.Get("json")

		if idx := strings.Index(fieldName, ","); idx != -1 {
			fieldName = fieldName[:idx]
		}

		// Dereference pointers if the field is a pointer type
		if field.Kind() == reflect.Ptr {
			if field.IsNil() {
				continue // Skip nil pointers
			}
			field = field.Elem() // Dereference pointer to get the underlying value
		}

		// Get the field's value
		fieldValue := field.Interface()

		// Check if field is zero (uninitialized or default value), but handle booleans explicitly
		zeroValue := reflect.Zero(field.Type()).Interface()
		if reflect.DeepEqual(fieldValue, zeroValue) && field.Kind() != reflect.Bool {
			continue // Skip zero values except for booleans
		}

		// Debug: Print type and value of fieldValue
		fmt.Printf("Field %s - Type: %T, Value: %v\n", fieldName, fieldValue, fieldValue)

		log.Println(field.Kind() == reflect.String)
		// Handle different types dynamically
		switch v := fieldValue.(type) {
		case string:
			// Sanitize HTML content if the field type is string
			sanitizedValue := p.Sanitize(v)
			args = append(args, sanitizedValue)
		case fmt.Stringer:
			// Handle types that implement fmt.Stringer
			sanitizedValue := p.Sanitize(v.String())
			args = append(args, sanitizedValue)
		default:
			// For non-string types, use the field value directly
			args = append(args, fieldValue)
		}

		// Construct the SET clause for the SQL query
		setClauses = append(setClauses, fieldName+" = $"+strconv.Itoa(len(args)))
	}

	log.Println(setClauses)
	// Check if there are any fields to update
	if len(setClauses) == 0 {
		log.Printf("Nothing to update for %s with ID %s\n", m.EntityName, id)
		return nil, errors.New("nothing to update, all fields are zero values")
	}

	// Append the ID as the last argument
	args = append(args, id)
	argIndex := len(args)

	// Construct the UPDATE query
	query := fmt.Sprintf("UPDATE %s SET %s WHERE id = $%d", m.TableName, strings.Join(setClauses, ", "), argIndex)

	// Prepare and execute the SQL statement
	stmt, err := m.Db.Prepare(query)
	if err != nil {
		log.Printf("Error preparing %s update statement: %s\n", m.EntityName, err)
		return nil, err
	}
	defer stmt.Close()

	_, err = stmt.Exec(args...)
	if err != nil {
		log.Printf("Error updating %s: %s\n", m.EntityName, err)
		return nil, err
	}

	// Fetch and return the updated entity
	updatedEntity, err := m.GetOne(id, entity)
	if err != nil {
		return nil, errors.New("entity not found after update")
	}

	return updatedEntity, nil
}

func (m *BaseRepository) UpdateBySomething(something string, val string, updates interface{}, entity interface{}) (interface{}, error) {
	valueType := reflect.ValueOf(updates)
	typeValue := valueType.Type()

	var setClauses []string
	var args []interface{}

	// Initialize bluemonday HTML sanitizer
	p := bluemonday.UGCPolicy()

	for i := 0; i < valueType.NumField(); i++ {
		field := valueType.Field(i)
		fieldName := typeValue.Field(i).Tag.Get("json")

		if idx := strings.Index(fieldName, ","); idx != -1 {
			fieldName = fieldName[:idx]
		}

		// Dereference pointers if the field is a pointer type
		if field.Kind() == reflect.Ptr {
			if field.IsNil() {
				continue // Skip nil pointers
			}
			field = field.Elem() // Dereference pointer to get the underlying value
		}

		// Get the field's value
		fieldValue := field.Interface()

		// Check if field is zero (uninitialized or default value), but handle booleans explicitly
		zeroValue := reflect.Zero(field.Type()).Interface()
		if reflect.DeepEqual(fieldValue, zeroValue) && field.Kind() != reflect.Bool {
			continue // Skip zero values except for booleans
		}

		// Debug: Print type and value of fieldValue
		fmt.Printf("Field %s - Type: %T, Value: %v\n", fieldName, fieldValue, fieldValue)

		// Handle different types dynamically
		switch v := fieldValue.(type) {
		case time.Time:
			// Format time in PostgreSQL compatible format
			args = append(args, v.Format("2006-01-02T15:04:05.999999Z"))
		case string:
			// Sanitize HTML content if the field type is string
			sanitizedValue := p.Sanitize(v)
			args = append(args, sanitizedValue)
		default:
			// For non-string types, check if it implements fmt.Stringer
			if stringer, ok := fieldValue.(fmt.Stringer); ok {
				sanitizedValue := p.Sanitize(stringer.String())
				args = append(args, sanitizedValue)
			} else {
				// Use the field value directly
				args = append(args, fieldValue)
			}
		}

		// Construct the SET clause for the SQL query
		setClauses = append(setClauses, fieldName+" = $"+strconv.Itoa(len(args)))
	}

	log.Println(setClauses)
	// Check if there are no fields to update
	if len(setClauses) == 0 {
		log.Printf("Nothing to update for %s with something %s\n", m.EntityName, val)
		return nil, errors.New("nothing to update, all fields are zero values")
	}

	// Append the val as the last argument
	args = append(args, val)
	argIndex := len(args)

	// Construct the UPDATE query
	query := fmt.Sprintf("UPDATE %s SET %s WHERE %s = $%d", m.TableName, strings.Join(setClauses, ", "), something, argIndex)

	// Debug: Print the query and args
	log.Printf("Query: %s\n", query)
	log.Printf("Args: %+v\n", args)

	// Prepare and execute the SQL statement
	stmt, err := m.Db.Prepare(query)
	if err != nil {
		log.Printf("Error preparing %s update statement: %s\n", m.EntityName, err)
		return nil, err
	}
	defer stmt.Close()

	_, err = stmt.Exec(args...)
	if err != nil {
		log.Printf("Error updating %s: %s\n", m.EntityName, err)
		return nil, err
	}

	// Fetch and return the updated entity
	updatedEntity, err := m.GetOneBySomething(something, val, entity)
	if err != nil {
		return nil, errors.New("entity not found after update")
	}

	return updatedEntity, nil
}
