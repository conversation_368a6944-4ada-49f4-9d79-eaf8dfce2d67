package model

import (
	"time"
)

// VisibilityStatus defines the available visibility statuses for an exam
type VisibilityStatus string

const (
	VisibilityPublic VisibilityStatus = "PUBLIC"
	VisibilityDraft  VisibilityStatus = "DRAFT"
)

// ExamType defines the available visibility statuses for an exam
type ExamType string

const (
	TypeExam     ExamType = "EXAM"
	TypePractice ExamType = "PRACTICE"
)

// AvailableExam represents the available exams in the system
type AvailableExam struct {
	Id            string           `json:"id" db:"id"`
	Name          string           `json:"name" db:"name"`
	Subname       string           `json:"subname" db:"subname"`
	Description   *string          `json:"description" db:"description"`
	BaselinePrice *float64         `json:"baseline_price,omitempty" db:"baseline_price"`
	Visibility    VisibilityStatus `json:"visibility" db:"visibility"`
	Duration      string           `json:"duration" db:"duration"` // Using string for interval representation
	Type          ExamType         `json:"type" db:"type"`
	Subject       *string          `json:"subject" db:"subject"`
	CreatedAt     time.Time        `json:"created_at" db:"created_at"`
	ModifiedAt    time.Time        `json:"modified_at" db:"modified_at"`
	UserId        string           `json:"user_id" db:"user_id"`
}

// AvailableExam represents the available exams in the system
type AvailableExamV2 struct {
	Id               string           `json:"id" db:"id"`
	Name             string           `json:"name" db:"name"`
	Subname          string           `json:"subname" db:"subname"`
	Description      *string          `json:"description" db:"description"`
	BaselinePrice    *float64         `json:"baseline_price,omitempty" db:"baseline_price"`
	Visibility       VisibilityStatus `json:"visibility" db:"visibility"`
	Duration         string           `json:"duration" db:"duration"` // Using string for interval representation
	Type             ExamType         `json:"type" db:"type"`
	Subject          *string          `json:"subject" db:"subject"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	ModifiedAt       time.Time        `json:"modified_at" db:"modified_at"`
	UserId           string           `json:"user_id" db:"user_id"`
	MediaUrl         string           `json:"media_url" db:"media_url"`
	CategoryName     *string          `json:"category_name,omitempty" db:"category_name"`           // Category name
	CategoryId       *string          `json:"category_id,omitempty" db:"category_id"`               // Added category ID
	CategoryImageUrl *string          `json:"category_image_url,omitempty" db:"category_image_url"` // Added category image URL
	IsPurchased      bool             `json:"is_purchased" db:"is_purchased"`                       // Purchase status
	PurchaseDate     *time.Time       `json:"purchase_date,omitempty" db:"purchase_date"`           // Purchase date
	IsFreeAccess     *string          `json:"is_free_access,omitempty" db:"is_free_access"`
}

type ExamSessionData struct {
	AvailableExamV2
	SessionId     string  `json:"session_id" db:"session_id"`
	SessionStatus string  `json:"session_status" db:"session_status"`
	Score         float64 `json:"score" db:"score"`
}

// PostAvailableExam is used for creating a new available exam
type PostAvailableExam struct {
	Name          string           `json:"name" binding:"required"`
	Subname       string           `json:"subname" binding:"required"`
	Description   string           `json:"description" binding:"required"`
	BaselinePrice float64          `json:"baseline_price" binding:"required"`
	Visibility    VisibilityStatus `json:"visibility" binding:"required"`
	Duration      string           `json:"duration" binding:"required"` // Duration for new exam creation
	Type          ExamType         `json:"type"`
	Subject       string           `json:"subject"`
	UserId        string           `json:"user_id" binding:"required"`
}

// UpdateAvailableExam is used for updating an existing available exam
type UpdateAvailableExam struct {
	Name          *string           `json:"name,omitempty"`
	Subname       *string           `json:"subname,omitempty"`
	Description   *string           `json:"description,omitempty"`
	BaselinePrice *float64          `json:"baseline_price,omitempty"`
	Visibility    *VisibilityStatus `json:"visibility,omitempty"`
	Duration      *string           `json:"duration,omitempty"` // Pointer to handle optional update
	Type          *ExamType         `json:"type,omitempty"`
	Subject       *string           `json:"subject,omitempty"`
}

// AvailableExamUri is used for binding URI parameters for available exam endpoints
type AvailableExamUri struct {
	ID string `uri:"id" binding:"required"`
}
type AvailableExamUserEmailUri struct {
	USEREMAIL string `uri:"user_email" binding:"required"`
}

// ExamQuestionAnswerHint represents a hint for an exam question
type ExamQuestionAnswerHint struct {
	Id           string  `json:"id" db:"id"`
	ExamId       string  `json:"exam_id" db:"exam_id"`
	Data         string  `json:"data,omitempty" db:"data"`
	PassingGrade *string `json:"passing_grade" db:"passing_grade"`
}

// PostExamQuestionAnswerHint is used for creating a new exam question answer hint
type PostExamQuestionAnswerHint struct {
	ExamId       string  `json:"exam_id" binding:"required"`
	Data         string  `json:"data" binding:"required"`
	PassingGrade *string `json:"passing_grade" binding:"required"`
}

type PostExamQuestionAnswerHintSubject struct {
	Subject string `json:"subject" binding:"required"`
}
