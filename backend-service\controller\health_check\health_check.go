package controller

import (
	"database/sql"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	repository "github.com/terang-ai/backend-service/repository/health_check"
)

type HealthCheckController struct {
	Db    *sql.DB
	Redis *redis.Client
}

func NewHealthCheckController(db *sql.DB, redisClient *redis.Client) HealthCheckControllerInterface {
	return &HealthCheckController{Db: db, Redis: redisClient}
}

// HealthCheck checks the database and Redis connection
func (m *HealthCheckController) HealthCheck(c *gin.Context) {
	DB := m.Db
	REDIS := m.Redis
	repo := repository.NewHealthCheckRepository(DB, REDIS)

	dbHealthy, dbErr := repo.DBHealthCheck()
	redisHealthy, redisErr := repo.REDISHealthCheck()

	if dbErr != nil || redisErr != nil {
		status := "failed"
		msg := "errors detected"
		if dbErr != nil {
			log.Println("Database error:", dbErr)
			msg += ", database error"
		}
		if redisErr != nil {
			log.Println("Redis error:", redisErr)
			msg += ", redis error"
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"status":      status,
			"db_error":    dbErr,
			"redis_error": redisErr,
			"msg":         msg,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"postgres_status": dbHealthy,
		"redis_status":    redisHealthy,
		"status":          "success",
		"msg":             "db and redis connected successfully",
	})
}
