package custom

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"
	"github.com/terang-ai/backend-service/lib"
)

var (
	// StrictPolicy for IDs, codes, and other basic strings
	strictSanitizer = bluemonday.StrictPolicy()

	// UGCPolicy for user-generated content like URLs and user agents
	ugcSanitizer = bluemonday.UGCPolicy()
)

// SanitizedInput is a helper struct to hold sanitized versions of common inputs
type SanitizedInput struct {
	UserID   string
	Code     string
	Type     string
	Status   string
	Metadata map[string]string
}

// sanitizeRouteParams sanitizes common route parameters
func sanitizeRouteParams(c *gin.Context) SanitizedInput {
	return SanitizedInput{
		UserID: strictSanitizer.Sanitize(c.<PERSON>("userId")),
		Code:   strictSanitizer.Sanitize(c.Param("code")),
		Type:   strictSanitizer.Sanitize(c.Param("type")),
	}
}

// SanitizeMiddleware sanitizes all incoming requests
func SanitizeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Sanitize URL parameters
		for i, param := range c.Params {
			c.Params[i] = gin.Param{
				Key:   param.Key,
				Value: strictSanitizer.Sanitize(param.Value),
			}
		}

		// Sanitize query parameters
		for key, values := range c.Request.URL.Query() {
			for i, value := range values {
				values[i] = strictSanitizer.Sanitize(value)
			}
			c.Request.URL.Query()[key] = values
		}

		c.Next()
	}
}

// RegisterReferralRoutes registers the referral related routes
func RegisterReferralRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	v0.Use(SanitizeMiddleware())
	{
		v0.GET("/referral/:userId", getReferralCodes(dbx))                 // Get all referral codes for user
		v0.GET("/referral/:userId/:code", getReferralDetails(dbx))         // Get specific referral code details
		v0.POST("/referral/:userId", createReferralCode(dbx))              // Create new referral code
		v0.DELETE("/referral/:userId/:code", deleteReferralCode(dbx))      // Delete a referral code
		v0.GET("/referral/:userId/analytics", getReferralAnalytics(dbx))   // Get analytics for user's referrals
		v0.GET("/referral/analytics/:code", getReferralCodeAnalytics(dbx)) // Get analytics based on code
		v0.POST("/referral/track/:code", trackReferralVisit(dbx))          // Track referral link visit
		v0.POST("/referral/redeem/:code", redeemReferralCode(dbx))         // Redeem a referral code
		v0.POST("/referral/update-redeemability", updateReferralRedeemability(dbx))
		v0.GET("/referral/validate/:code", validateReferralCodeAPI(dbx))    // Validate a referral code
		v0.GET("/referral/check-duplicate", checkDuplicateReferralAPI(dbx)) // Check for duplicate usage
		v0.GET("/referral/calculate/:type", calculateRewardsAPI(dbx))       // Calculate potential rewards
		v0.PUT("/referral/status/:code", updateReferralStatusAPI(dbx))      // Update referral status

		v0.GET("/referral/balance/:userId", getReferralBalance(dbx))           // Get user's balance
		v0.POST("/referral/balance/withdraw", requestWithdrawal(dbx))          // Request withdrawal
		v0.GET("/referral/balance/history/:userId", getWithdrawalHistory(dbx)) // Get withdrawal history

		v0.POST("/referral/config/:userId", createUserConfig(dbx))         // Create user-specific config
		v0.GET("/referral/config/:userId", getUserConfigs(dbx))            // Get user's configs
		v0.GET("/referral/config/:userId/:type", getConfigByType(dbx))     // Get specific config
		v0.PUT("/referral/config/:userId/:type", updateUserConfig(dbx))    // Update user config
		v0.DELETE("/referral/config/:userId/:type", deleteUserConfig(dbx)) // Delete user config
		v0.GET("/referral/config/current/:type", getCurrentConfig(dbx))
		v0.GET("/referral/withdrawal/update/status/:referenceNo", CheckPayoutStatusHandler(dbx))
		v0.GET("/referral/withdrawal/status/:referenceNo", GetPayoutStatusAPI(dbx))
	}
}

// ReferralBalance represents the user's referral balance
type ReferralBalance struct {
	ID               string    `json:"id" db:"id"` // Add ID field
	UserID           string    `json:"userId" db:"user_id"`
	AvailableBalance float64   `json:"availableBalance" db:"available_balance"`
	PendingBalance   float64   `json:"pendingBalance" db:"pending_balance"`
	LifetimeEarned   float64   `json:"lifetimeEarned" db:"lifetime_earned"`
	CreatedAt        time.Time `json:"createdAt" db:"created_at"`
	ModifiedAt       time.Time `json:"modifiedAt" db:"modified_at"` // Add ModifiedAt field
}

// ReferralConfiguration represents a configuration record
type ReferralConfiguration struct {
	ID         string          `db:"id" json:"id"`
	UserID     *string         `db:"user_id" json:"userId,omitempty"`
	Type       string          `db:"type" json:"type"`
	Config     json.RawMessage `db:"config" json:"config"`
	IsActive   bool            `db:"is_active" json:"isActive"`
	CreatedAt  time.Time       `db:"created_at" json:"createdAt"`
	ModifiedAt time.Time       `db:"modified_at" json:"modifiedAt"`
}

// ConfigRequest represents the request to create/update a configuration
type ConfigRequest struct {
	Type   string `json:"type" binding:"required"`
	Config struct {
		Referrer struct {
			Type      string   `json:"type" binding:"required"`
			Amount    float64  `json:"amount" binding:"required"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referrer" binding:"required"`
		Referee struct {
			Type      string   `json:"type" binding:"required"`
			Amount    float64  `json:"amount" binding:"required"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referee" binding:"required"`
		ValidityDays int  `json:"validityDays" binding:"required"`
		MaxUses      *int `json:"maxUses,omitempty"`
		Conditions   []struct {
			Field    string      `json:"field" binding:"required"`
			Operator string      `json:"operator" binding:"required"`
			Value    interface{} `json:"value" binding:"required"`
		} `json:"conditions" binding:"required"`
	} `json:"config" binding:"required"`
	IsActive bool `json:"isActive"`
}

// WithdrawalRequest represents a withdrawal request
type WithdrawalRequest struct {
	UserID        string                 `json:"userId" binding:"required"`
	Amount        float64                `json:"amount" binding:"required,gt=0"`
	PaymentMethod string                 `json:"paymentMethod" binding:"required"`
	BankDetails   map[string]interface{} `json:"bankDetails" binding:"required"`
}

// Get user's referral balance
func getReferralBalance(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		input := sanitizeRouteParams(c)
		userID := input.UserID

		var balance ReferralBalance
		query := `SELECT * FROM referral_balances WHERE user_id = $1`
		err := dbx.Get(&balance, query, userID)
		if err != nil {
			if err == sql.ErrNoRows {
				// Return empty balance with zero values when no record exists
				c.JSON(http.StatusOK, ReferralBalance{
					UserID:           userID,
					AvailableBalance: 0,
					PendingBalance:   0,
					LifetimeEarned:   0,
					CreatedAt:        time.Now(),
					ModifiedAt:       time.Now(),
				})
				return
			}
			// Log the specific error for debugging
			log.Printf("Error fetching balance for user %s: %v", userID, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch balance"})
			return
		}

		c.JSON(http.StatusOK, balance)
	}
}

// Request a withdrawal
func requestWithdrawal(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req WithdrawalRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// Check balance
		var balance float64
		err = tx.Get(&balance, `
            SELECT available_balance 
            FROM referral_balances 
            WHERE user_id = $1
            FOR UPDATE`,
			req.UserID)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Balance not found"})
				return
			}
			log.Printf("Error checking balance: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check balance"})
			return
		}

		if balance < req.Amount {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Insufficient balance"})
			return
		}

		// Get user details for payout
		var user struct {
			FirstName string `db:"first_name"`
			LastName  string `db:"last_name"`
			Email     string `db:"email"`
		}
		err = tx.Get(&user, "SELECT first_name, last_name, email FROM users WHERE id = $1", req.UserID)
		if err != nil {
			log.Printf("Error fetching user details: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user details"})
			return
		}

		// Validate bank details
		if req.BankDetails == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Bank details are required"})
			return
		}

		bankCode, ok := req.BankDetails["bankCode"].(string)
		if !ok || bankCode == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Bank code is required"})
			return
		}

		accountNumber, ok := req.BankDetails["accountNumber"].(string)
		if !ok || accountNumber == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Account number is required"})
			return
		}

		// Create withdrawal request
		withdrawalID := lib.GenerateULID()
		bankDetails := req.BankDetails

		log.Println(bankDetails)

		bankDetailsJSON, err := json.Marshal(bankDetails)
		if err != nil {
			log.Printf("Error marshaling bank details: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bank details format"})
			return
		}

		log.Println(string(bankDetailsJSON))

		// Insert withdrawal request - balance update will be handled by trigger
		_, err = tx.Exec(`
            INSERT INTO withdrawal_requests (
                id, user_id, amount, status,
                payment_method, payment_details
            )
            VALUES ($1, $2, $3, 'PENDING', $4, $5::jsonb)`,
			withdrawalID,
			req.UserID,
			req.Amount,
			req.PaymentMethod,
			string(bankDetailsJSON),
		)
		if err != nil {
			log.Printf("Error creating withdrawal request: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create withdrawal request"})
			return
		}

		// Commit transaction before async processing
		if err = tx.Commit(); err != nil {
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process withdrawal"})
			return
		}

		// Initialize Iris service
		irisService, err := NewIrisPayoutService()
		if err != nil {
			log.Printf("Error initializing iris service: %v", err)
			c.JSON(http.StatusOK, gin.H{
				"message": "Withdrawal request created but payout failed to initialize",
				"id":      withdrawalID,
			})
			return
		}

		// Convert bankCode to lowercase for Midtrans
		bankCode = strings.ToLower(bankCode)

		// Create payout request
		payoutReq := &PayoutRequest{
			Payouts: []PayoutItem{
				{
					BeneficiaryName:    fmt.Sprintf("%s %s", user.FirstName, user.LastName),
					BeneficiaryAccount: accountNumber,
					BeneficiaryBank:    bankCode,
					BeneficiaryEmail:   user.Email,
					Amount:             fmt.Sprintf("%.2f", req.Amount),
					Notes:              fmt.Sprintf("Withdrawal request %s", withdrawalID),
				},
			},
		}

		// Initiate payout asynchronously
		go func() {
			resp, err := irisService.CreatePayout(payoutReq)
			if err != nil {
				log.Printf("Error creating payout for withdrawal %s: %v", withdrawalID, err)

				// Update withdrawal status to failed with proper JSON
				details := map[string]string{"error": err.Error()}
				detailsJSON, _ := json.Marshal(details)

				_, err = dbx.Exec(`
                    UPDATE withdrawal_requests 
                    SET 
                        status = 'FAILED',
                        payment_details = payment_details || $2,
                        modified_at = CURRENT_TIMESTAMP
                    WHERE id = $1`,
					withdrawalID,
					string(detailsJSON),
				)
				if err != nil { // Add this error check
					log.Printf("Error updating withdrawal status: %v", err)
					return
				}
				return
			}

			if len(resp.Payouts) > 0 {
				// Update withdrawal with reference number using proper JSON
				details := map[string]string{
					"reference_no": resp.Payouts[0].ReferenceNo,
					"iris_status":  resp.Payouts[0].Status,
				}
				detailsJSON, err := json.Marshal(details)
				if err != nil {
					log.Printf("Error marshaling reference details: %v", err)
					return
				}

				log.Println(string(detailsJSON))

				_, err = dbx.Exec(`
                    UPDATE withdrawal_requests 
                    SET 
                        status = CASE 
                            WHEN $2 = 'queued' THEN 'PROCESSING'
                            ELSE 'FAILED'
                        END,
                        payment_details = payment_details || $3,
                        modified_at = CURRENT_TIMESTAMP
                    WHERE id = $1`,
					withdrawalID,
					resp.Payouts[0].Status,
					string(detailsJSON),
				)
				if err != nil { // Add this error check
					log.Printf("Error updating withdrawal status: %v", err)
					return
				}
			}
		}()

		c.JSON(http.StatusOK, gin.H{
			"message": "Withdrawal request created and payout initiated",
			"id":      withdrawalID,
		})
	}
}

// WithdrawalHistory represents a withdrawal record with proper JSON handling
type WithdrawalHistory struct {
	ID            string          `json:"id" db:"id"`
	Amount        float64         `json:"amount" db:"amount"`
	Status        string          `json:"status" db:"status"`
	PaymentMethod string          `json:"paymentMethod" db:"payment_method"`
	CreatedAt     time.Time       `json:"createdAt" db:"created_at"`
	ProcessedAt   *time.Time      `json:"processedAt,omitempty" db:"processed_at"`
	BankDetails   json.RawMessage `json:"bankDetails" db:"payment_details"`
}

// PayoutBankDetails represents the bank details structure for payouts
type PayoutBankDetails struct {
	BankDetails map[string]interface{} `json:"bankDetails"`
}

// NewPayoutBankDetails creates a new PayoutBankDetails instance
func NewPayoutBankDetails(bankCode, accountNumber string) PayoutBankDetails {
	return PayoutBankDetails{
		BankDetails: map[string]interface{}{
			"bankCode":      bankCode,
			"accountNumber": accountNumber,
		},
	}
}

// Get withdrawal history
func getWithdrawalHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		var rawWithdrawals []WithdrawalHistory
		query := `
            SELECT id, amount, status, payment_method, 
                   created_at, processed_at, payment_details
            FROM withdrawal_requests
            WHERE user_id = $1
            ORDER BY created_at DESC
        `

		if err := dbx.Select(&rawWithdrawals, query, userID); err != nil {
			log.Printf("Error fetching withdrawal history: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch withdrawal history"})
			return
		}

		// Convert raw JSON to proper format for response
		type ResponseWithdrawal struct {
			ID            string                 `json:"id"`
			Amount        float64                `json:"amount"`
			Status        string                 `json:"status"`
			PaymentMethod string                 `json:"paymentMethod"`
			CreatedAt     time.Time              `json:"createdAt"`
			ProcessedAt   *time.Time             `json:"processedAt,omitempty"`
			BankDetails   map[string]interface{} `json:"bankDetails"`
		}

		withdrawals := make([]ResponseWithdrawal, len(rawWithdrawals))
		for i, raw := range rawWithdrawals {
			withdrawal := ResponseWithdrawal{
				ID:            raw.ID,
				Amount:        raw.Amount,
				Status:        raw.Status,
				PaymentMethod: raw.PaymentMethod,
				CreatedAt:     raw.CreatedAt,
				ProcessedAt:   raw.ProcessedAt,
			}

			// Parse the JSON bank details
			if raw.BankDetails != nil {
				var bankDetails map[string]interface{}
				if err := json.Unmarshal(raw.BankDetails, &bankDetails); err != nil {
					log.Printf("Error parsing bank details for withdrawal %s: %v", raw.ID, err)
					// Don't fail the whole request, just set empty map
					bankDetails = make(map[string]interface{})
				}
				withdrawal.BankDetails = bankDetails
			} else {
				withdrawal.BankDetails = make(map[string]interface{})
			}

			withdrawals[i] = withdrawal
		}

		c.JSON(http.StatusOK, gin.H{"withdrawals": withdrawals})
	}
}

func getReferralCodeAnalytics(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Param("code")

		// First get the referral ID from the code
		var referralID string
		idQuery := `SELECT id FROM referral_codes WHERE code = $1`
		err := dbx.Get(&referralID, idQuery, code)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Referral code not found"})
				return
			}
			log.Printf("Error getting referral ID: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics"})
			return
		}

		var analytics ReferralAnalytics
		query := `
            WITH referral_metrics AS (
                SELECT 
                    rc.id as code_id,
                    COUNT(DISTINCT CASE WHEN ra.type = 'CLICK' THEN ra.id END) as clicks,
                    COUNT(DISTINCT CASE WHEN ra.type = 'IMPRESSION' THEN ra.id END) as impressions,
                    COUNT(DISTINCT CASE WHEN ru.type = 'REGISTRATION' AND ru.is_redeemable IS TRUE THEN ru.id END) as registrations,
                    COUNT(DISTINCT CASE WHEN ru.type = 'REGISTRATION' AND (ru.is_redeemable IS FALSE OR ru.is_redeemable IS NULL) THEN ru.id END) as non_redeemable_registrations,
                    COUNT(DISTINCT CASE WHEN ru.type = 'PURCHASE' THEN ru.id END) as purchases,
                    COUNT(DISTINCT CASE WHEN ru.type = 'SUBSCRIPTION' THEN ru.id END) as subscriptions,
                    COALESCE(SUM(
                        CASE 
                            WHEN ru.type IN ('PURCHASE', 'SUBSCRIPTION') OR ru.is_redeemable IS TRUE 
                            THEN rr.amount 
                            ELSE 0 
                        END
                    ), 0) as total_commission
                FROM referral_codes rc
                LEFT JOIN referral_analytics ra ON rc.id = ra.referral_code_id
                LEFT JOIN referral_uses ru ON rc.id = ru.referral_code_id
                LEFT JOIN referral_rewards rr ON ru.id = rr.referral_use_id
                WHERE rc.id = $1
                GROUP BY rc.id
            )
            SELECT * FROM referral_metrics
        `

		if err := dbx.Get(&analytics, query, referralID); err != nil {
			log.Printf("Error fetching referral code analytics: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch analytics"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"analytics": analytics})
	}
}

// deleteReferralCode deletes a referral code and all associated data
func deleteReferralCode(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		code := c.Param("code")

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// First, verify the code belongs to the user
		var referralID string
		verifyQuery := `
            SELECT id 
            FROM referral_codes 
            WHERE referrer_id = $1 AND code = $2
            FOR UPDATE
        `
		err = tx.Get(&referralID, verifyQuery, userID, code)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Referral code not found"})
				return
			}
			log.Printf("Error verifying referral code ownership: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify referral code"})
			return
		}

		// Delete related records
		// Note: We don't need explicit deletes for tables with ON DELETE CASCADE

		// Delete analytics
		analyticsQuery := `
            DELETE FROM referral_analytics 
            WHERE referral_code_id = $1
        `
		_, err = tx.Exec(analyticsQuery, referralID)
		if err != nil {
			log.Printf("Error deleting referral analytics: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete referral analytics"})
			return
		}

		// Delete rewards
		rewardsQuery := `
            DELETE FROM referral_rewards 
            WHERE referral_use_id IN (
                SELECT id FROM referral_uses WHERE referral_code_id = $1
            )
        `
		_, err = tx.Exec(rewardsQuery, referralID)
		if err != nil {
			log.Printf("Error deleting referral rewards: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete referral rewards"})
			return
		}

		// Delete uses
		usesQuery := `
            DELETE FROM referral_uses 
            WHERE referral_code_id = $1
        `
		_, err = tx.Exec(usesQuery, referralID)
		if err != nil {
			log.Printf("Error deleting referral uses: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete referral uses"})
			return
		}

		// Finally, delete the referral code
		deleteQuery := `
            DELETE FROM referral_codes 
            WHERE id = $1
        `
		result, err := tx.Exec(deleteQuery, referralID)
		if err != nil {
			log.Printf("Error deleting referral code: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete referral code"})
			return
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			log.Printf("Error checking rows affected: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm deletion"})
			return
		}

		if rowsAffected == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Referral code not found"})
			return
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete deletion"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Referral code and associated data deleted successfully",
		})
	}
}

// Update the validateReferralCodeAPI handler to include type validation
func validateReferralCodeAPI(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Param("code")
		referralType := c.Query("type")
		currentUserID := c.Query("userId") // Assume user ID is stored in context after authentication

		validation, err := validateReferralCode(dbx, code, referralType, currentUserID)
		if err != nil {
			log.Printf("Error validating referral code: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate referral code"})
			return
		}

		c.JSON(http.StatusOK, validation)
	}
}

// DuplicateCheckRequest represents the request body for duplicate check
type DuplicateCheckRequest struct {
	RefereeID      string `json:"refereeId" binding:"required"`
	ReferralCodeID string `json:"referralCodeId" binding:"required"`
}

// checkDuplicateReferralAPI provides an API endpoint for checking duplicates
func checkDuplicateReferralAPI(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var request DuplicateCheckRequest
		if err := c.ShouldBindQuery(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid request parameters",
			})
			return
		}

		isDuplicate, err := checkDuplicateReferral(dbx, request.RefereeID, request.ReferralCodeID)
		if err != nil {
			log.Printf("Error checking duplicate referral: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to check for duplicate referral",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"isDuplicate": isDuplicate,
		})
	}
}

// calculateRewardsAPI provides an API endpoint for reward calculation
func calculateRewardsAPI(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		referralType := c.Param("type")
		userID := c.Query("userId") // Get userID from query parameter

		referrerReward, refereeReward, err := calculateRewards(dbx, userID, referralType)
		if err != nil {
			log.Printf("Error calculating rewards: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to calculate rewards",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"referrerReward": referrerReward,
			"refereeReward":  refereeReward,
		})
	}
}

// UpdateStatusRequest represents the request body for status updates
type UpdateStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=ACTIVE EXPIRED USED"`
}

func (r *UpdateStatusRequest) sanitize() {
	r.Status = strictSanitizer.Sanitize(r.Status)
}

// updateReferralStatusAPI provides an API endpoint for updating referral status
func updateReferralStatusAPI(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		input := sanitizeRouteParams(c)

		var request UpdateStatusRequest
		if err := c.BindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid status provided",
			})
			return
		}

		// First get the referral ID from the code
		var referralID string
		query := "SELECT id FROM referral_codes WHERE code = $1"
		err := dbx.Get(&referralID, query, input.Code)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{
					"error": "Referral code not found",
				})
				return
			}
			log.Printf("Error getting referral ID: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to process status update",
			})
			return
		}

		// Update the status
		err = updateReferralStatus(dbx, referralID, request.Status)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{
					"error": "Referral code not found",
				})
				return
			}
			log.Printf("Error updating referral status: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to update status",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Status updated successfully",
		})
	}
}

// Update the ReferralCode struct
type ReferralCode struct {
	ID                     string    `db:"id" json:"id"`
	ReferrerID             string    `db:"referrer_id" json:"referrerId"`
	Code                   string    `db:"code" json:"code"`
	Status                 string    `db:"status" json:"status"`
	ExpiresAt              time.Time `db:"expires_at" json:"expiresAt"`
	CreatedAt              time.Time `db:"created_at" json:"createdAt"`
	ModifiedAt             time.Time `db:"modified_at" json:"modifiedAt"`
	TotalCommission        float64   `db:"total_commission" json:"totalCommission"`
	RegistrationCommission float64   `db:"registration_commission" json:"registrationCommission"`
	PurchaseCommission     float64   `db:"purchase_commission" json:"purchaseCommission"`
	SubscriptionCommission float64   `db:"subscription_commission" json:"subscriptionCommission"`
}

// ReferralAnalytics represents analytics for a referral code
type ReferralAnalytics struct {
	CodeID                     string `db:"code_id" json:"codeId"`
	Clicks                     int    `db:"clicks" json:"clicks"`
	Impressions                int    `db:"impressions" json:"impressions"`
	Registrations              int    `db:"registrations" json:"registrations"`
	NonRedeemableRegistrations int    `db:"non_redeemable_registrations" json:"nonRedeemableRegistrations"`
	Purchases                  int    `db:"purchases" json:"purchases"`
	Subscriptions              int    `db:"subscriptions" json:"subscriptions"`
	TotalCommission            string `db:"total_commission" json:"totalCommission"`
}

// ReferralUse represents a referral code usage
type ReferralUse struct {
	ID                   string    `db:"id" json:"id"`
	ReferralCodeID       string    `db:"referral_code_id" json:"referralCodeId"`
	RefereeID            string    `db:"referee_id" json:"refereeId"`
	Type                 string    `db:"type" json:"type"`
	ReferrerRewardAmount float64   `db:"referrer_reward_amount" json:"referrerRewardAmount"`
	RefereeRewardAmount  float64   `db:"referee_reward_amount" json:"refereeRewardAmount"`
	CreatedAt            time.Time `db:"created_at" json:"createdAt"`
}

type ReferralUseDetails struct {
	Type               string    `db:"type" json:"type"`
	RefereeID          string    `db:"referee_id" json:"refereeId"`
	PaymentID          *string   `db:"payment_id" json:"paymentId,omitempty"`
	PaymentAmount      *float64  `db:"payment_amount" json:"paymentAmount,omitempty"`
	PaymentStatus      *string   `db:"payment_status" json:"paymentStatus,omitempty"`
	SubscriptionID     *string   `db:"subscription_id" json:"subscriptionId,omitempty"`
	SubscriptionTier   *string   `db:"subscription_tier" json:"subscriptionTier,omitempty"`
	MonthlyPrice       *float64  `db:"monthly_price" json:"monthlyPrice,omitempty"`
	SubscriptionStatus *string   `db:"subscription_status" json:"subscriptionStatus,omitempty"`
	IsRedeemable       bool      `db:"is_redeemable" json:"isRedeemable"`
	RewardAmount       float64   `db:"reward_amount" json:"rewardAmount"`
	CommissionPercent  float64   `db:"commission_percent" json:"commissionPercent"`
	CreatedAt          time.Time `db:"created_at" json:"createdAt"`
}

type ReferralCodeDetails struct {
	ID              string               `db:"id" json:"id"`
	ReferrerID      string               `db:"referrer_id" json:"referrerId"`
	Code            string               `db:"code" json:"code"`
	Status          string               `db:"status" json:"status"`
	ExpiresAt       time.Time            `db:"expires_at" json:"expiresAt"`
	CreatedAt       time.Time            `db:"created_at" json:"createdAt"`
	ModifiedAt      time.Time            `db:"modified_at" json:"modifiedAt"`
	TotalCommission float64              `db:"total_commission" json:"totalCommission"`
	UsageDetails    []ReferralUseDetails `json:"usageDetails"`
}

// CreateReferralRequest represents the request to create a referral code
type CreateReferralRequest struct {
	Code string `json:"code" binding:"required"`
}

func (r *CreateReferralRequest) sanitize() {
	r.Code = strictSanitizer.Sanitize(r.Code)
}

// TrackReferralRequest represents the request to track a referral visit
type TrackReferralRequest struct {
	Type       string `json:"type" binding:"required,oneof=CLICK IMPRESSION"`
	IPAddress  string `json:"ipAddress"`
	UserAgent  string `json:"userAgent"`
	RefererURL string `json:"refererUrl"`
}

func (r *TrackReferralRequest) sanitize() {
	r.Type = strictSanitizer.Sanitize(r.Type)
	r.IPAddress = strictSanitizer.Sanitize(r.IPAddress)
	r.UserAgent = ugcSanitizer.Sanitize(r.UserAgent)
	r.RefererURL = ugcSanitizer.Sanitize(r.RefererURL)
}

// RedeemReferralRequest represents the request to redeem a referral code
type RedeemReferralRequest struct {
	RefereeID      string `json:"refereeId" binding:"required"`
	Type           string `json:"type" binding:"required,oneof=REGISTRATION PURCHASE SUBSCRIPTION"`
	PaymentID      string `json:"paymentId"`      // Required for PURCHASE type
	SubscriptionID string `json:"subscriptionId"` // Required for SUBSCRIPTION type
}

func (r *RedeemReferralRequest) sanitize() {
	r.RefereeID = strictSanitizer.Sanitize(r.RefereeID)
	r.Type = strictSanitizer.Sanitize(r.Type)
	r.PaymentID = strictSanitizer.Sanitize(r.PaymentID)
	r.SubscriptionID = strictSanitizer.Sanitize(r.SubscriptionID)
}

func (r *RedeemReferralRequest) validate() error {
	switch r.Type {
	case "PURCHASE":
		if r.PaymentID == "" {
			return fmt.Errorf("paymentId is required for PURCHASE type referral")
		}
		if r.SubscriptionID != "" {
			return fmt.Errorf("subscriptionId should not be set for PURCHASE type referral")
		}
	case "SUBSCRIPTION":
		if r.SubscriptionID == "" {
			return fmt.Errorf("subscriptionId is required for SUBSCRIPTION type referral")
		}
		if r.PaymentID != "" {
			return fmt.Errorf("paymentId should not be set for SUBSCRIPTION type referral")
		}
	case "REGISTRATION":
		if r.PaymentID != "" || r.SubscriptionID != "" {
			return fmt.Errorf("paymentId and subscriptionId should not be set for REGISTRATION type referral")
		}
	}
	return nil
}

// Helper function to update balance from referral
func updateBalanceFromReferral(tx *sqlx.Tx, reward struct {
	UserID string
	Amount float64
	Type   string
}) error {
	// Lock the balance row to prevent concurrent updates
	var currentBalance float64
	lockQuery := `SELECT available_balance FROM referral_balances WHERE user_id = $1 FOR UPDATE`
	if err := tx.Get(&currentBalance, lockQuery, reward.UserID); err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("error locking balance row: %v", err)
	}

	updateQuery := `
        UPDATE referral_balances 
        SET 
            available_balance = available_balance + $1,
            lifetime_earned = lifetime_earned + $1,
            modified_at = CURRENT_TIMESTAMP
        WHERE user_id = $2
    `
	result, err := tx.Exec(updateQuery, reward.Amount, reward.UserID)
	if err != nil {
		return fmt.Errorf("error updating balance: %v", err)
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("error checking rows affected: %v", err)
	}

	if rows == 0 {
		// Insert new balance record if none exists
		insertQuery := `
            INSERT INTO referral_balances (id, user_id, available_balance, pending_balance, lifetime_earned)
            VALUES ($1, $2, $3, 0, $3)
        `
		balanceID := lib.GenerateULID()
		_, err = tx.Exec(insertQuery, balanceID, reward.UserID, reward.Amount)
		if err != nil {
			return fmt.Errorf("error creating new balance: %v", err)
		}
	}

	return nil
}

// redeemReferralCode processes a referral code redemption
func redeemReferralCode(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		input := sanitizeRouteParams(c)

		var request RedeemReferralRequest
		if err := c.BindJSON(&request); err != nil {
			log.Println(err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		request.sanitize()
		if err := request.validate(); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// Get referral code details and verify it's valid
		var referral ReferralCode
		verifyQuery := `
            SELECT id, referrer_id, status
            FROM referral_codes 
            WHERE code = $1 AND status = 'ACTIVE' AND expires_at > CURRENT_TIMESTAMP
            FOR UPDATE
        `
		err = tx.Get(&referral, verifyQuery, input.Code)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Invalid or expired referral code"})
				return
			}
			log.Printf("Error verifying referral code: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify referral code"})
			return
		}

		// Prevent users from redeeming their own referral code
		if referral.ReferrerID == request.RefereeID {
			log.Printf("User %s attempted to redeem their own referral code", request.RefereeID)
			c.JSON(http.StatusBadRequest, gin.H{"error": "You cannot redeem your own referral code"})
			return
		}

		// Get reward configuration
		var config struct {
			ReferrerRewardAmount float64 `db:"referrer_reward_amount"`
			RefereeRewardAmount  float64 `db:"referee_reward_amount"`
		}
		configQuery := `
			WITH effective_configs AS (
				SELECT 
					(config->'referrer'->>'amount')::numeric as referrer_reward_amount,
					(config->'referee'->>'amount')::numeric as referee_reward_amount,
					ROW_NUMBER() OVER (
						PARTITION BY type
						ORDER BY CASE WHEN user_id = $1 THEN 1 ELSE 2 END, created_at DESC
					) AS rn
				FROM referral_configurations
				WHERE type = $2 AND is_active = true
			)
			SELECT 
				referrer_reward_amount,
				referee_reward_amount
			FROM effective_configs
			WHERE rn = 1
		`
		err = tx.Get(&config, configQuery, referral.ReferrerID, request.Type)
		if err != nil {
			log.Printf("Error fetching reward configuration: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process rewards"})
			return
		}

		// Calculate rewards based on type
		var referrerAmount, refereeAmount float64
		// Debug log
		log.Printf("Debug - Type: %s, PaymentID: %v, SubscriptionID: %v",
			request.Type, request.PaymentID, request.SubscriptionID)

		switch request.Type {
		case "REGISTRATION":
			referrerAmount = config.ReferrerRewardAmount
			refereeAmount = config.RefereeRewardAmount

		case "PURCHASE":
			var payment struct {
				Amount float64 `db:"amount"`
				Status string  `db:"status"`
			}
			err = tx.Get(&payment, "SELECT amount, status FROM payments WHERE id = $1", request.PaymentID)
			if err != nil {
				log.Printf("Error fetching payment: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process payment reward"})
				return
			}

			if payment.Status != "PAID" {
				log.Printf("Invalid payment status for referral redemption: %s", payment.Status)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Payment must be completed before redeeming referral"})
				return
			}

			referrerAmount = payment.Amount * (config.ReferrerRewardAmount / 100.0)
			refereeAmount = payment.Amount * (config.RefereeRewardAmount / 100.0)

		case "SUBSCRIPTION":
			var subscription struct {
				MonthlyPrice  float64 `db:"price_monthly"`
				PaymentStatus string  `db:"payment_status"`
				IsActive      bool    `db:"is_active"`
			}
			err = tx.Get(&subscription, `
                SELECT st.price_monthly, us.payment_status, us.is_active
                FROM user_subscriptions us 
                JOIN subscription_tiers st ON us.tier_id = st.id 
                WHERE us.id = $1
            `, request.SubscriptionID)
			if err != nil {
				log.Printf("Error fetching subscription: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process subscription reward"})
				return
			}

			if subscription.PaymentStatus != "PAID" || !subscription.IsActive {
				log.Printf("Invalid subscription status for referral redemption: payment_status=%s, is_active=%v",
					subscription.PaymentStatus, subscription.IsActive)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Subscription must be active and paid before redeeming referral"})
				return
			}

			referrerAmount = subscription.MonthlyPrice * (config.ReferrerRewardAmount / 100.0)
			refereeAmount = subscription.MonthlyPrice * (config.RefereeRewardAmount / 100.0)
		}

		// Create referral use record
		useID := lib.GenerateULID()
		var useQuery string
		var isRedeemable bool

		// Set is_redeemable based on referral type
		// REGISTRATION referrals are not immediately redeemable
		// PURCHASE and SUBSCRIPTION referrals are immediately redeemable
		if request.Type == "REGISTRATION" {
			isRedeemable = false
		} else {
			isRedeemable = true
		}

		switch request.Type {
		case "PURCHASE":
			useQuery = `
				INSERT INTO referral_uses (
					id, referral_code_id, referee_id, type,
					payment_id, subscription_id,
					referrer_reward_amount, referee_reward_amount,
					is_redeemable, created_at, modified_at
				)
				VALUES ($1, $2, $3, $4, $5, NULL, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			`
			_, err = tx.Exec(useQuery,
				useID,
				referral.ID,
				request.RefereeID,
				request.Type,
				request.PaymentID,
				referrerAmount,
				refereeAmount,
				isRedeemable,
			)

		case "SUBSCRIPTION":
			useQuery = `
				INSERT INTO referral_uses (
					id, referral_code_id, referee_id, type,
					payment_id, subscription_id,
					referrer_reward_amount, referee_reward_amount,
					is_redeemable, created_at, modified_at
				)
				VALUES ($1, $2, $3, $4, NULL, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			`
			_, err = tx.Exec(useQuery,
				useID,
				referral.ID,
				request.RefereeID,
				request.Type,
				request.SubscriptionID,
				referrerAmount,
				refereeAmount,
				isRedeemable,
			)

		case "REGISTRATION":
			useQuery = `
				INSERT INTO referral_uses (
					id, referral_code_id, referee_id, type,
					payment_id, subscription_id,
					referrer_reward_amount, referee_reward_amount,
					is_redeemable, created_at, modified_at
				)
				VALUES ($1, $2, $3, $4, NULL, NULL, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			`
			_, err = tx.Exec(useQuery,
				useID,
				referral.ID,
				request.RefereeID,
				request.Type,
				referrerAmount,
				refereeAmount,
				isRedeemable,
			)
		}

		if err != nil {
			log.Printf("Error creating referral use: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process referral"})
			return
		}

		// Only create rewards and update balances if the referral is redeemable
		// For REGISTRATION referrals, this will be handled later when a payment is made
		if isRedeemable {
			// Create rewards for referrer
			rewards := []struct {
				UserID string
				Amount float64
				Type   string
			}{
				{referral.ReferrerID, referrerAmount, "COMMISSION"},
			}

			for _, reward := range rewards {
				// Create reward record
				rewardID := lib.GenerateULID()
				rewardQuery := `
					INSERT INTO referral_rewards (
						id, referral_use_id, user_id,
						reward_type, amount, status,
						created_at, modified_at
					)
					VALUES ($1, $2, $3, $4, $5, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
				`
				_, err = tx.Exec(rewardQuery,
					rewardID,
					useID,
					reward.UserID,
					reward.Type,
					reward.Amount,
				)
				if err != nil {
					log.Printf("Error creating reward: %v", err)
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process rewards"})
					return
				}

				// Update balance
				err = updateBalanceFromReferral(tx, reward)
				if err != nil {
					log.Printf("Error updating balance: %v", err)
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update balance"})
					return
				}
			}
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete referral process"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message":       "Referral code redeemed successfully",
			"referralUseId": useID,
			"isRedeemable":  isRedeemable,
		})
	}
}

// updateReferralRedeemability updates a registration referral to be redeemable
// when a payment of at least 25,000 is made
func updateReferralRedeemability(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			RefereeID string `json:"refereeId" binding:"required"`
			PaymentID string `json:"paymentId" binding:"required"`
		}

		if err := c.BindJSON(&request); err != nil {
			log.Printf("Error binding request: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// Find the REGISTRATION referral use that needs to be updated
		var referralUse struct {
			ID                   string  `db:"id"`
			ReferralCodeID       string  `db:"referral_code_id"`
			ReferrerID           string  `db:"referrer_id"`
			ReferrerRewardAmount float64 `db:"referrer_reward_amount"`
			IsRedeemable         bool    `db:"is_redeemable"`
		}

		findQuery := `
			SELECT ru.id, ru.referral_code_id, rc.referrer_id, ru.referrer_reward_amount, ru.is_redeemable
			FROM referral_uses ru
			JOIN referral_codes rc ON ru.referral_code_id = rc.id
			WHERE ru.referee_id = $1 
			AND ru.type = 'REGISTRATION'
			AND (ru.is_redeemable IS NULL OR ru.is_redeemable = FALSE)
			ORDER BY ru.created_at DESC
			LIMIT 1
			FOR UPDATE
		`

		err = tx.Get(&referralUse, findQuery, request.RefereeID)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "No unredeemed registration referral found for this user"})
				return
			}
			log.Printf("Error finding referral use: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find referral record"})
			return
		}

		// Check if the referral has already been redeemed
		if referralUse.IsRedeemable {
			c.JSON(http.StatusBadRequest, gin.H{"error": "This referral has already been redeemed"})
			return
		}

		// Verify the current payment exists and is valid
		var currentPayment struct {
			Status string  `db:"status"`
			Amount float64 `db:"amount"`
		}
		err = tx.Get(&currentPayment, "SELECT status, amount FROM payments WHERE id = $1", request.PaymentID)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Payment not found"})
				return
			}
			log.Printf("Error fetching payment: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify payment"})
			return
		}

		if currentPayment.Status != "PAID" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Payment must be completed to redeem referral"})
			return
		}

		// Calculate the cumulative payment amount for this user
		var cumulativeAmount float64

		// Query to get the sum of all PAID payments for this user
		// This includes the current payment and any previous paid payments
		cumulativeQuery := `
			SELECT COALESCE(SUM(p.amount), 0) as total_amount
			FROM payments p
			JOIN invoices i ON p.invoice_id = i.id
			JOIN orders o ON i.order_id = o.id
			WHERE o.client_id = $1
			AND p.status = 'PAID'
		`

		err = tx.Get(&cumulativeAmount, cumulativeQuery, request.RefereeID)
		if err != nil {
			log.Printf("Error calculating cumulative payment amount: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to calculate total payments"})
			return
		}

		// Check if cumulative payments meet the minimum amount requirement of 25,000
		// 25000 to 15000 maximum registration referral
		// const MinimumCumulativeAmount = 25000.0
		const MinimumCumulativeAmount = 15000.0
		if cumulativeAmount < MinimumCumulativeAmount {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Cumulative payment amount (%.2f) is less than the minimum required amount (%.2f) to redeem referral",
					cumulativeAmount, MinimumCumulativeAmount),
			})
			return
		}

		// Update the referral use to be redeemable and link to the payment
		_, err = tx.Exec(`
			UPDATE referral_uses
			SET is_redeemable = TRUE, 
			    payment_id = $1,
			    modified_at = CURRENT_TIMESTAMP
			WHERE id = $2
		`, request.PaymentID, referralUse.ID)

		if err != nil {
			log.Printf("Error updating referral use: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update referral status"})
			return
		}

		// Create reward for the referrer
		rewardID := lib.GenerateULID()
		_, err = tx.Exec(`
			INSERT INTO referral_rewards (
				id, referral_use_id, user_id,
				reward_type, amount, status,
				created_at, modified_at
			)
			VALUES ($1, $2, $3, 'COMMISSION', $4, 'PENDING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`, rewardID, referralUse.ID, referralUse.ReferrerID, referralUse.ReferrerRewardAmount)

		if err != nil {
			log.Printf("Error creating reward: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create referral reward"})
			return
		}

		// Update referrer's balance
		reward := struct {
			UserID string
			Amount float64
			Type   string
		}{
			UserID: referralUse.ReferrerID,
			Amount: referralUse.ReferrerRewardAmount,
			Type:   "COMMISSION",
		}

		err = updateBalanceFromReferral(tx, reward)
		if err != nil {
			log.Printf("Error updating balance: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update referrer balance"})
			return
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete referral redemption"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message":              "Referral successfully redeemed",
			"referralUseId":        referralUse.ID,
			"rewardId":             rewardID,
			"referrerRewardAmount": referralUse.ReferrerRewardAmount,
			"cumulativeAmount":     cumulativeAmount,
		})
	}
}

func getReferralCodes(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		var codes []ReferralCode
		query := `
		WITH effective_configs AS (
		  SELECT 
			type,
			config,
			is_active,
			-- Rank configs: user-specific first, then defaults, both ordered by recency
			ROW_NUMBER() OVER (
			  PARTITION BY type
			  ORDER BY 
				CASE WHEN user_id = $1 THEN 1 ELSE 2 END,  -- Prioritize user-specific
				created_at DESC                            -- Then pick the latest
			) AS rn
		  FROM referral_configurations
		  WHERE (user_id = $1 OR user_id IS NULL)
			AND is_active = true
		),
		referral_metrics AS (
		  SELECT 
			rc.id, 
			rc.referrer_id, 
			rc.code, 
			rc.status,
			rc.expires_at, 
			rc.created_at, 
			rc.modified_at,
			COALESCE(SUM(
			  CASE 
				WHEN ru.type IN ('PURCHASE', 'SUBSCRIPTION', 'REGISTRATION') 
				  AND cfg.is_active = true 
				  THEN ru.referrer_reward_amount
				ELSE 0
			  END
			), 0) AS total_commission,
			-- Use MAX() to ensure config values are retained after GROUP BY
			MAX(CASE WHEN cfg_reg.type = 'REGISTRATION' THEN (cfg_reg.config->'referrer'->>'amount')::numeric ELSE 0 END) AS registration_commission,
			MAX(CASE WHEN cfg_pur.type = 'PURCHASE' THEN (cfg_pur.config->'referrer'->>'amount')::numeric ELSE 0 END) AS purchase_commission,
			MAX(CASE WHEN cfg_sub.type = 'SUBSCRIPTION' THEN (cfg_sub.config->'referrer'->>'amount')::numeric ELSE 0 END) AS subscription_commission
		  FROM referral_codes rc
		  -- Join effective configs for each type
		  LEFT JOIN effective_configs cfg_reg ON cfg_reg.type = 'REGISTRATION' AND cfg_reg.rn = 1
		  LEFT JOIN effective_configs cfg_pur ON cfg_pur.type = 'PURCHASE' AND cfg_pur.rn = 1
		  LEFT JOIN effective_configs cfg_sub ON cfg_sub.type = 'SUBSCRIPTION' AND cfg_sub.rn = 1
		  LEFT JOIN referral_uses ru ON rc.id = ru.referral_code_id
		  LEFT JOIN effective_configs cfg ON ru.type = cfg.type AND cfg.rn = 1
		  WHERE rc.referrer_id = $1
		  GROUP BY rc.id, rc.referrer_id, rc.code, rc.status, rc.expires_at, rc.created_at, rc.modified_at
		)
		SELECT * FROM referral_metrics
		ORDER BY created_at DESC;
		`
		if err := dbx.Select(&codes, query, userID); err != nil {
			log.Printf("Error fetching referral codes: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch referral codes"})
			return
		}
		if codes == nil {
			codes = []ReferralCode{}
		}
		c.JSON(http.StatusOK, gin.H{"codes": codes})
	}
}

func getReferralDetails(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		code := c.Param("code")
		showAll := c.DefaultQuery("showAll", "false") == "true"

		// 1. Base Query: Get total commission from static reward amounts, only counting redeemable REGISTRATION referrals
		var referral ReferralCodeDetails
		baseQuery := `
			WITH effective_configs AS (
				SELECT 
					type,
					config,
					ROW_NUMBER() OVER (
						PARTITION BY type
						ORDER BY 
							CASE WHEN user_id = $1 THEN 1 ELSE 2 END,  -- Prioritize user-specific
							created_at DESC
					) AS rn
				FROM referral_configurations
				WHERE (user_id = $1 OR user_id IS NULL)
					AND is_active = true
			),
			commission_total AS (
				SELECT 
					rc.id,
					COALESCE(SUM(
						CASE WHEN ru.type IN ('PURCHASE', 'SUBSCRIPTION') OR ru.is_redeemable IS TRUE OR $3 = true
						THEN ru.referrer_reward_amount ELSE 0 END
					), 0) as total_commission
				FROM referral_codes rc
				LEFT JOIN referral_uses ru ON rc.id = ru.referral_code_id
				LEFT JOIN effective_configs cfg ON ru.type = cfg.type AND cfg.rn = 1
				WHERE cfg.rn = 1  -- Ensure only prioritized configs
				GROUP BY rc.id
			)
			SELECT 
				rc.id,
				rc.referrer_id,
				rc.code,
				rc.status,
				rc.expires_at,
				rc.created_at,
				rc.modified_at,
				COALESCE(ct.total_commission, 0) as total_commission
			FROM referral_codes rc
			LEFT JOIN commission_total ct ON rc.id = ct.id
			WHERE rc.referrer_id = $1 AND rc.code = $2;
        `

		if err := dbx.Get(&referral, baseQuery, userID, code, showAll); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				c.JSON(http.StatusNotFound, gin.H{"error": "Referral code not found"})
				return
			}
			log.Printf("Error fetching referral details: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch referral details"})
			return
		}

		// 2. Usage Query: Get detailed usage with prioritized configurations, including is_redeemable status
		usageQuery := `
			WITH effective_configs AS (
				SELECT 
					type,
					config,
					ROW_NUMBER() OVER (
						PARTITION BY type
						ORDER BY 
							CASE WHEN user_id = $1 THEN 1 ELSE 2 END,
							created_at DESC
					) AS rn
				FROM referral_configurations
				WHERE (user_id = $1 OR user_id IS NULL)
					AND is_active = true
			)
			SELECT 
				ru.type,
				ru.referee_id,
				ru.payment_id,
				ru.is_redeemable,
				ru.referrer_reward_amount as reward_amount,  -- Use static value
				(cfg.config->'referrer'->>'amount')::numeric as commission_percent,
				ru.created_at
			FROM referral_uses ru
			LEFT JOIN effective_configs cfg ON ru.type = cfg.type AND cfg.rn = 1
			WHERE ru.referral_code_id = $2
			AND (ru.is_redeemable IS TRUE OR ru.type IN ('PURCHASE', 'SUBSCRIPTION') OR $3 = true)
			ORDER BY ru.created_at DESC;
        `

		if err := dbx.Select(&referral.UsageDetails, usageQuery, userID, referral.ID, showAll); err != nil {
			log.Printf("Error fetching usage details: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch referral usage details"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"referral": referral})
	}
}

// createReferralCode creates a new referral code
func createReferralCode(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		input := sanitizeRouteParams(c)

		var request CreateReferralRequest
		if err := c.BindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Add this line to sanitize the request body
		request.sanitize()

		// Validate code format if needed
		if len(request.Code) < 5 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Code must be at least 5 characters long"})
			return
		}

		// Check if code already exists
		var exists bool
		checkQuery := `SELECT EXISTS(SELECT 1 FROM referral_codes WHERE code = $1)`
		if err := dbx.Get(&exists, checkQuery, request.Code); err != nil {
			log.Printf("Error checking code existence: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate code"})
			return
		}

		if exists {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Code already exists"})
			return
		}

		var referral ReferralCode
		query := `
            INSERT INTO referral_codes (id, referrer_id, code, status, expires_at)
            VALUES ($1, $2, $3, 'ACTIVE', CURRENT_TIMESTAMP + INTERVAL '365 days')
            RETURNING id, referrer_id, code, status, expires_at, created_at, modified_at
        `

		id := lib.GenerateULID()
		err := dbx.Get(&referral, query, id, input.UserID, request.Code)
		if err != nil {
			log.Printf("Error creating referral code: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create referral code"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"referral": referral,
			"message":  "Referral code created successfully",
		})
	}
}

// getReferralAnalytics retrieves analytics for user's referrals
func getReferralAnalytics(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		showAll := c.DefaultQuery("showAll", "false") == "true"

		var analytics []ReferralAnalytics

		// Modified query to include is_redeemable and filter appropriately
		query := `
            WITH referral_metrics AS (
                SELECT 
                    rc.id as code_id,
                    COUNT(DISTINCT CASE WHEN ra.type = 'CLICK' THEN ra.id END) as clicks,
                    COUNT(DISTINCT CASE WHEN ra.type = 'IMPRESSION' THEN ra.id END) as impressions,
                    COUNT(DISTINCT CASE WHEN ru.type = 'REGISTRATION' AND (ru.is_redeemable IS TRUE OR $2 = true) THEN ru.id END) as registrations,
                    COUNT(DISTINCT CASE WHEN ru.type = 'PURCHASE' THEN ru.id END) as purchases,
                    COUNT(DISTINCT CASE WHEN ru.type = 'SUBSCRIPTION' THEN ru.id END) as subscriptions,
                    COALESCE(SUM(
                        CASE WHEN ru.is_redeemable IS TRUE OR ru.type IN ('PURCHASE', 'SUBSCRIPTION') OR $2 = true 
                        THEN rr.amount ELSE 0 END
                    ), 0) as total_commission
                FROM referral_codes rc
                LEFT JOIN referral_analytics ra ON rc.id = ra.referral_code_id
                LEFT JOIN referral_uses ru ON rc.id = ru.referral_code_id
                LEFT JOIN referral_rewards rr ON ru.id = rr.referral_use_id
                WHERE rc.referrer_id = $1
                GROUP BY rc.id
            )
            SELECT * FROM referral_metrics
        `

		if err := dbx.Select(&analytics, query, userID, showAll); err != nil {
			log.Printf("Error fetching referral analytics: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch referral analytics"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"analytics": analytics})
	}
}

// trackReferralVisit tracks clicks and impressions on referral links
func trackReferralVisit(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		input := sanitizeRouteParams(c)

		var request TrackReferralRequest
		if err := c.BindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// First, verify the referral code exists and is active
		var referralID string
		verifyQuery := `
            SELECT id FROM referral_codes 
            WHERE code = $1 AND status = 'ACTIVE' AND expires_at > CURRENT_TIMESTAMP
        `
		err := dbx.Get(&referralID, verifyQuery, input.Code)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Invalid or expired referral code"})
				return
			}
			log.Printf("Error verifying referral code: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify referral code"})
			return
		}

		// Track the visit
		query := `
            INSERT INTO referral_analytics (id, referral_code_id, type, ip_address, user_agent, referer_url)
            VALUES ($1, $2, $3, $4, $5, $6)
        `

		id := lib.GenerateULID()
		_, err = dbx.Exec(query, id, referralID, request.Type, request.IPAddress, request.UserAgent, request.RefererURL)
		if err != nil {
			log.Printf("Error tracking referral visit: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to track referral visit"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Referral visit tracked successfully"})
	}
}

// Update the ReferralValidation struct to include type-specific details
type ReferralValidation struct {
	IsValid              bool      `json:"isValid"`
	ReferrerID           string    `json:"referrerId,omitempty"`
	ExpiresAt            time.Time `json:"expiresAt,omitempty"`
	RefereeRewardAmount  float64   `json:"refereeRewardAmount,omitempty"`
	ErrorMessage         string    `json:"errorMessage,omitempty"`
	Type                 string    `json:"type,omitempty"`
	ReferrerRewardAmount float64   `json:"referrerRewardAmount,omitempty"`
}

// validateReferralCode checks if a referral code is valid
// validateReferralCode checks if a referral code is valid for a specific type
func validateReferralCode(dbx *sqlx.DB, code string, referralType string, currentUserID string) (*ReferralValidation, error) {
	sanitizedCode := strictSanitizer.Sanitize(code)
	sanitizedType := strictSanitizer.Sanitize(referralType)
	var validation ReferralValidation

	// Validate referral type
	if !isValidReferralType(sanitizedType) {
		validation.IsValid = false
		validation.ErrorMessage = "Invalid referral type. Must be REGISTRATION, PURCHASE, or SUBSCRIPTION"
		return &validation, nil
	}

	// Get referral code details and check if the current user is the owner
	query := `
		WITH effective_config AS (
			SELECT 
				(config->'referrer'->>'amount')::numeric as referrer_reward_amount,
				(config->'referee'->>'amount')::numeric as referee_reward_amount,
				ROW_NUMBER() OVER (
					ORDER BY 
						CASE WHEN user_id IS NOT NULL THEN 1 ELSE 2 END,
						created_at DESC
				) AS rn
			FROM referral_configurations
			WHERE 
				type = $1 
				AND is_active = true 
				AND (user_id = (SELECT referrer_id FROM referral_codes WHERE code = $2) OR user_id IS NULL)
		)
		SELECT 
			rc.referrer_id,
			rc.expires_at,
			rc.status,
			CASE 
				WHEN rc.referrer_id = $3 AND $1 IN ('PURCHASE', 'SUBSCRIPTION') THEN 0  -- Zero for self-use in purchase/subscription
				ELSE ec.referee_reward_amount 
			END as referee_reward_amount,
			CASE 
				WHEN rc.referrer_id = $3 AND $1 IN ('PURCHASE', 'SUBSCRIPTION') THEN 0  -- Zero for self-use in purchase/subscription
				ELSE ec.referrer_reward_amount 
			END as referrer_reward_amount,
			CASE 
				WHEN rc.referrer_id = $3 AND $1 IN ('PURCHASE', 'SUBSCRIPTION') THEN 'You cannot use your own referral code'
				WHEN rc.status != 'ACTIVE' THEN 'Referral code is not active'
				WHEN rc.expires_at <= CURRENT_TIMESTAMP THEN 'Referral code has expired'
				WHEN ec.referee_reward_amount IS NULL THEN 'No active configuration for this referral type'
				ELSE ''
			END as error_message
		FROM referral_codes rc
		LEFT JOIN effective_config ec ON ec.rn = 1
		WHERE rc.code = $2
    `

	type result struct {
		ReferrerID     string    `db:"referrer_id"`
		ExpiresAt      time.Time `db:"expires_at"`
		Status         string    `db:"status"`
		RefereeReward  float64   `db:"referee_reward_amount"`
		ReferrerReward float64   `db:"referrer_reward_amount"`
		ErrorMessage   string    `db:"error_message"`
	}

	var r result
	err := dbx.Get(&r, query, sanitizedType, sanitizedCode, currentUserID)
	if err != nil {
		if err == sql.ErrNoRows {
			validation.IsValid = false
			validation.ErrorMessage = "Invalid referral code"
			return &validation, nil
		}
		return nil, fmt.Errorf("database error: %v", err)
	}

	validation.ReferrerID = r.ReferrerID
	validation.ExpiresAt = r.ExpiresAt
	validation.RefereeRewardAmount = r.RefereeReward
	validation.ReferrerRewardAmount = r.ReferrerReward
	validation.ErrorMessage = r.ErrorMessage
	validation.Type = sanitizedType
	validation.IsValid = r.ErrorMessage == ""

	return &validation, nil
}

func isValidReferralType(referralType string) bool {
	switch referralType {
	case "REGISTRATION", "PURCHASE", "SUBSCRIPTION":
		return true
	default:
		return false
	}
}

// checkDuplicateReferral checks if a user has already used a referral code
func checkDuplicateReferral(dbx *sqlx.DB, refereeID string,
	referralCodeID string) (bool, error) {
	sanitizedRefereeID := strictSanitizer.Sanitize(refereeID)
	sanitizedReferralCodeID := strictSanitizer.Sanitize(referralCodeID)
	var count int
	query := `
        SELECT COUNT(*) 
        FROM referral_uses 
        WHERE referee_id = $1 AND referral_code_id = $2
    `

	err := dbx.Get(&count, query, sanitizedRefereeID, sanitizedReferralCodeID)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// calculateRewards calculates rewards based on referral type and configuration
func calculateRewards(dbx *sqlx.DB, userID string, referralType string) (float64, float64, error) {
	var config ReferralConfiguration

	// First try to get user-specific configuration
	query := `
		SELECT * FROM referral_configurations
		WHERE type = $1 AND is_active = true
		AND (user_id = $2 OR user_id IS NULL)
		ORDER BY 
			CASE WHEN user_id = $2 THEN 1 ELSE 2 END,  -- User-specific first
			created_at DESC
		LIMIT 1
    `

	err := dbx.Get(&config, query, referralType, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get configuration: %v", err)
	}

	// Parse the configuration
	var configData struct {
		Referrer struct {
			Type      string   `json:"type"`
			Amount    float64  `json:"amount"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referrer"`
		Referee struct {
			Type      string   `json:"type"`
			Amount    float64  `json:"amount"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referee"`
	}

	if err := json.Unmarshal(config.Config, &configData); err != nil {
		return 0, 0, fmt.Errorf("invalid configuration format: %v", err)
	}

	return configData.Referrer.Amount, configData.Referee.Amount, nil
}

// updateReferralStatus updates the status of a referral code
func updateReferralStatus(dbx *sqlx.DB, codeID string,
	status string) error {
	query := `
        UPDATE referral_codes 
        SET 
            status = $1,
            modified_at = CURRENT_TIMESTAMP
        WHERE id = $2
    `

	result, err := dbx.Exec(query, status, codeID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return sql.ErrNoRows
	}

	return nil
}

// createUserConfig creates a new user-specific configuration
func createUserConfig(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		var req ConfigRequest

		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Validate referral type
		if !isValidReferralType(req.Type) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid referral type",
			})
			return
		}

		// Convert config to JSON
		configJSON, err := json.Marshal(req.Config)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process configuration"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// Check for existing config
		var exists bool
		err = tx.Get(&exists, `
            SELECT EXISTS(
                SELECT 1 FROM referral_configurations 
                WHERE user_id = $1 AND type = $2
            )
        `, userID, req.Type)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check existing configuration"})
			return
		}

		if exists {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Configuration already exists for this type"})
			return
		}

		// Insert new configuration
		id := lib.GenerateULID()
		_, err = tx.Exec(`
            INSERT INTO referral_configurations (
                id, user_id, type, config, is_active
            ) VALUES ($1, $2, $3, $4::jsonb, $5)
        `, id, userID, req.Type, configJSON, req.IsActive)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create configuration"})
			return
		}

		if err = tx.Commit(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save configuration"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Configuration created successfully",
			"id":      id,
		})
	}
}

// getUserConfigs retrieves configurations for a user, falling back to global configs if none exist
func getUserConfigs(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		var configs []ReferralConfiguration
		var err error

		// First try to get user-specific configurations
		userQuery := `
            SELECT * FROM referral_configurations
            WHERE user_id = $1
            ORDER BY type, created_at DESC
        `

		if err = dbx.Select(&configs, userQuery, userID); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch configurations"})
			return
		}

		// If no user-specific configs found, fallback to global configurations
		if len(configs) == 0 {
			fallbackQuery := `
                SELECT * FROM referral_configurations
                WHERE user_id IS NULL
                ORDER BY type, created_at DESC
            `

			if err = dbx.Select(&configs, fallbackQuery); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch fallback configurations"})
				return
			}

			// If no global configs found either
			if len(configs) == 0 {
				c.JSON(http.StatusNotFound, gin.H{"error": "No configurations found"})
				return
			}
		}

		c.JSON(http.StatusOK, gin.H{"configurations": configs})
	}
}

// getConfigByType retrieves a specific configuration type for a user
func getConfigByType(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		configType := c.Param("type")

		var config ReferralConfiguration
		query := `
            SELECT * FROM referral_configurations
            WHERE user_id = $1 AND type = $2 AND is_active = true
            ORDER BY created_at DESC
            LIMIT 1
        `

		err := dbx.Get(&config, query, userID, configType)
		if err != nil {
			if err == sql.ErrNoRows {
				// If no user-specific config exists, get system default
				err = dbx.Get(&config, `
                    SELECT * FROM referral_configurations
                    WHERE user_id IS NULL AND type = $1 AND is_active = true
                    ORDER BY created_at DESC
                    LIMIT 1
                `, configType)
				if err != nil {
					if err == sql.ErrNoRows {
						c.JSON(http.StatusNotFound, gin.H{"error": "No configuration found"})
						return
					}
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch configuration"})
					return
				}
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch configuration"})
				return
			}
		}

		c.JSON(http.StatusOK, gin.H{"configuration": config})
	}
}

// updateUserConfig updates a user's configuration
func updateUserConfig(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		configType := c.Param("type")

		var req ConfigRequest
		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		if req.Type != configType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Configuration type mismatch"})
			return
		}

		configJSON, err := json.Marshal(req.Config)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process configuration"})
			return
		}

		result, err := dbx.Exec(`
            UPDATE referral_configurations
            SET config = $1::jsonb, is_active = $2, modified_at = CURRENT_TIMESTAMP
            WHERE user_id = $3 AND type = $4
        `, configJSON, req.IsActive, userID, configType)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update configuration"})
			return
		}

		rows, err := result.RowsAffected()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm update"})
			return
		}

		if rows == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Configuration not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Configuration updated successfully"})
	}
}

// deleteUserConfig deletes a user's configuration
func deleteUserConfig(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		configType := c.Param("type")

		result, err := dbx.Exec(`
            DELETE FROM referral_configurations
            WHERE user_id = $1 AND type = $2
        `, userID, configType)

		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete configuration"})
			return
		}

		rows, err := result.RowsAffected()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm deletion"})
			return
		}

		if rows == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Configuration not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Configuration deleted successfully"})
	}
}

// getCurrentConfig gets the active configuration for a type
func getCurrentConfig(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		referralType := c.Param("type")
		userID := c.Query("userId")

		var config ReferralConfiguration
		query := `
            SELECT * FROM referral_configurations
            WHERE type = $1 AND is_active = true
            AND (user_id = $2 OR user_id IS NULL)
            ORDER BY user_id NULLS LAST, created_at DESC
            LIMIT 1
        `

		err := dbx.Get(&config, query, referralType, userID)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "No active configuration found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch configuration"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"configuration": config})
	}
}

// Helper function to get configuration and calculate rewards
func getConfigAndCalculateRewards(tx *sqlx.Tx, userID string, referralType string, transactionAmount *float64) (float64, float64, error) {
	var config ReferralConfiguration

	query := `
		SELECT * FROM referral_configurations
		WHERE type = $1 AND is_active = true
		AND (user_id = $2 OR user_id IS NULL)
		ORDER BY 
			CASE WHEN user_id = $2 THEN 1 ELSE 2 END,  -- User-specific first
			created_at DESC
		LIMIT 1
    `

	err := tx.Get(&config, query, referralType, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get configuration: %v", err)
	}

	var configData struct {
		Referrer struct {
			Type      string   `json:"type"`
			Amount    float64  `json:"amount"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referrer"`
		Referee struct {
			Type      string   `json:"type"`
			Amount    float64  `json:"amount"`
			MinAmount *float64 `json:"minAmount,omitempty"`
			MaxAmount *float64 `json:"maxAmount,omitempty"`
		} `json:"referee"`
	}

	if err := json.Unmarshal(config.Config, &configData); err != nil {
		return 0, 0, fmt.Errorf("invalid configuration format: %v", err)
	}

	var referrerAmount, refereeAmount float64

	// Calculate referrer reward
	if configData.Referrer.Type == "PERCENTAGE" && transactionAmount != nil {
		referrerAmount = *transactionAmount * (configData.Referrer.Amount / 100.0)
	} else {
		referrerAmount = configData.Referrer.Amount
	}

	// Calculate referee reward
	if configData.Referee.Type == "PERCENTAGE" && transactionAmount != nil {
		refereeAmount = *transactionAmount * (configData.Referee.Amount / 100.0)
	} else {
		refereeAmount = configData.Referee.Amount
	}

	// Apply min/max bounds for referrer
	if configData.Referrer.MinAmount != nil && referrerAmount < *configData.Referrer.MinAmount {
		referrerAmount = *configData.Referrer.MinAmount
	}
	if configData.Referrer.MaxAmount != nil && referrerAmount > *configData.Referrer.MaxAmount {
		referrerAmount = *configData.Referrer.MaxAmount
	}

	// Apply min/max bounds for referee
	if configData.Referee.MinAmount != nil && refereeAmount < *configData.Referee.MinAmount {
		refereeAmount = *configData.Referee.MinAmount
	}
	if configData.Referee.MaxAmount != nil && refereeAmount > *configData.Referee.MaxAmount {
		refereeAmount = *configData.Referee.MaxAmount
	}

	return referrerAmount, refereeAmount, nil
}
