name: Build & Deploy PROD

on:
  workflow_dispatch:
  # push:
  #   branches: [ main ]
  # pull_request:
  #   branches: [ main ]

env:
  PROJECT_ID: ************
  PROJECT_NAME: terang-ai
  GCR_REGION: asia-southeast2
  IMAGE_NAME: lk-agent
  GKE_CLUSTER: terang-ai-autopilot-cluster
  GKE_ZONE: asia-southeast2

jobs:
  build-and-push-deploy:
    runs-on: ubicloud-standard-16
    
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
    - uses: actions/checkout@v4
    
    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v1'
      with:
        workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/pool/providers/pool'
        service_account: '<EMAIL>'

    - id: fetch-pat-secret
      name: Fetch GSM Secrets
      uses: 'google-github-actions/get-secretmanager-secrets@v2'
      with:
        min_mask_length: '4'
        secrets: |-
          prod-env:${{ env.PROJECT_NAME }}/lk-agent-prod-env

    - name: Create .env file
      run: |
        echo "${{ steps.fetch-pat-secret.outputs.prod-env }}" > src/.env

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v1'

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    # Configure Docker authentication BEFORE using build-push-action
    - name: Configure Docker to use gcloud as a credential helper
      run: |
        gcloud auth configure-docker asia-southeast2-docker.pkg.dev

    # Set up Docker Buildx AFTER authentication
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    # Build and push using Docker Buildx with caching
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./src
        file: ./src/Dockerfile
        push: true
        tags: asia-southeast2-docker.pkg.dev/${{ env.PROJECT_NAME }}/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}-prod
        cache-from: type=gha
        cache-to: type=gha,mode=max
        compression: zstd
        platforms: linux/amd64
        provenance: false
        build-args: |
          BUILDKIT_INLINE_CACHE=1

    - name: 'Install GKE Auth Plugin'
      run: |
        gcloud components install gke-gcloud-auth-plugin
        echo "CLOUDSDK_AUTH_PLUGIN_GKE_GCLOUD_AUTH_PLUGIN=true" >> $GITHUB_ENV

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --region ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_NAME }}

    - name: Deploy to GKE
      run: |
        kubectl set image deployment/lk-agent lk-agent=asia-southeast2-docker.pkg.dev/${{ env.PROJECT_NAME }}/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}-prod -n prod
        kubectl rollout status deployment/lk-agent -n prod