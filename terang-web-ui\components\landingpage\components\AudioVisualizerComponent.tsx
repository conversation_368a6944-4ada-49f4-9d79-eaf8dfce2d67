// Fixed Audio Visualizer with proper styled-components usage

import React, { useEffect, useRef, useState } from 'react';
import styled, { keyframes, css } from 'styled-components';

// Smoother wave animation with more natural movement
const smoothWave = keyframes`
  0% { transform: scaleY(0.3); }
  25% { transform: scaleY(0.8); }
  50% { transform: scaleY(1); }
  75% { transform: scaleY(0.6); }
  100% { transform: scaleY(0.3); }
`;

const AudioVisualizer = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isActive'].includes(prop),
})<{ isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 60px;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(168, 85, 247, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  
  ${props => props.isActive && css`
    background: rgba(168, 85, 247, 0.2);
    box-shadow: 0 4px 20px rgba(168, 85, 247, 0.3);
  `}
`;

const AudioBar = styled.div.withConfig({
  shouldForwardProp: (prop) => !['height', 'delay', 'isActive', 'duration'].includes(prop),
})<{ 
  height: number; 
  delay: number; 
  isActive: boolean;
  duration: number;
}>`
  width: 4px;
  background: linear-gradient(135deg, #A855F7 0%, #7C3AED 100%);
  border-radius: 2px;
  transform-origin: bottom;
  transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  ${props => props.isActive ? css`
    animation: ${smoothWave} ${props.duration}s ease-in-out infinite;
    animation-delay: ${props.delay}s;
    height: ${Math.max(4, props.height)}px;
  ` : css`
    height: 4px;
    animation: none;
  `}
`;

interface AudioVisualizerComponentProps {
  isActive: boolean;
  barCount?: number;
  intensity?: 'low' | 'medium' | 'high';
}

const AudioVisualizerComponent: React.FC<AudioVisualizerComponentProps> = ({ 
  isActive, 
  barCount = 12,
  intensity = 'medium' 
}) => {
  const [audioLevels, setAudioLevels] = useState<number[]>([]);
  const [barAnimations, setBarAnimations] = useState<Array<{
    delay: number;
    duration: number;
    targetHeight: number;
  }>>([]);
  const animationFrameRef = useRef<number>(0);
  const lastUpdateTime = useRef<number>(0);

  // Initialize bars with random properties
  useEffect(() => {
    const initialLevels = Array.from({ length: barCount }, () => 4);
    const initialAnimations = Array.from({ length: barCount }, (_, i) => ({
      delay: (i * 0.08) + (Math.random() * 0.1), // More varied delays
      duration: 0.8 + (Math.random() * 0.6), // Random duration between 0.8-1.4s
      targetHeight: 4
    }));
    
    setAudioLevels(initialLevels);
    setBarAnimations(initialAnimations);
  }, [barCount]);

  // Smooth animation loop using requestAnimationFrame
  useEffect(() => {
    const animate = (currentTime: number) => {
      // Throttle updates to ~60fps but with some variation
      if (currentTime - lastUpdateTime.current > 16 + Math.random() * 8) {
        lastUpdateTime.current = currentTime;
        
        if (isActive) {
          setAudioLevels(prev => 
            prev.map((currentHeight, index) => {
              const maxHeight = intensity === 'high' ? 35 : intensity === 'medium' ? 28 : 20;
              const minHeight = 4;
              
              // More natural randomization with some correlation to previous values
              const randomFactor = Math.sin(currentTime * 0.003 + index * 0.5) * 0.3 + 0.7;
              const noise = (Math.random() - 0.5) * 0.4;
              const baseHeight = (Math.random() * (maxHeight - minHeight) + minHeight) * randomFactor;
              const targetHeight = Math.max(minHeight, Math.min(maxHeight, baseHeight + noise * 10));
              
              // Smooth interpolation with variable smoothing per bar
              const smoothing = 0.15 + (index % 3) * 0.05; // Vary smoothing between bars
              return currentHeight + (targetHeight - currentHeight) * smoothing;
            })
          );

          // Occasionally update animation properties for more variation
          if (Math.random() < 0.02) { // 2% chance per frame
            setBarAnimations(prev => 
              prev.map((anim, i) => ({
                ...anim,
                duration: 0.8 + (Math.random() * 0.6),
                delay: (i * 0.08) + (Math.random() * 0.1)
              }))
            );
          }
        } else {
          // Smooth return to idle state
          setAudioLevels(prev => 
            prev.map(level => {
              const smoothing = 0.08;
              return Math.max(4, level + (4 - level) * smoothing);
            })
          );
        }
      }
      
      if (isActive || audioLevels.some(level => level > 4.1)) {
        animationFrameRef.current = requestAnimationFrame(animate);
      }
    };

    if (isActive || audioLevels.some(level => level > 4.1)) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isActive, intensity, audioLevels]);

  return (
    <AudioVisualizer isActive={isActive}>
      {audioLevels.map((height, i) => (
        <AudioBar
          key={i}
          height={height}
          delay={barAnimations[i]?.delay || i * 0.1}
          duration={barAnimations[i]?.duration || 1}
          isActive={isActive}
        />
      ))}
    </AudioVisualizer>
  );
};

export default AudioVisualizerComponent;