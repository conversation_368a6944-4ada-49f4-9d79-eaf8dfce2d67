// components/sections/AIInterviewSection.tsx
import React, { useState, useEffect } from 'react';
import { Mic, Headphones, Brain, MessageCircle, Video, BarChart3, Users, Sparkles, ArrowRight, CheckCircle, Zap, Target } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChatMessage {
  id: number;
  sender: 'interviewer' | 'applicant';
  message: string;
  timestamp: string;
}

interface MockConversationItem {
  sender: 'interviewer' | 'applicant';
  message: string;
}

const AIInterviewSection: React.FC = () => {
    const router = useRouter();
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState<number>(0);
  const [isListening, setIsListening] = useState<boolean>(false);
  const [audioLevels, setAudioLevels] = useState<number[]>([0, 0, 0, 0, 0, 0, 0, 0]);
  const [currentSpeaker, setCurrentSpeaker] = useState<'interviewer' | 'applicant' | null>(null);

  const mockConversation: MockConversationItem[] = [
    { sender: 'interviewer', message: 'Selamat pagi! Saya Professor Terra, pewawancara AI untuk tes substansi LPDP hari ini. Bagaimana kabarnya?' },
    { sender: 'applicant', message: 'Selamat pagi Professor Terra! Kabar saya baik, terima kasih. Saya sangat antusias untuk mengikuti tes substansi ini.' },
    { sender: 'interviewer', message: 'Bagus! Mari kita mulai dengan perkenalan. Bisakan kamu ceritakan sedikit tentang latar belakang pendidikan dan motivasimu untuk mengambil beasiswa LPDP?' },
    { sender: 'applicant', message: 'Tentu! Saya lulusan Teknik Informatika dari Universitas Indonesia. Motivasi saya mengambil LPDP adalah untuk mendalami AI dan Machine Learning di luar negeri...' },
    { sender: 'interviewer', message: 'Menarik! Mengapa kamu memilih bidang AI dan bagaimana kamu melihat kontribusi AI untuk Indonesia ke depannya?' },
    { sender: 'applicant', message: 'AI memiliki potensi besar untuk memajukan Indonesia, terutama dalam sektor pendidikan, kesehatan, dan ekonomi digital...' }
  ];

  // Audio visualization effect
  useEffect(() => {
    const audioInterval = setInterval(() => {
      if (currentSpeaker) {
        setAudioLevels(prev => prev.map(() => Math.random() * 100));
      } else {
        setAudioLevels(prev => prev.map(level => Math.max(0, level - 10)));
      }
    }, 100);

    return () => clearInterval(audioInterval);
  }, [currentSpeaker]);

  // Chat animation effect
  useEffect(() => {
    const chatInterval = setInterval(() => {
      if (currentMessageIndex < mockConversation.length) {
        const currentConversationItem = mockConversation[currentMessageIndex];
        const newMessage: ChatMessage = {
          id: currentMessageIndex,
          sender: currentConversationItem.sender,
          message: currentConversationItem.message,
          timestamp: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' })
        };
        
        // Set current speaker for audio visualization
        setCurrentSpeaker(newMessage.sender);
        setIsListening(newMessage.sender === 'interviewer');
        
        setChatMessages(prev => [...prev, newMessage]);
        setCurrentMessageIndex(prev => prev + 1);
        
        // Stop speaking after message duration
        setTimeout(() => {
          setCurrentSpeaker(null);
          setIsListening(newMessage.sender === 'applicant');
        }, 2000);
        
        // Auto scroll to bottom when new message is added
        setTimeout(() => {
          const chatContainer = document.querySelector('.chat-messages-container');
          if (chatContainer) {
            chatContainer.scrollTop = chatContainer.scrollHeight;
          }
        }, 100);
      } else {
        // Reset after showing all messages
        setTimeout(() => {
          setChatMessages([]);
          setCurrentMessageIndex(0);
          setCurrentSpeaker(null);
          setIsListening(false);
        }, 3000);
      }
    }, 3500);

    return () => clearInterval(chatInterval);
  }, [currentMessageIndex, mockConversation.length]);

  const aiFeatures = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Advanced Natural Language Processing",
      description: "AI yang memahami konteks percakapan dan memberikan pertanyaan follow-up yang relevan",
      bgColor: "from-purple-100 to-indigo-100",
      borderColor: "border-purple-200"
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      title: "Contextual Interview Flow",
      description: "Pertanyaan yang dinamis berdasarkan jawaban sebelumnya, seperti pewawancara sesungguhnya",
      bgColor: "from-blue-100 to-cyan-100",
      borderColor: "border-blue-200"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Real-time Performance Analysis",
      description: "Analisis mendalam terhadap confidence, clarity, dan relevansi jawaban secara real-time",
      bgColor: "from-green-100 to-emerald-100",
      borderColor: "border-green-200"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Personalized Learning Path",
      description: "Rekomendasi pembelajaran yang disesuaikan dengan area yang perlu diperbaiki",
      bgColor: "from-orange-100 to-red-100",
      borderColor: "border-orange-200"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse animation-delay-2000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full text-indigo-800 text-sm font-medium mb-8 border border-indigo-200">
            <Brain className="w-5 h-5 mr-2" />
            Revolutionary AI Technology
            <Sparkles className="w-5 h-5 ml-2" />
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-8">
            Meet Professor Terra
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600">
              Your AI Interview Coach
            </span>
          </h2>
          
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            AI interviewer yang dapat melakukan percakapan natural seperti manusia, dengan kemampuan analisis mendalam dan feedback yang konstruktif
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Live Demo Interface */}
          <div className="relative">
            <div className="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 text-white shadow-2xl transform hover:scale-105 transition-all animate-gradient-xy relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              
              <div className="relative z-10 space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm flex-shrink-0">
                    <Headphones className="w-6 h-6 animate-pulse" />
                  </div>
                  <span className="text-lg md:text-xl font-bold truncate">Live AI Interview Demo</span>
                </div>
                
                {/* Voice Interface */}
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 h-96 flex flex-col">
                  {/* Header */}
                  <div className="flex items-center justify-between p-4 border-b border-white/20">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="font-semibold text-sm">LPDP Interview Session</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm opacity-80">LIVE</span>
                    </div>
                  </div>

                  {/* Chat Messages */}
                  <div className="flex-1 p-4 space-y-3 overflow-y-auto chat-messages-container min-h-0">
                    {chatMessages.map((message, index) => (
                      <div 
                        key={message.id} 
                        className={`flex ${message.sender === 'interviewer' ? 'justify-start' : 'justify-end'} animate-slide-up`}
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <div className={`max-w-[85%] p-3 rounded-xl text-sm ${
                          message.sender === 'interviewer' 
                            ? 'bg-white/90 text-gray-800 rounded-tl-none' 
                            : 'bg-blue-500/90 text-white rounded-tr-none'
                        }`}>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-semibold text-xs">
                              {message.sender === 'interviewer' ? '🤖 Professor Terra' : '👤 You'}
                            </span>
                            <span className="text-xs opacity-70">{message.timestamp}</span>
                          </div>
                          <p className="leading-relaxed">{message.message}</p>
                        </div>
                      </div>
                    ))}
                    
                    {currentSpeaker && (
                      <div className={`flex ${currentSpeaker === 'interviewer' ? 'justify-start' : 'justify-end'}`}>
                        <div className={`p-3 rounded-xl ${
                          currentSpeaker === 'interviewer' 
                            ? 'bg-white/90 text-gray-800' 
                            : 'bg-blue-500/90 text-white'
                        }`}>
                          <div className="flex items-center space-x-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce animation-delay-200"></div>
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce animation-delay-400"></div>
                            </div>
                            <span className="text-xs">
                              {currentSpeaker === 'interviewer' ? 'Professor Terra is typing...' : 'You are speaking...'}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Controls */}
                  <div className="p-4 border-t border-white/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full transition-all ${
                          isListening ? 'bg-red-500 animate-pulse' : 'bg-white/20'
                        }`}>
                          <Mic className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex items-center space-x-1">
                          {audioLevels.map((level, index) => (
                            <div
                              key={index}
                              className={`w-1 bg-gradient-to-t from-green-400 to-blue-400 rounded-full transition-all duration-150 ${
                                currentSpeaker ? 'animate-pulse' : ''
                              }`}
                              style={{
                                height: `${Math.max(4, level * 0.2)}px`,
                                animationDelay: `${index * 50}ms`
                              }}
                            />
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="text-sm font-mono bg-white/20 px-3 py-1 rounded">
                          {String(Math.floor(chatMessages.length * 2.5)).padStart(2, '0')}:{String((chatMessages.length * 15) % 60).padStart(2, '0')}
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span className="text-sm">Active</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content Description */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900">
                Teknologi AI Terdepan untuk Simulasi Wawancara
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Professor Terra menggunakan Natural Language Processing dan Machine Learning canggih untuk menciptakan pengalaman wawancara yang benar-benar interaktif dan adaptif.
              </p>
            </div>

            {/* Key Benefits */}
            <div className="space-y-4">
              {[
                "🎯 Pertanyaan yang dinamis dan kontekstual",
                "🧠 Analisis real-time dari jawaban kandidat", 
                "📊 Feedback konstruktif dengan scoring detail",
                "🔄 Pembelajaran berkelanjutan dari setiap sesi"
              ].map((benefit, index) => (
                <div 
                  key={index} 
                  className="flex items-center space-x-3 p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 hover:shadow-lg transition-all"
                >
                  <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0" />
                  <span className="text-gray-800 font-medium">{benefit}</span>
                </div>
              ))}
            </div>

            <div className="pt-6">
              <button 
              onClick={() => router.push('/available-interviews')}
              className="group bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-xl flex items-center">
                <Zap className="w-6 h-6 mr-3 group-hover:animate-pulse" />
                Experience AI Interview Now
                <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" />
              </button>
            </div>
          </div>
        </div>

        {/* AI Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {aiFeatures.map((feature, index) => (
            <div 
              key={index} 
              className={`bg-gradient-to-br ${feature.bgColor} rounded-2xl p-6 border ${feature.borderColor} hover:shadow-xl transition-all transform hover:scale-105 group`}
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="text-indigo-600 mb-4 group-hover:scale-110 transition-transform">
                {feature.icon}
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-3">
                {feature.title}
              </h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AIInterviewSection;