package payments

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	payments "github.com/terang-ai/backend-service/controller/payments"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	PaymentCtrl := payments.NewPaymentController(db, redis)

	PaymentBaseGroup := r.Group("/v1")
	{
		PaymentBaseGroup.GET("/payments", PaymentCtrl.GetAllPayments)
		PaymentBaseGroup.POST("/payments", PaymentCtrl.InsertPayment)
	}

	paymentsGroup := r.Group("/v1/payments/:id")
	{
		// payments routes
		paymentsGroup.GET("", PaymentCtrl.GetOnePayment)
		paymentsGroup.PUT("", PaymentCtrl.UpdatePayment)
		paymentsGroup.DELETE("", PaymentCtrl.DeletePayment)
	}

}
