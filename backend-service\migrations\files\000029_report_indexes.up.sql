-- up.sql: Add indexes to improve query performance for time management reports

-- Index for filtering exam sessions by user and status, with start_time for sorting
CREATE INDEX IF NOT EXISTS idx_exam_sessions_user_status_time ON exam_sessions(user_id, status, start_time DESC);

-- Index for quickly looking up session data
CREATE INDEX IF NOT EXISTS idx_exam_sessions_session_id ON exam_sessions(session_id);

-- Index for joining exam_gamification with exam_sessions
CREATE INDEX IF NOT EXISTS idx_exam_gamification_session_id ON exam_gamification(exam_session_id);

-- Index for quick lookup of exam question data
CREATE INDEX IF NOT EXISTS idx_exam_question_answer_hints_exam_id ON exam_question_answer_hints(exam_id);

-- Index for jsonb access patterns in answers
CREATE INDEX IF NOT EXISTS idx_exam_sessions_answers_gin ON exam_sessions USING gin(answers);

-- Index for jsonb access patterns in question_times
CREATE INDEX IF NOT EXISTS idx_exam_gamification_question_times_gin ON exam_gamification USING gin(question_times);

-- Add comment to track migration
COMMENT ON INDEX idx_exam_sessions_user_status_time IS 'Added to improve performance of time management report queries';