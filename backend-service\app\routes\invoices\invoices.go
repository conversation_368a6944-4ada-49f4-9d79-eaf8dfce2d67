package invoices

import (
	"database/sql"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	invoices "github.com/terang-ai/backend-service/controller/invoices"
)

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	InvoiceCtrl := invoices.NewInvoiceController(db, redis)

	InvoiceBaseGroup := r.Group("/v1")
	{
		InvoiceBaseGroup.GET("/invoices", InvoiceCtrl.GetAllInvoices)
		InvoiceBaseGroup.POST("/invoices", InvoiceCtrl.InsertInvoice)
	}

	invoicesGroup := r.Group("/v1/invoices/:id")
	{
		// invoices routes
		invoicesGroup.GET("", InvoiceCtrl.GetOneInvoice)
		invoicesGroup.PUT("", InvoiceCtrl.UpdateInvoice)
		invoicesGroup.DELETE("", InvoiceCtrl.DeleteInvoice)
	}

}
