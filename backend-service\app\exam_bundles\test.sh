# CREATE
curl -X POST "https://prod-backend.terang.ai/v0/exam-bundles/01JBVY7BVY81YM4KWCVKWVFPCC" \
     -H "x-api-key: cassieinkl" \
     -H "Content-Type: application/json" \
     -d '{
  "name": "Paket Bundling LPDP TBS",
  "description": "Simulasi Tes Bakat Skolastik LPDP Latihan soal TBS terbaru",
  "price": 30000,
  "discountPercentage": 25,
  "visibility": "PUBLIC",
  "examIds": [
    "01JR7TRN1YGDA21WA339CHZEJ1",
    "01JRMTT53V692BPFS35188MQS1",
    "01JRMTT5J1H667ZTY6G8DVEW36"
  ]
}'

# LIST ALL
curl -X GET "http://localhost:8081/v0/exam-bundles" \
  -H "x-api-key: cassieinkl" \
  -H "Content-Type: application/json"

# LIST SPECIFIC BUNDLE ID
curl -X GET "http://localhost:8081/v0/exam-bundles/01JPM3M21MZFS3TC8Q444KNJ7R" \
  -H "x-api-key: cassieinkl" \
  -H "Content-Type: application/json"

# UPDATE
curl -X PUT "http://localhost:8081/v0/exam-bundles/YOUR_USER_ID/BUNDLE_ID_HERE" \
-H "x-api-key: cassieinkl" \
-H "Content-Type: application/json" \
-d '{
  "name": "Updated Exam Bundle",
  "description": "Updated description",
  "price": 249000,
  "discountPercentage": 15,
  "examIds": [
    "EXAM_ID_1",
    "EXAM_ID_2",
    "EXAM_ID_3"
  ]
}'

# DELETE
curl -X DELETE "http://localhost:8081/v0/exam-bundles/YOUR_USER_ID/BUNDLE_ID_HERE" \
  -H "x-api-key: cassieinkl" \
  -H "Content-Type: application/json"

# ADD EXAM TO EXISTING BUNDLE
curl -X POST "http://localhost:8081/v0/exam-bundles/YOUR_USER_ID/BUNDLE_ID_HERE/exams" \
  -H "x-api-key: cassieinkl" \
  -H "Content-Type: application/json" \
  -d '{
    "examId": "EXAM_ID_TO_ADD"
  }'

# REMOVE EXAM TO EXISTING BUNDLE
curl -X DELETE "http://localhost:8081/v0/exam-bundles/YOUR_USER_ID/BUNDLE_ID_HERE/exams/EXAM_ID_TO_REMOVE" \
  -H "x-api-key: cassieinkl" \
  -H "Content-Type: application/json"