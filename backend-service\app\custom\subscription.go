package custom

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	"github.com/terang-ai/backend-service/lib" // ULID generation package
)

// SubscriptionTier represents a subscription tier
type SubscriptionTier struct {
	ID                 string    `db:"id" json:"id"`
	Name               string    `db:"name" json:"name"`
	PriceMonthly       float64   `db:"price_monthly" json:"priceMonthly"`
	OriginalPrice      *float64  `db:"original_price,omitempty" json:"originalPrice,omitempty"`
	DiscountPercentage *int      `db:"discount_percentage,omitempty" json:"discountPercentage,omitempty"`
	Features           string    `db:"features" json:"features"`
	IsActive           bool      `db:"is_active" json:"isActive"`
	CreatedAt          time.Time `db:"created_at" json:"createdAt"`
	ModifiedAt         time.Time `db:"modified_at" json:"modifiedAt"`
	BillingInterval    string    `db:"billing_interval" json:"billingInterval"` // New field for billing interval
}

// UserSubscription represents a user's subscription
type UserSubscription struct {
	ID              string     `db:"id" json:"id"`
	UserID          string     `db:"user_id" json:"userId"`
	TierID          string     `db:"tier_id" json:"tierId"`
	StartDate       time.Time  `db:"start_date" json:"startDate"`
	EndDate         *time.Time `db:"end_date,omitempty" json:"endDate,omitempty"`
	IsActive        bool       `db:"is_active" json:"isActive"`
	AutoRenewal     bool       `db:"auto_renewal" json:"autoRenewal"`
	PaymentStatus   string     `db:"payment_status" json:"paymentStatus"`
	LastPaymentDate *time.Time `db:"last_payment_date,omitempty" json:"lastPaymentDate,omitempty"`
	NextPaymentDate *time.Time `db:"next_payment_date,omitempty" json:"nextPaymentDate,omitempty"`
	CreatedAt       time.Time  `db:"created_at" json:"createdAt"`
	ModifiedAt      time.Time  `db:"modified_at" json:"modifiedAt"`
	TransactionId   *string    `db:"transaction_id" json:"transactionId,omitempty"`
}

// RegisterSubscriptionRoutes registers the subscription-related routes
func RegisterSubscriptionRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.GET("/subscription-tiers", getSubscriptionTiers(dbx))
		// Add this to the RegisterSubscriptionRoutes function:
		v0.GET("/subscription-tiers/:id", getSubscriptionTierByID(dbx))
		v0.GET("/user-subscription/:userId", getUserSubscription(dbx))
		v0.POST("/user-subscription/:userId", createUserSubscription(dbx))
		v0.GET("/user-subscription/:userId/remaining-time", checkSubscriptionRemainingTime(dbx))
		v0.PUT("/user-subscription/:subscriptionId/payment-status", updatePaymentStatus(dbx)) // New endpoint
	}
}

type Daerah3TRecord struct {
	ID         string    `db:"id" json:"id,omitempty"`
	UserEmail  string    `db:"user_email" json:"userEmail"`
	CategoryID string    `db:"category_id" json:"categoryId"`
	IsActive   bool      `db:"is_active" json:"isActive"`
	CreatedAt  time.Time `db:"created_at" json:"createdAt,omitempty"`
	ModifiedAt time.Time `db:"modified_at" json:"modifiedAt,omitempty"`
}

func RegisterDaerah3TRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	v0 := r.Group("/v0")
	{
		v0.GET("/user/is-daerah-3t/:user_id", CheckUserDaerah3TStatus(dbx, redis))
		v0.POST("/daerah-3t", CreateDaerah3TEntry(dbx, redis))
	}
}

// CreateDaerah3TEntry handles the creation of a single daerah 3T entry
func CreateDaerah3TEntry(dbx *sqlx.DB, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// Define the request structure
		type daerah3TRequest struct {
			UserEmail    string `json:"userEmail"`
			CategoryID   string `json:"categoryId"`
			CategoryName string `json:"categoryName"`
			IsActive     bool   `json:"isActive"`
		}

		var req daerah3TRequest

		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"ok":    false,
				"error": "Invalid request: " + err.Error(),
			})
			return
		}

		// Validate required fields
		if req.UserEmail == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"ok":    false,
				"error": "User email is required",
			})
			return
		}

		// If CategoryName is provided, look up the corresponding CategoryID
		if req.CategoryName != "" {
			var categoryId string
			query := `SELECT id FROM categories WHERE name = $1 LIMIT 1`
			err := dbx.Get(&categoryId, query, req.CategoryName)

			if err != nil {
				if err == sql.ErrNoRows {
					c.JSON(http.StatusNotFound, gin.H{
						"ok":    false,
						"error": "Category with name '" + req.CategoryName + "' not found in the database",
					})
					return
				}

				log.Printf("Error fetching category by name '%s': %v", req.CategoryName, err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"ok":    false,
					"error": "Failed to fetch category by name",
				})
				return
			}

			req.CategoryID = categoryId
		} else if req.CategoryID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"ok":    false,
				"error": "Either categoryId or categoryName must be provided",
			})
			return
		}

		// Generate ID using ULID
		record := Daerah3TRecord{
			ID:         lib.GenerateULID(),
			UserEmail:  req.UserEmail,
			CategoryID: req.CategoryID,
			IsActive:   true, // Default to active if not specified
		}

		if req.IsActive != false { // Only override if explicitly set to false
			record.IsActive = req.IsActive
		}

		// Insert the record
		query := `
			INSERT INTO free_daerah_3t (
				id, user_email, category_id, is_active, created_at, modified_at
			) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			RETURNING id, created_at, modified_at
		`

		var result Daerah3TRecord
		err := dbx.QueryRowx(query, record.ID, record.UserEmail, record.CategoryID, record.IsActive).
			Scan(&result.ID, &result.CreatedAt, &result.ModifiedAt)

		if err != nil {
			log.Printf("Error creating daerah 3T entry: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"ok":    false,
				"error": "Failed to create daerah 3T entry",
			})
			return
		}

		// Invalidate the cache for this user
		// First, get the user's ID from email
		var userId string
		queryUser := `SELECT id FROM users WHERE email = $1`
		err = dbx.Get(&userId, queryUser, record.UserEmail)
		if err != nil && err != sql.ErrNoRows {
			log.Printf("Warning: could not get user ID for cache invalidation: %v", err)
			// Continue anyway, not critical
		}

		if userId != "" {
			// Delete the cache entry
			cacheKey := fmt.Sprintf("user:daerah3t:%s", userId)
			err = redis.Del(ctx, cacheKey).Err()
			if err != nil {
				log.Printf("Warning: could not invalidate cache for user %s: %v", userId, err)
				// Continue anyway, not critical
			}
		}

		// Add created_at and modified_at from the result
		record.CreatedAt = result.CreatedAt
		record.ModifiedAt = result.ModifiedAt

		c.JSON(http.StatusCreated, gin.H{
			"ok":            true,
			"message":       "Daerah 3T entry created successfully",
			"daerah3tEntry": record,
		})
	}
}

func CheckUserDaerah3TStatus(dbx *sqlx.DB, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userId := c.Param("user_id")

		if userId == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"ok":    false,
				"error": "User ID is required",
			})
			return
		}

		// Create a cache key for Redis
		cacheKey := fmt.Sprintf("user:daerah3t:%s", userId)

		// Try to get from cache first
		cachedData, err := redis.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return cached result
			c.Header("X-Cache", "HIT")
			if cachedData == "true" {
				c.JSON(http.StatusOK, gin.H{
					"ok":         true,
					"isDaerah3T": true,
				})
			} else {
				c.JSON(http.StatusOK, gin.H{
					"ok":         true,
					"isDaerah3T": false,
				})
			}
			return
		}

		// Cache miss - query the database
		c.Header("X-Cache", "MISS")

		// First, get the user's email
		var userEmail string
		queryEmail := `SELECT email FROM users WHERE id = $1`
		err = dbx.Get(&userEmail, queryEmail, userId)
		if err != nil {
			log.Printf("Error fetching user email: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"ok":    false,
				"error": "Failed to retrieve user information",
			})
			return
		}

		// Check if the user has free_daerah_3t entries
		var count int
		query := `
			SELECT COUNT(*) 
			FROM free_daerah_3t 
			WHERE user_email = $1 AND is_active = true
		`
		err = dbx.Get(&count, query, userEmail)
		if err != nil {
			log.Printf("Error checking daerah 3T status: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"ok":    false,
				"error": "Failed to check daerah 3T status",
			})
			return
		}

		isDaerah3T := count > 0

		// Cache the result for 10 minutes (600 seconds)
		cacheValue := "false"
		if isDaerah3T {
			cacheValue = "true"
		}
		err = redis.Set(ctx, cacheKey, cacheValue, 600*time.Second).Err()
		if err != nil {
			log.Printf("Error caching daerah 3T status: %v", err)
			// Continue even if caching fails
		}

		// Return the result
		c.JSON(http.StatusOK, gin.H{
			"ok":         true,
			"isDaerah3T": isDaerah3T,
		})
	}
}

// Add this new handler function:
func getSubscriptionTierByID(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		var tier SubscriptionTier

		query := "SELECT * FROM subscription_tiers WHERE id = $1 AND is_active = true"
		if err := dbx.Get(&tier, query, id); err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Subscription tier not found"})
				return
			}
			log.Printf("Error fetching subscription tier %s: %v", id, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription tier"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"tier": tier})
	}
}

// In the backend route handler for /v0/user-subscription/:subscriptionId/payment-status
// Add a check similar to what we added to the webhook handler
func updatePaymentStatus(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		subscriptionId := c.Param("subscriptionId")

		var req struct {
			PaymentStatus string `json:"paymentStatus" binding:"required"`
		}

		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
			return
		}

		// First check if it's already PAID
		var currentStatus string
		err := dbx.Get(&currentStatus, "SELECT payment_status FROM user_subscriptions WHERE id = $1", subscriptionId)

		if err != nil {
			log.Printf("Error checking current status: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check current status"})
			return
		}

		// Don't downgrade from PAID
		if currentStatus == "PAID" && req.PaymentStatus != "PAID" {
			log.Printf("Subscription %s already has PAID status, ignoring update to %s",
				subscriptionId, req.PaymentStatus)

			// Return success but notify that status was maintained
			c.JSON(http.StatusOK, gin.H{
				"message": "Payment status maintained as PAID",
				"subscription": UserSubscription{
					ID:            subscriptionId,
					PaymentStatus: "PAID",
				},
			})
			return
		}

		// Update payment status and last payment date
		query := `
            UPDATE user_subscriptions 
            SET 
                payment_status = $1,
                last_payment_date = CURRENT_TIMESTAMP,
                modified_at = CURRENT_TIMESTAMP
            WHERE id = $2
            RETURNING id, user_id, tier_id, payment_status
        `

		var updatedSubscription UserSubscription
		err = dbx.Get(&updatedSubscription, query, req.PaymentStatus, subscriptionId)

		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Subscription not found"})
				return
			}
			log.Printf("Error updating payment status for subscription %s: %v", subscriptionId, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update payment status"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message":      "Payment status updated successfully",
			"subscription": updatedSubscription,
		})
	}
}

// getSubscriptionTiers retrieves all active subscription tiers
func getSubscriptionTiers(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var tiers []SubscriptionTier
		query := "SELECT * FROM subscription_tiers WHERE is_active = true ORDER BY price_monthly ASC"
		if err := dbx.Select(&tiers, query); err != nil {
			log.Printf("Error fetching subscription tiers: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription tiers"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"tiers": tiers})
	}
}

func createUserSubscription(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("userId")
		var req struct {
			TierID string `json:"tierId" binding:"required"`
		}
		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
			return
		}

		// Generate a new subscription ID using ULID
		subscriptionID := lib.GenerateULID()

		// Set end_date to 30 days from now
		endDate := time.Now().AddDate(0, 0, 30) // Adds 30 days

		query := `
			INSERT INTO user_subscriptions (
				id, user_id, tier_id, start_date, end_date, is_active, auto_renewal, created_at, modified_at
			) VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		`
		_, err := dbx.Exec(query, subscriptionID, userId, req.TierID, endDate)
		if err != nil {
			log.Printf("Error creating subscription for user %s: %v", userId, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create subscription"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Subscription created successfully", "subscriptionId": subscriptionID})
	}
}

func checkSubscriptionRemainingTime(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("userId")
		var subscription UserSubscription

		// Query to get the active subscription for the user
		query := "SELECT * FROM user_subscriptions WHERE user_id = $1 AND is_active = true"
		err := dbx.Get(&subscription, query, userId)
		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription"})
			return
		}

		// Check if the subscription has an end date
		if subscription.EndDate == nil {
			c.JSON(http.StatusOK, gin.H{"message": "This subscription has no end date (permanent)"})
			return
		}

		// Calculate the remaining time
		now := time.Now()
		remainingDuration := subscription.EndDate.Sub(now)

		// If the subscription has expired
		if remainingDuration <= 0 {
			c.JSON(http.StatusOK, gin.H{"message": "Subscription has expired"})
			return
		}

		// Format the remaining duration in days, hours, and minutes
		days := int(remainingDuration.Hours() / 24)
		hours := int(remainingDuration.Hours()) % 24
		minutes := int(remainingDuration.Minutes()) % 60

		c.JSON(http.StatusOK, gin.H{
			"message":           "Subscription is active",
			"remaining_days":    days,
			"remaining_hours":   hours,
			"remaining_minutes": minutes,
		})
	}
}

// getUserSubscription retrieves the most recent PAID subscription for a user
func getUserSubscription(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("userId")
		var subscription UserSubscription

		// Modified query to get only PAID subscription, ordered by created_at desc
		query := `
			SELECT * FROM user_subscriptions 
			WHERE user_id = $1 
				AND is_active = true 
				AND payment_status = 'PAID'
			ORDER BY created_at DESC
			LIMIT 1`

		err := dbx.Get(&subscription, query, userId)
		if err != nil {
			if err == sql.ErrNoRows {
				// Return a specific response for no paid subscription
				c.JSON(http.StatusOK, gin.H{
					"message": "No active paid subscription found",
					"subscription": UserSubscription{
						UserID:        userId,
						TierID:        "free_tier_001",
						IsActive:      true,
						PaymentStatus: "PAID",
						StartDate:     time.Now(),
						CreatedAt:     time.Now(),
						ModifiedAt:    time.Now(),
					},
				})
				return
			}
			log.Printf("Error fetching subscription for user %s: %v", userId, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"subscription": subscription})
	}
}
