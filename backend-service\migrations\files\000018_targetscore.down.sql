-- Drop indexes for the name columns
DROP INDEX IF EXISTS idx_user_demographics_province_name;
DROP INDEX IF EXISTS idx_user_demographics_city_name;

-- Remove columns added in the up migration
ALTER TABLE user_demographics 
DROP COLUMN IF EXISTS target_score,
DROP COLUMN IF EXISTS learning_challenges;

-- Drop the newly added columns
ALTER TABLE user_demographics 
DROP COLUMN IF EXISTS province_name,
DROP COLUMN IF EXISTS city_name,
DROP COLUMN IF EXISTS district_name,
DROP COLUMN IF EXISTS village_name,
DROP COLUMN IF EXISTS education_level_name,
DROP COLUMN IF EXISTS program_study_name,
DROP COLUMN IF EXISTS target_score,
DROP COLUMN IF EXISTS learning_challenges,
DROP COLUMN IF EXISTS target_university,
DROP COLUMN IF EXISTS target_major;