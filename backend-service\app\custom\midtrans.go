package custom

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gopkg.in/yaml.v2"
)

// Config holds all configuration for Midtrans
type Config struct {
	Midtrans struct {
		Environment string `yaml:"environment"`
		APIKey      string `yaml:"api_key"`
	} `yaml:"midtrans"`
}

// ReadConfig reads configuration from a YAML file
func ReadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// GetMidtransConfig retrieves Midtrans configuration from environment variables or config file
func GetMidtransConfig() (*Config, error) {
	config := &Config{}

	// Check if environment variables exist
	env := os.Getenv("APP_ENV")
	apiKey := os.Getenv("MIDTRANS_IRIS_API_KEY")

	if env != "" && apiKey != "" {
		// Use environment variables
		config.Midtrans.Environment = env
		config.Midtrans.APIKey = apiKey
	} else {
		// Use config.yaml as a fallback
		var err error
		config, err = ReadConfig("configs/config.yaml")
		if err != nil {
			return nil, err
		}
	}

	return config, nil
}

// IrisPayoutService handles communication with Midtrans Iris API
type IrisPayoutService struct {
	config     *Config
	httpClient *http.Client
}

// NewIrisPayoutService creates a new instance of IrisPayoutService
func NewIrisPayoutService() (*IrisPayoutService, error) {
	config, err := GetMidtransConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get Midtrans config: %v", err)
	}

	return &IrisPayoutService{
		config:     config,
		httpClient: &http.Client{},
	}, nil
}

// GetBaseURL returns the appropriate base URL based on the environment
func (s *IrisPayoutService) GetBaseURL() string {
	// Only use production URL when environment is explicitly set to "production"
	// All other environments (development, local, staging, etc.) use sandbox
	if s.config.Midtrans.Environment == "production" {
		return "https://app.midtrans.com/iris/api/v1"
	}
	return "https://app.sandbox.midtrans.com/iris/api/v1"
}

// PayoutRequest represents the request structure for Iris payout
type PayoutRequest struct {
	Payouts []PayoutItem `json:"payouts"`
}

// PayoutItem represents a single payout item
type PayoutItem struct {
	BeneficiaryName    string `json:"beneficiary_name"`
	BeneficiaryAccount string `json:"beneficiary_account"`
	BeneficiaryBank    string `json:"beneficiary_bank"`
	BeneficiaryEmail   string `json:"beneficiary_email,omitempty"`
	Amount             string `json:"amount"`
	Notes              string `json:"notes"`
}

// PayoutResponse represents the response from Iris API
type PayoutResponse struct {
	Payouts []struct {
		Status      string `json:"status"`
		ReferenceNo string `json:"reference_no"`
	} `json:"payouts"`
}

// CreatePayout sends a payout request to Iris API
func (s *IrisPayoutService) CreatePayout(req *PayoutRequest) (*PayoutResponse, error) {
	endpoint := fmt.Sprintf("%s/payouts", s.GetBaseURL())

	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %v", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Add headers
	auth := base64.StdEncoding.EncodeToString([]byte(s.config.Midtrans.APIKey + ":"))
	httpReq.Header.Set("Authorization", "Basic "+auth)
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	// Send request
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var payoutResp PayoutResponse
	if err := json.Unmarshal(body, &payoutResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &payoutResp, nil
}

// ProcessWithdrawalPayout handles the payout process for a withdrawal request
func ProcessWithdrawalPayout(dbx *sqlx.DB, withdrawalID string) error {
	// Start transaction
	tx, err := dbx.Beginx()
	if err != nil {
		return fmt.Errorf("error starting transaction: %v", err)
	}
	defer tx.Rollback()

	// Get withdrawal request details
	var withdrawal struct {
		UserID         string          `db:"user_id"`
		Amount         float64         `db:"amount"`
		PaymentMethod  string          `db:"payment_method"`
		PaymentDetails json.RawMessage `db:"payment_details"`
	}

	err = tx.Get(&withdrawal, `
		SELECT user_id, amount, payment_method, payment_details 
		FROM withdrawal_requests 
		WHERE id = $1 AND status = 'PENDING'
		FOR UPDATE`,
		withdrawalID)
	if err != nil {
		return fmt.Errorf("error fetching withdrawal: %v", err)
	}

	// Get user details
	var user struct {
		Name  string `db:"name"`
		Email string `db:"email"`
	}
	err = tx.Get(&user, "SELECT name, email FROM users WHERE id = $1", withdrawal.UserID)
	if err != nil {
		return fmt.Errorf("error fetching user: %v", err)
	}

	// Parse payment details
	var bankDetails struct {
		BankCode string `json:"bank_code"`
		Account  string `json:"account_number"`
	}
	if err := json.Unmarshal(withdrawal.PaymentDetails, &bankDetails); err != nil {
		return fmt.Errorf("error parsing bank details: %v", err)
	}

	// Get configuration for environment info
	config, err := GetMidtransConfig()
	if err != nil {
		return fmt.Errorf("error getting config: %v", err)
	}

	// Initialize Iris service
	irisService, err := NewIrisPayoutService()
	if err != nil {
		return fmt.Errorf("error initializing iris service: %v", err)
	}

	// Create payout request
	payoutReq := &PayoutRequest{
		Payouts: []PayoutItem{
			{
				BeneficiaryName:    user.Name,
				BeneficiaryAccount: bankDetails.Account,
				BeneficiaryBank:    bankDetails.BankCode,
				BeneficiaryEmail:   user.Email,
				Amount:             fmt.Sprintf("%.2f", withdrawal.Amount),
				Notes:              fmt.Sprintf("Withdrawal request %s", withdrawalID),
			},
		},
	}

	// Send payout request
	resp, err := irisService.CreatePayout(payoutReq)
	if err != nil {
		return fmt.Errorf("error creating payout: %v", err)
	}

	if len(resp.Payouts) == 0 {
		return fmt.Errorf("no payout response received")
	}

	// Update withdrawal request with reference number and status
	_, err = tx.Exec(`
		UPDATE withdrawal_requests 
		SET 
			status = CASE 
				WHEN $2 = 'queued' THEN 'PROCESSING'
				ELSE 'FAILED'
			END,
			payment_details = payment_details || $3::jsonb,
			modified_at = CURRENT_TIMESTAMP
		WHERE id = $1`,
		withdrawalID,
		resp.Payouts[0].Status,
		json.RawMessage(fmt.Sprintf(`{"reference_no": "%s", "environment": "%s"}`,
			resp.Payouts[0].ReferenceNo, config.Midtrans.Environment)))
	if err != nil {
		return fmt.Errorf("error updating withdrawal status: %v", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("error committing transaction: %v", err)
	}

	return nil
}

// Handle webhook notifications from Iris
func HandleIrisWebhook(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var notification struct {
			Status             string `json:"status"`
			ReferenceNo        string `json:"reference_no"`
			BeneficiaryAccount string `json:"beneficiary_account"`
			Amount             string `json:"amount"`
			Timestamp          string `json:"timestamp"`
		}

		if err := c.BindJSON(&notification); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification format"})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}
		defer tx.Rollback()

		// Update withdrawal request status
		result, err := tx.Exec(`
			UPDATE withdrawal_requests
			SET 
				status = CASE 
					WHEN $1 = 'completed' THEN 'COMPLETED'
					WHEN $1 = 'failed' THEN 'FAILED'
					ELSE status
				END,
				processed_at = CASE 
					WHEN $1 IN ('completed', 'failed') THEN CURRENT_TIMESTAMP
					ELSE processed_at
				END,
				payment_details = payment_details || $2::jsonb
			WHERE payment_details->>'reference_no' = $3
			AND status = 'PROCESSING'`,
			notification.Status,
			json.RawMessage(fmt.Sprintf(`{"webhook_timestamp": "%s"}`, notification.Timestamp)),
			notification.ReferenceNo)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update withdrawal status"})
			return
		}

		rows, err := result.RowsAffected()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm update"})
			return
		}

		if rows == 0 {
			c.JSON(http.StatusOK, gin.H{"message": "No matching withdrawal found"})
			return
		}

		if err = tx.Commit(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete update"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Notification processed successfully"})
	}
}
