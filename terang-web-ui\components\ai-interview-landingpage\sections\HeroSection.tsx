// components/sections/HeroSection.tsx
import React from 'react';
import { Play, ArrowRight } from 'lucide-react';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

interface HeroSectionProps {
  router: AppRouterInstance;
}

interface Stat {
  number: string;
  label: string;
  color: string;
  iconSrc: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ router }) => {
  const stats: Stat[] = [
    { number: "10,000+", label: "Hours of Beta Testing", color: "text-purple-600", iconSrc: "https://cdn.terang.ai/landingpage-assets/time.svg" },
    { number: "24/7", label: "AI Availability", color: "text-blue-600", iconSrc: "https://cdn.terang.ai/landingpage-assets/247.svg" },
    { number: "100%", label: "Free Available", color: "text-orange-600", iconSrc: "https://cdn.terang.ai/landingpage-assets/gift.svg" }
  ];

  return (
    <section className="pt-16 pb-16 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full text-purple-800 text-sm font-medium mb-8 animate-bounce-slow border border-purple-200">
            <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="Sparkles" className="w-5 h-5 mr-2 text-purple-600" />
            Platform Simulasi Wawancara AI Terdepan di Indonesia
            <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="Sparkles" className="w-5 h-5 ml-2 text-purple-600" />
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 animate-fade-in-up">
            Master Your Interview with
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 animate-gradient-xy"> AI Power</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-600 mb-10 max-w-4xl mx-auto leading-relaxed animate-fade-in-up">
            Platform simulasi wawancara berbasis AI yang 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 font-semibold"> kontekstual dan dipersonalisasi</span>. 
            Persiapkan diri untuk LPDP, beasiswa luar negeri, dan wawancara kerja dengan teknologi terdepan.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16 animate-scale-in">
            <button 
              onClick={() => router.push('/available-interviews')}
              className="group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-10 py-5 rounded-2xl font-bold text-xl hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 hover:shadow-2xl shadow-lg flex items-center"
            >
              <Play className="w-6 h-6 mr-3 group-hover:animate-pulse" />
              Mulai LPDP Interview Gratis
              <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" />
            </button>
          </div>

          {/* Stats with animations - Updated and Centered */}
          <div className="flex justify-center">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-4xl">
              {stats.map((stat, index) => (
                <div 
                  key={index} 
                  className="text-center group cursor-pointer transform hover:scale-110 transition-all duration-300 bg-white/50 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-lg hover:shadow-xl"
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="flex justify-center mb-4">
                    <img 
                      src={stat.iconSrc} 
                      alt={stat.label}
                      className="w-24 h-24 group-hover:animate-pulse"
                    />
                  </div>
                  <div className={`text-4xl md:text-5xl font-bold ${stat.color} group-hover:animate-pulse mb-2`}>
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium text-lg">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;