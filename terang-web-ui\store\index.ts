// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { createWrapper } from 'next-redux-wrapper';
import availableExamsReducer from './slices/availableExamsSlice';
import bundlesReducer from './slices/bundlesSlice';
import purchasedBundlesReducer from './slices/purchasedBundlesSlice';

export const makeStore = () => 
  configureStore({
    reducer: {
      availableExams: availableExamsReducer,
      bundles: bundlesReducer,
      purchasedBundles: purchasedBundlesReducer,
      // Add other reducers here as your app grows
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];

export const wrapper = createWrapper<AppStore>(makeStore);