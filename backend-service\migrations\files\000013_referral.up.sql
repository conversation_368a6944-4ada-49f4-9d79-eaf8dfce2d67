-- Create enum for referral types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'referral_type') THEN
        CREATE TYPE referral_type AS ENUM (
            'REGISTRATION',
            'PURCHASE',
            'SUBSCRIPTION'
        );
    END IF;
END $$;

-- Create enum for referral status
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'referral_status') THEN
        CREATE TYPE referral_status AS ENUM (
            'ACTIVE',
            'EXPIRED',
            'USED'
        );
    END IF;
END $$;

-- Create withdrawal_requests table first (since it will be referenced)
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    amount NUMERIC(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, COMPLETED
    payment_method VARCHAR(50) NOT NULL, -- e.g., 'BANK_TRANSFER', 'PAYPAL'
    payment_details JSONB NOT NULL, -- Store payment method specific details
    processed_at TIMESTAMPTZ,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_withdrawal FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create referral_codes table
CREATE TABLE IF NOT EXISTS referral_codes (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    referrer_id VARCHAR(26) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    status referral_status DEFAULT 'ACTIVE',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL '365 days'),
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_referrer FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create referral_uses table
CREATE TABLE IF NOT EXISTS referral_uses (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    referral_code_id VARCHAR(26) NOT NULL,
    referee_id VARCHAR(26) NOT NULL,
    type referral_type NOT NULL,
    payment_id VARCHAR(26) NULL,
    subscription_id VARCHAR(26) NULL,
    referrer_reward_amount NUMERIC(10, 2) NOT NULL,
    referee_reward_amount NUMERIC(10, 2) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_referral_code FOREIGN KEY (referral_code_id) REFERENCES referral_codes(id) ON DELETE CASCADE,
    CONSTRAINT fk_referee FOREIGN KEY (referee_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_payment FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE SET NULL,
    CONSTRAINT fk_subscription FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL
);

-- Create referral_rewards table with withdrawal_id included
CREATE TABLE IF NOT EXISTS referral_rewards (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    referral_use_id VARCHAR(26) NOT NULL,
    user_id VARCHAR(26) NOT NULL,
    reward_type VARCHAR(50) NOT NULL,
    amount NUMERIC(10, 2) NOT NULL,
    status payment_status DEFAULT 'PENDING',
    withdrawal_id VARCHAR(26),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_referral_use FOREIGN KEY (referral_use_id) REFERENCES referral_uses(id) ON DELETE CASCADE,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_withdrawal FOREIGN KEY (withdrawal_id) REFERENCES withdrawal_requests(id)
);

-- Create referral_balances table
CREATE TABLE IF NOT EXISTS referral_balances (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL UNIQUE,
    available_balance NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    pending_balance NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    lifetime_earned NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_referral_balance FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create referral_configurations table
CREATE TABLE IF NOT EXISTS referral_configurations (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    type referral_type NOT NULL,
    referrer_reward_type VARCHAR(50) NOT NULL,
    referrer_reward_amount NUMERIC(10, 2) NOT NULL,
    referee_reward_type VARCHAR(50) NOT NULL,
    referee_reward_amount NUMERIC(10, 2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create referral_analytics table
CREATE TABLE IF NOT EXISTS referral_analytics (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    referral_code_id VARCHAR(26) NOT NULL,
    type VARCHAR(20) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    referer_url TEXT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_referral_code_analytics FOREIGN KEY (referral_code_id) REFERENCES referral_codes(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_referral_codes_referrer ON referral_codes(referrer_id);
CREATE INDEX idx_referral_codes_status ON referral_codes(status);
CREATE INDEX idx_referral_codes_expires ON referral_codes(expires_at);
CREATE INDEX idx_referral_uses_code ON referral_uses(referral_code_id);
CREATE INDEX idx_referral_uses_referee ON referral_uses(referee_id);
CREATE INDEX idx_referral_uses_payment ON referral_uses(payment_id);
CREATE INDEX idx_referral_uses_subscription ON referral_uses(subscription_id);
CREATE INDEX idx_referral_rewards_use ON referral_rewards(referral_use_id);
CREATE INDEX idx_referral_rewards_user ON referral_rewards(user_id);
CREATE INDEX idx_referral_configs_type ON referral_configurations(type);
CREATE INDEX idx_referral_analytics_code ON referral_analytics(referral_code_id);
CREATE INDEX idx_referral_analytics_type ON referral_analytics(type);
CREATE INDEX idx_referral_analytics_created ON referral_analytics(created_at);
CREATE INDEX idx_referral_balances_user ON referral_balances(user_id);
CREATE INDEX idx_withdrawal_requests_user ON withdrawal_requests(user_id);
CREATE INDEX idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX idx_withdrawal_requests_created ON withdrawal_requests(created_at);

-- Create trigger functions
CREATE OR REPLACE FUNCTION update_modified_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modified_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- First create a function to generate ULID
CREATE OR REPLACE FUNCTION generate_ulid() 
RETURNS text 
LANGUAGE plpgsql 
AS $$
DECLARE
    -- Timestamp part: 48 bits = 10 characters base32
    timestamp_chars text;
    -- Randomness part: 80 bits = 16 characters base32
    random_chars text;
    -- Crockford's Base32 alphabet
    alphabet text := '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    now_ms bigint;
    result text := '';
    i integer;
BEGIN
    -- Get current timestamp in milliseconds
    now_ms := (extract(epoch from clock_timestamp()) * 1000)::bigint;
    
    -- Generate timestamp part (10 characters)
    FOR i IN 1..10 LOOP
        timestamp_chars := substr(alphabet, (now_ms % 32)::integer + 1, 1) || timestamp_chars;
        now_ms := now_ms / 32;
    END LOOP;
    
    -- Generate random part (16 characters)
    FOR i IN 1..16 LOOP
        random_chars := random_chars || substr(alphabet, (random() * 32)::integer + 1, 1);
    END LOOP;
    
    RETURN timestamp_chars || random_chars;
END;
$$;

-- Update the trigger function
CREATE OR REPLACE FUNCTION process_withdrawal()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if sufficient balance exists
    IF NOT EXISTS (
        SELECT 1 
        FROM referral_balances 
        WHERE user_id = NEW.user_id 
        AND available_balance >= NEW.amount
    ) THEN
        RAISE EXCEPTION 'Insufficient balance for withdrawal';
    END IF;

    -- Update both available and pending balance
    UPDATE referral_balances
    SET 
        available_balance = available_balance - NEW.amount,
        pending_balance = pending_balance + NEW.amount,
        modified_at = CURRENT_TIMESTAMP
    WHERE user_id = NEW.user_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION handle_withdrawal_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'REJECTED' AND OLD.status = 'PENDING' THEN
        UPDATE referral_balances
        SET available_balance = available_balance + NEW.amount
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER referral_codes_modified_at
    BEFORE UPDATE ON referral_codes
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_analytics_modified_at
    BEFORE UPDATE ON referral_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_uses_modified_at
    BEFORE UPDATE ON referral_uses
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_rewards_modified_at
    BEFORE UPDATE ON referral_rewards
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_configs_modified_at
    BEFORE UPDATE ON referral_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER referral_balances_modified_at
    BEFORE UPDATE ON referral_balances
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER withdrawal_requests_modified_at
    BEFORE UPDATE ON withdrawal_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

CREATE TRIGGER reduce_balance_on_withdrawal
    AFTER INSERT ON withdrawal_requests
    FOR EACH ROW
    WHEN (NEW.status = 'PENDING')
    EXECUTE FUNCTION process_withdrawal();

CREATE TRIGGER handle_withdrawal_status_updates
    AFTER UPDATE ON withdrawal_requests
    FOR EACH ROW
    EXECUTE FUNCTION handle_withdrawal_status_change();

-- Insert default configurations
INSERT INTO referral_configurations (
    id,
    type,
    referrer_reward_type,
    referrer_reward_amount,
    referee_reward_type,
    referee_reward_amount,
    is_active
) VALUES 
(
    'reg_config_001',
    'REGISTRATION',
    'CASH',
    2000.00,
    'AI_CREDITS',
    1000.00,
    true
),
(
    'purchase_config_001',
    'PURCHASE',
    'COMMISSION',
    25.00,
    'DISCOUNT',
    25.00,
    true
),
(
    'subs_config_001',
    'SUBSCRIPTION',
    'COMMISSION',
    20.00,
    'DISCOUNT',
    20.00,
    true
);