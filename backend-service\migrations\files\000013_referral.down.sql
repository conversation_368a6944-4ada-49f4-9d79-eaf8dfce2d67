-- Drop all triggers first to avoid dependency issues
DROP TRIGGER IF EXISTS referral_codes_modified_at ON referral_codes;
DROP TRIGGER IF EXISTS referral_analytics_modified_at ON referral_analytics;
DROP TRIGGER IF EXISTS referral_uses_modified_at ON referral_uses;
DROP TRIGGER IF EXISTS referral_rewards_modified_at ON referral_rewards;
DROP TRIGGER IF EXISTS referral_configs_modified_at ON referral_configurations;
DROP TRIGGER IF EXISTS referral_balances_modified_at ON referral_balances;
DROP TRIGGER IF EXISTS withdrawal_requests_modified_at ON withdrawal_requests;
DROP TRIGGER IF EXISTS reduce_balance_on_withdrawal ON withdrawal_requests;
DROP TRIGGER IF EXISTS handle_withdrawal_status_updates ON withdrawal_requests;

-- Drop trigger functions
DROP FUNCTION IF EXISTS process_withdrawal();
DROP FUNCTION IF EXISTS handle_withdrawal_status_change();
DROP FUNCTION IF EXISTS generate_ulid();

-- Drop indexes
DROP INDEX IF EXISTS idx_referral_codes_referrer;
DROP INDEX IF EXISTS idx_referral_codes_status;
DROP INDEX IF EXISTS idx_referral_codes_expires;
DROP INDEX IF EXISTS idx_referral_uses_code;
DROP INDEX IF EXISTS idx_referral_uses_referee;
DROP INDEX IF EXISTS idx_referral_uses_payment;
DROP INDEX IF EXISTS idx_referral_uses_subscription;
DROP INDEX IF EXISTS idx_referral_rewards_use;
DROP INDEX IF EXISTS idx_referral_rewards_user;
DROP INDEX IF EXISTS idx_referral_configs_type;
DROP INDEX IF EXISTS idx_referral_analytics_code;
DROP INDEX IF EXISTS idx_referral_analytics_type;
DROP INDEX IF EXISTS idx_referral_analytics_created;
DROP INDEX IF EXISTS idx_referral_balances_user;
DROP INDEX IF EXISTS idx_withdrawal_requests_user;
DROP INDEX IF EXISTS idx_withdrawal_requests_status;
DROP INDEX IF EXISTS idx_withdrawal_requests_created;

-- Drop tables in correct order (respecting foreign key constraints)
DROP TABLE IF EXISTS referral_analytics;
DROP TABLE IF EXISTS referral_rewards;
DROP TABLE IF EXISTS referral_uses;
DROP TABLE IF EXISTS referral_codes;
DROP TABLE IF EXISTS referral_configurations;
DROP TABLE IF EXISTS withdrawal_requests;
DROP TABLE IF EXISTS referral_balances;

-- Drop update_modified_at function since it's no longer needed
DROP FUNCTION IF EXISTS update_modified_at();

-- Drop custom types
DROP TYPE IF EXISTS referral_type;
DROP TYPE IF EXISTS referral_status;