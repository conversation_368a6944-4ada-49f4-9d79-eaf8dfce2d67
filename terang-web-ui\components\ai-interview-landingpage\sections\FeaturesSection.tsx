// components/sections/FeaturesSection.tsx
import React, { useState, useEffect } from 'react';
import { Headphones, Mic, CheckCircle } from 'lucide-react';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: string;
  bgColor: string;
}

const FeaturesSection: React.FC = () => {
  const [activeFeature, setActiveFeature] = useState<number>(0);
  const [isVisible, setIsVisible] = useState<Record<string, boolean>>({});
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState<number>(0);
  const [isListening, setIsListening] = useState<boolean>(false);
  const [audioLevels, setAudioLevels] = useState<number[]>([0, 0, 0, 0, 0, 0, 0, 0]);
  const [currentSpeaker, setCurrentSpeaker] = useState<'interviewer' | 'applicant' | null>(null);

  const mockConversation = [
    { sender: 'interviewer', message: 'Selamat pagi! <PERSON>a <PERSON>, pewawancara AI untuk tes substansi LPDP hari ini. Bagaimana kabarnya?' },
    { sender: 'applicant', message: 'Selamat pagi Professor Terra! Kabar saya baik, terima kasih. Saya sangat antusias untuk mengikuti tes substansi ini.' },
    { sender: 'interviewer', message: 'Bagus! Mari kita mulai dengan perkenalan. Bisakan kamu ceritakan sedikit tentang latar belakang pendidikan dan motivasimu untuk mengambil beasiswa LPDP?' },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 3);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  // Audio visualization effect
  useEffect(() => {
    const audioInterval = setInterval(() => {
      if (currentSpeaker) {
        setAudioLevels(prev => prev.map(() => Math.random() * 100));
      } else {
        setAudioLevels(prev => prev.map(level => Math.max(0, level - 10)));
      }
    }, 100);

    return () => clearInterval(audioInterval);
  }, [currentSpeaker]);

  // Chat animation effect
  useEffect(() => {
    const chatInterval = setInterval(() => {
      if (currentMessageIndex < mockConversation.length) {
        const currentConversationItem = mockConversation[currentMessageIndex];
        const newMessage = {
          id: currentMessageIndex,
          sender: currentConversationItem.sender,
          message: currentConversationItem.message,
          timestamp: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' })
        };
        
        setCurrentSpeaker(newMessage.sender as 'interviewer' | 'applicant');
        setIsListening(newMessage.sender === 'interviewer');
        
        setChatMessages(prev => [...prev, newMessage]);
        setCurrentMessageIndex(prev => prev + 1);
        
        setTimeout(() => {
          setCurrentSpeaker(null);
          setIsListening(newMessage.sender === 'applicant');
        }, 2000);
      } else {
        setTimeout(() => {
          setChatMessages([]);
          setCurrentMessageIndex(0);
          setCurrentSpeaker(null);
          setIsListening(false);
        }, 3000);
      }
    }, 3500);

    return () => clearInterval(chatInterval);
  }, [currentMessageIndex, mockConversation.length]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('[data-animate]');
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const features: Feature[] = [
    {
      icon: <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="AI Brain" className="w-36 h-36" />,
      title: "AI-Powered Interview Simulation",
      description: "Experience realistic interview scenarios with our advanced AI interviewer, Professor Terra",
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-gradient-to-br from-purple-50 to-pink-50"
    },
    {
      icon: <img src="https://cdn.terang.ai/landingpage-assets/personalized-medicine.svg" alt="Analytics" className="w-36 h-36" />,
      title: "Personalized Feedback",
      description: "Get detailed analysis of your performance with actionable insights and improvement recommendations",
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50"
    },
    {
      icon: <img src="https://cdn.terang.ai/landingpage-assets/ai-book.svg" alt="Book" className="w-36 h-36" />,
      title: "Interactive Learning Path",
      description: "Auto-generated mindmap and curated resources based on your interview results",
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-gradient-to-br from-green-50 to-emerald-50"
    }
  ];

  const handleFeatureClick = (index: number) => {
    setActiveFeature(index);
  };

  return (
    <section 
      id="features" 
      className="py-20 bg-gradient-to-br from-white to-purple-50 relative"
      data-animate="true"
    >
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className={`text-center mb-20 ${isVisible.features ? 'animate-fade-in-up' : 'opacity-0'}`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full text-blue-800 text-sm font-medium mb-6">
            <img src="https://cdn.terang.ai/landingpage-assets/ai-brain.svg" alt="AI" className="w-4 h-4 mr-2" />
            Powered by Advanced AI
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Kenapa Memilih 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> Terang AI?</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Platform kami menggunakan teknologi AI terdepan untuk memberikan pengalaman wawancara yang realistis dan feedback yang mendalam
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            {features.map((feature, index) => (
              <button 
                key={index}
                className={`w-full p-4 rounded-2xl border-1 transition-all transform hover:scale-105 text-left ${
                  activeFeature === index 
                    ? `border-transparent ${feature.bgColor} shadow-2xl` 
                    : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-lg'
                }`}
                onClick={() => handleFeatureClick(index)}
                style={{ animationDelay: `${index * 300}ms` }}
                type="button"
              >
                <div className="flex items-center justify-center space-x-6">
                  <div className={`p-4 rounded-xl transform transition-all ${
                    activeFeature === index ? 'scale-110 animate-pulse' : 'scale-100'
                  }`}>
                    {feature.icon}
                  </div>
                  <div className="text-left">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                    <p className="text-gray-600 text-lg leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>

          <div className="relative">
            <div className="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 text-white shadow-2xl transform hover:scale-105 transition-all animate-gradient-xy relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              
              <div className="relative z-10 space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm flex-shrink-0">
                    <Headphones className="w-6 h-6 animate-pulse" />
                  </div>
                  <span className="text-lg md:text-xl font-bold truncate">Live Voice Interview</span>
                </div>
                
                {/* Voice Interface */}
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 h-80 flex flex-col">
                  {/* Header */}
                  <div className="flex items-center justify-between p-4 border-b border-white/20">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="font-semibold text-sm">Tes Substansi LPDP</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm opacity-80">RECORDING</span>
                    </div>
                  </div>

                  {/* Main Voice Interface */}
                  <div className="flex-1 p-4 flex flex-col items-center justify-center space-y-3 min-h-0">
                    {/* AI Avatar */}
                    <div className="relative">
                      <div className={`w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center shadow-lg transition-all ${
                        currentSpeaker === 'interviewer' ? 'scale-110 animate-pulse' : 'scale-100'
                      }`}>
                        <span className="text-lg font-bold text-white">PT</span>
                      </div>
                      {currentSpeaker === 'interviewer' && (
                        <div className="absolute -inset-1 border-2 border-yellow-400 rounded-full animate-ping"></div>
                      )}
                    </div>

                    {/* Voice Status */}
                    <div className="text-center">
                      <h3 className="text-sm font-semibold">Professor Terra</h3>
                      <p className="text-xs opacity-80">
                        {currentSpeaker === 'interviewer' ? '🗣️ Berbicara...' : 
                         isListening ? '👂 Mendengarkan...' : 
                         '⏸️ Standby'}
                      </p>
                    </div>

                    {/* Scrolling Question Display */}
                    {chatMessages.length > 0 && (
                      <div className="w-full bg-white/10 rounded-lg px-3 py-3 mb-3 border border-white/20">
                        <div className="text-xs opacity-60 mb-2 text-center font-medium">Pertanyaan Terkini:</div>
                        <div className="text-xs text-center leading-relaxed text-white">
                          {(() => {
                            const lastMessage = chatMessages[chatMessages.length - 1];
                            const secondLastMessage = chatMessages.length > 1 ? chatMessages[chatMessages.length - 2] : null;
                            
                            if (lastMessage?.sender === 'interviewer') {
                              return lastMessage.message.length > 70 
                                ? lastMessage.message.substring(0, 70) + '...'
                                : lastMessage.message;
                            } else if (secondLastMessage?.sender === 'interviewer') {
                              return secondLastMessage.message.length > 70 
                                ? secondLastMessage.message.substring(0, 70) + '...'
                                : secondLastMessage.message;
                            } else {
                              return 'Memulai sesi wawancara...';
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* Audio Visualizer */}
                    <div className="flex items-center justify-center space-x-1 h-8 bg-black/10 rounded-lg px-3 py-2">
                      {audioLevels.map((level, index) => (
                        <div
                          key={index}
                          className={`w-1.5 bg-gradient-to-t from-green-400 to-blue-400 rounded-full transition-all duration-150 ${
                            currentSpeaker ? 'animate-pulse' : ''
                          }`}
                          style={{
                            height: `${Math.max(4, level * 0.3)}px`,
                            animationDelay: `${index * 50}ms`
                          }}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="p-3 border-t border-white/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`p-1.5 rounded-full transition-all ${
                          isListening ? 'bg-red-500 animate-pulse' : 'bg-white/20'
                        }`}>
                          <Mic className="w-3 h-3 text-white" />
                        </div>
                        <div className="text-xs">
                          {isListening ? 'Active' : 'Standby'}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <div className="text-xs font-mono bg-white/20 px-2 py-1 rounded">
                          {String(Math.floor(chatMessages.length * 2.5)).padStart(2, '0')}:{String((chatMessages.length * 15) % 60).padStart(2, '0')} / 45:00
                        </div>
                        <div className="flex items-center space-x-1">
                          <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                          <span className="text-xs">Live</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Voice Features */}
                <div className="grid grid-cols-3 gap-3 text-center mt-4">
                  <div className="bg-white/10 rounded-xl p-3">
                    <div className="text-xl mb-1">🎙️</div>
                    <div className="text-xs opacity-80 leading-tight">Voice Recognition</div>
                  </div>
                  <div className="bg-white/10 rounded-xl p-3">
                    <div className="text-xl mb-1">🧠</div>
                    <div className="text-xs opacity-80 leading-tight">Real-time Analysis</div>
                  </div>
                  <div className="bg-white/10 rounded-xl p-3">
                    <div className="text-xl mb-1">📊</div>
                    <div className="text-xs opacity-80 leading-tight">Live Feedback</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;