import React, { useState, useRef, useEffect } from "react";
import styled, { keyframes } from "styled-components";
import Link from "next/link";
import { ChevronDown, Sparkles } from "lucide-react";

interface ProductDropdownProps {
  t?: (key: string) => string;
  isMobile?: boolean;
  onItemClick?: () => void;
}

const fadeInSlide = keyframes`
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-8px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
`;

const fadeInSlideMobile = keyframes`
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
  width: 100%;

  @media (max-width: 64em) {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
`;

const DropdownTrigger = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== '$isActive',
})<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: inherit;
  cursor: pointer;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
  line-height: inherit;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;

  svg {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: ${props => props.$isActive ? 'rotate(180deg)' : 'rotate(0deg)'};
  }

  /* Match MenuItem hover effect */
  &::after {
    content: " ";
    display: block;
    width: ${props => props.$isActive ? '100%' : '0%'};
    height: 2px;
    background: ${(props) => props.theme.text};
    transition: width 0.3s ease;
  }
  
  &:hover::after {
    width: 100%;
  }

  @media (max-width: 64em) {
    justify-content: center;
    
    &::after {
      display: none;
    }
  }
`;

const DropdownMenu = styled.div.withConfig({
  shouldForwardProp: (prop) => !['$isOpen', '$isMobile'].includes(prop),
})<{ $isOpen: boolean; $isMobile?: boolean }>`
  /* Desktop positioning */
  position: ${props => props.$isMobile ? 'static' : 'absolute'};
  top: ${props => props.$isMobile ? '0' : 'calc(100% + 8px)'};
  left: ${props => props.$isMobile ? '0' : '50%'};
  
  /* Desktop transforms - consistent anchor point */
  transform: ${props => {
    if (props.$isMobile) return 'none';
    return 'translateX(-50%) translateY(0) scale(1)';
  }};
  transform-origin: top center;
  
  /* Styling */
  background: ${(props) => props.theme.body};
  border: 1px solid ${(props) => props.theme.text}20;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04),
              0 0 0 1px rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  min-width: ${props => props.$isMobile ? '100%' : '300px'};
  z-index: 1000;
  
  /* Visibility and animations */
  opacity: ${(props) => (props.$isOpen ? 1 : 0)};
  visibility: ${(props) => (props.$isOpen ? "visible" : "hidden")};
  
  /* Smooth transitions without delays */
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              visibility 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Animation for opening */
  animation: ${(props) => {
    if (!props.$isOpen) return 'none';
    return props.$isMobile ? fadeInSlideMobile : fadeInSlide;
  }} 0.25s ease-out;
  
  /* Mobile positioning adjustments */
  @media (max-width: 64em) {
    position: static;
    transform: none !important;
    box-shadow: none;
    border: none;
    background: transparent;
    backdrop-filter: none;
    margin-left: 0;
    padding-left: 0;
    border-left: ${(props) => props.$isOpen ? `2px solid ${props.theme.text}30` : 'none'};
    margin-top: ${(props) => props.$isOpen ? '0.5rem' : '0'};
    width: 100%;
    display: ${(props) => props.$isOpen ? 'flex' : 'none'};
    flex-direction: column;
    align-items: center;
  }
`;

const DropdownItem = styled(Link)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  color: ${(props) => props.theme.text};
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  margin: 0.25rem;
  position: relative;
  overflow: hidden;

  &:hover {
    background: ${(props) => props.theme.text}08;
    transform: translateX(4px);
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: linear-gradient(45deg, #00b4d8, #0077b6);
    transform: scaleY(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom;
  }

  &:hover::before {
    transform: scaleY(1);
    transform-origin: top;
  }

  @media (max-width: 64em) {
    margin: 0.5rem 0;
    padding: 0.75rem 0;
    border-radius: 0;
    
    &:hover {
      background: transparent;
      transform: none;
    }
    
    &::before {
      display: none;
    }
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #00b4d8, #0077b6);
  color: white;
  flex-shrink: 0;
  transition: all 0.3s ease;

  ${DropdownItem}:hover & {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 180, 216, 0.3);
  }

  @media (max-width: 64em) {
    width: 32px;
    height: 32px;
    border-radius: 8px;
  }
`;

const CustomIcon = styled.img`
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1); /* Makes the icon white */
  transition: all 0.3s ease;
  display: block;
  object-fit: contain;

  @media (max-width: 64em) {
    width: 16px;
    height: 16px;
  }

  /* Fallback if image fails to load */
  &::before {
    content: "📚"; /* Fallback for exam prep */
    display: block;
    text-align: center;
    line-height: 20px;
    font-size: 16px;
  }
`;

const ItemContent = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const ItemTitle = styled.span`
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2;
  transition: color 0.3s ease;
  
  @media (max-width: 64em) {
    font-size: 0.9rem;
  }
`;

const ItemDescription = styled.span`
  font-size: 0.875rem;
  opacity: 0.7;
  line-height: 1.3;
  margin-top: 0.25rem;
  transition: opacity 0.3s ease;

  ${DropdownItem}:hover & {
    opacity: 0.9;
  }

  @media (max-width: 64em) {
    font-size: 0.8rem;
  }
`;

const ComingSoonBadge = styled.span`
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
  transition: all 0.3s ease;

  ${DropdownItem}:hover & {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  }
`;

const ProductDropdown: React.FC<ProductDropdownProps> = ({ t, isMobile = false, onItemClick }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Safe translation function
  const translate = (key: string, fallback: string) => {
    return t ? t(key) : fallback;
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen && isMobile) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isMobile]);

  // Mouse events for desktop hover
  const handleMouseEnter = () => {
    if (!isMobile) {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
      setIsOpen(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
      }, 100); // Reduced delay for smoother interaction
    }
  };

  // Click handler for mobile
  const handleTriggerClick = () => {
    if (isMobile) {
      setIsOpen(!isOpen);
    }
  };

  // Item click handler
  const handleItemClick = () => {
    setIsOpen(false);
    onItemClick?.();
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  return (
    <DropdownContainer 
      ref={dropdownRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <DropdownTrigger 
        $isActive={isOpen}
        onClick={handleTriggerClick}
      >
        {translate('products', 'Products')}
        <ChevronDown size={16} />
      </DropdownTrigger>
      
      <DropdownMenu 
        $isOpen={isOpen} 
        $isMobile={isMobile}
      >
        <DropdownItem href="/" onClick={handleItemClick}>
          <IconWrapper>
            <CustomIcon 
              src="https://cdn.terang.ai/landingpage-assets/application.svg" 
              alt="Exam Preparation" 
            />
          </IconWrapper>
          <ItemContent>
            <ItemTitle>{translate('exam_preparation_ai', 'Exam Preparation AI')}</ItemTitle>
            <ItemDescription>
              {translate('exam_prep_description', 'AI-powered study companion for exam success')}
            </ItemDescription>
          </ItemContent>
        </DropdownItem>
        
        <DropdownItem href="/ai-interview-lpdp" onClick={handleItemClick}>
          <IconWrapper>
            <CustomIcon 
              src="https://cdn.terang.ai/landingpage-assets/ai-voice.svg" 
              alt="AI Interview" 
            />
          </IconWrapper>
          <ItemContent>
            <ItemTitle>{translate('ai_interview', 'AI Interview')}</ItemTitle>
            <ItemDescription>
              {translate('ai_interview_description', 'Practice interviews with AI feedback')}
            </ItemDescription>
          </ItemContent>
          <ComingSoonBadge>
            <Sparkles size={12} />
            {translate('beta', 'Beta')}
          </ComingSoonBadge>
        </DropdownItem>
      </DropdownMenu>
    </DropdownContainer>
  );
};

export default ProductDropdown;