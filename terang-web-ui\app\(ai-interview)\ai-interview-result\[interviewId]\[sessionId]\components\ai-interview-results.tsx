"use client";

import React, { useState, useEffect, useRef, ChangeEvent, JSX } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp,
  User,
  MessageSquare,
  Award,
  Target,
  Lightbulb,
  BarChart3,
  Download,
  Share2,
  History,
  Play,
  Pause,
  Volume2,
  VolumeX,
  SkipBack,
  SkipForward,
  FileText,
  ChevronDown,
  ChevronUp,
  Loader
} from "lucide-react";
import { <PERSON><PERSON>, Card, CardBody, Progress, Chip } from "@heroui/react";
import MergedOverviewSection from "./merged-overview-section";
import Loading from "@/components/landingpage/components/Loading";
import { getOrTriggerGrading } from './actions';
import ResourceRecommendations from "./resource-recommendation";
import InterviewLearningMindMap from "./mindmap";

// Define types for transcript and interview data
interface TranscriptEntry {
  speaker: string;
  text: string;
  timestamp: string;
}

interface CategoryScore {
  category: string;
  score: number;
  maxScore: number;
  feedback: string;
  strengths: string[];
  improvements: string[];
}

interface CommunicationMetrics {
  clarityScore: number;
  confidenceScore: number;
  structureScore: number;
  responseTimeAvg: string;
  totalWords: number;
  averageResponseLength: string;
}

interface QuestionStats {
  totalQuestions: number;
  personalQuestions: number;
  studyPlanQuestions: number;
  contributionQuestions: number;
  qualificationQuestions: number;
  leadershipQuestions: number;
  indonesiaKnowledgeQuestions: number;
}

interface InterviewerAssessment {
  overallComment: string;
  recommendation: string;
  confidenceLevel: string;
  readinessLevel: number;
}

interface InterviewData {
  interviewId: string;
  participantName: string;
  interviewDate: string;
  duration: string;
  interviewer: string;
  category: string;
  interviewType?: string; // Added to store the interview type (subname)
  overallScore: number;
  maxScore: number;
  status: string;
  categoryScores: CategoryScore[];
  communicationMetrics: CommunicationMetrics;
  highlights: string[];
  improvementAreas: string[];
  questionStats: QuestionStats;
  interviewerAssessment: InterviewerAssessment;
  recordingUrl: string;
  transcript: TranscriptEntry[];
}

// API response types
interface RawTranscriptEntry {
  role: string;
  content: string;
  timestamp: string;
}

interface RoomMetadata {
  sessionId: string;
  interviewId: string;
  category: string;
  type: string;
  duration: string;
  category_name: string;
  name: string;
  subname: string;
  description: string;
  created_at: string;
}

interface UserMetadata {
  userEmail: string;
  userId: string;
  userName: string;
  interviewId: string;
  category: string;
  type: string;
  sessionId: string;
}

interface SessionSummary {
  user_name: string;
  user_email: string;
  interview_id: string;
  category: string;
  type: string;
  duration: string;
  duration_minutes: number;
}

interface TranscriptResponse {
  room_name: string;
  session_id: string;
  timestamp: string;
  last_updated: string;
  room_metadata: RoomMetadata;
  user_metadata: UserMetadata;
  transcript: RawTranscriptEntry[];
  session_summary: SessionSummary;
}

// Component props interface
interface AIInterviewResultsProps {
  interviewId: string;
  sessionId?: string;
}

// File status
enum FileStatus {
  LOADING = "loading",
  READY = "ready", 
  ERROR = "error",
  POLLING = "polling",
  NOT_FOUND = "not_found"
}

export default function AIInterviewResults({ interviewId, sessionId }: AIInterviewResultsProps) {
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<InterviewData | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [transcriptExpanded, setTranscriptExpanded] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Separate status for audio and transcript
  const [audioStatus, setAudioStatus] = useState<FileStatus>(FileStatus.LOADING);
  const [transcriptStatus, setTranscriptStatus] = useState<FileStatus>(FileStatus.LOADING);
  
  // Polling attempts counter
  const [audioPollingAttempts, setAudioPollingAttempts] = useState<number>(0);
  const [transcriptPollingAttempts, setTranscriptPollingAttempts] = useState<number>(0);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const mountedRef = useRef<boolean>(false);
  const audioPollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const transcriptPollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Configuration constants
  const MAX_POLLING_ATTEMPTS = 20;
  const POLLING_INTERVAL = 3000; // 3 seconds

  // Construct Google Cloud Storage URLs using sessionId
  const getRecordingUrl = (sid: string): string => {
    return `https://cdn-google.terang.ai/recordings/${sid}/recording.ogg`;
  };
  
  const getTranscriptUrl = (sid: string): string => {
    return `https://cdn-google.terang.ai/recordings/${sid}/transcript.json`;
  };

  // Check if a file exists at the given URL
  const checkFileExists = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error(`Error checking file existence at ${url}:`, error);
      return false;
    }
  };

  // Poll for audio file availability
  const pollForAudioFile = async (url: string): Promise<void> => {
    if (!mountedRef.current) return;
    
    setAudioStatus(FileStatus.POLLING);
    console.log(`Polling for audio file at ${url}, attempt ${audioPollingAttempts + 1}/${MAX_POLLING_ATTEMPTS}`);
    
    try {
      const exists = await checkFileExists(url);
      
      if (exists) {
        console.log("Audio file found, loading...");
        setAudioStatus(FileStatus.READY);
        
        // Clear timer
        if (audioPollingTimerRef.current) {
          clearTimeout(audioPollingTimerRef.current);
          audioPollingTimerRef.current = null;
        }
        
        // Update the audio source
        if (audioRef.current) {
          audioRef.current.src = url;
          audioRef.current.load();
        }
      } else {
        // Check if we've reached max attempts
        if (audioPollingAttempts >= MAX_POLLING_ATTEMPTS - 1) {
          console.error("Maximum audio polling attempts reached");
          setAudioStatus(FileStatus.NOT_FOUND);
          return;
        }
        
        // Increment attempts and schedule next poll
        setAudioPollingAttempts(prev => prev + 1);
        
        audioPollingTimerRef.current = setTimeout(() => {
          if (mountedRef.current) {
            pollForAudioFile(url);
          }
        }, POLLING_INTERVAL);
      }
    } catch (error) {
      console.error("Error polling for audio file:", error);
      setAudioStatus(FileStatus.ERROR);
    }
  };

  // Poll for transcript file availability
  const pollForTranscriptFile = async (url: string): Promise<void> => {
    if (!mountedRef.current) return;
    
    setTranscriptStatus(FileStatus.POLLING);
    console.log(`Polling for transcript file at ${url}, attempt ${transcriptPollingAttempts + 1}/${MAX_POLLING_ATTEMPTS}`);
    
    try {
      const response = await fetch(url);
      
      if (response.ok) {
        const transcriptData = await response.json();
        console.log("Transcript file found and loaded:", transcriptData);
        
        // Clear timer
        if (transcriptPollingTimerRef.current) {
          clearTimeout(transcriptPollingTimerRef.current);
          transcriptPollingTimerRef.current = null;
        }
        
        // Process the transcript data
        await processTranscriptData(transcriptData);
        setTranscriptStatus(FileStatus.READY);
        
      } else {
        // Check if we've reached max attempts
        if (transcriptPollingAttempts >= MAX_POLLING_ATTEMPTS - 1) {
          console.error("Maximum transcript polling attempts reached");
          setTranscriptStatus(FileStatus.NOT_FOUND);
          return;
        }
        
        // Increment attempts and schedule next poll
        setTranscriptPollingAttempts(prev => prev + 1);
        
        transcriptPollingTimerRef.current = setTimeout(() => {
          if (mountedRef.current) {
            pollForTranscriptFile(url);
          }
        }, POLLING_INTERVAL);
      }
    } catch (error) {
      console.error("Error polling for transcript file:", error);
      
      // Check if we've reached max attempts before retrying
      if (transcriptPollingAttempts >= MAX_POLLING_ATTEMPTS - 1) {
        setTranscriptStatus(FileStatus.ERROR);
        return;
      }
      
      // Schedule retry
      setTranscriptPollingAttempts(prev => prev + 1);
      transcriptPollingTimerRef.current = setTimeout(() => {
        if (mountedRef.current) {
          pollForTranscriptFile(url);
        }
      }, POLLING_INTERVAL);
    }
  };

  // Process transcript data and update state
  const processTranscriptData = async (transcriptData: TranscriptResponse): Promise<void> => {
    try {
      // Validate transcript data structure
      if (!transcriptData || !transcriptData.transcript || !Array.isArray(transcriptData.transcript)) {
        throw new Error("Invalid transcript data structure");
      }
      
      // Transform the transcript
      const formattedTranscript = transformTranscriptData(transcriptData);
      
      if (formattedTranscript.length === 0) {
        throw new Error("Empty transcript after formatting");
      }
      
      // Update data with transcript
      setData(prevData => {
        if (!prevData) return prevData;
        
        const updatedData: InterviewData = {
          ...prevData,
          transcript: formattedTranscript,
          questionStats: updateQuestionStats(formattedTranscript)
        };
        
        // Update metadata if available
        if (transcriptData.user_metadata?.userName) {
          updatedData.participantName = transcriptData.user_metadata.userName;
        }
        
        if (transcriptData.room_metadata?.category_name) {
          updatedData.category = transcriptData.room_metadata.category_name;
        }
        
        if (transcriptData.room_metadata?.duration) {
          updatedData.duration = transcriptData.room_metadata.duration;
        }

        // Store the interview subname from room_metadata for filtering categories
        if (transcriptData.room_metadata?.subname) {
          console.log("Interview subname from room_metadata:", transcriptData.room_metadata.subname);
          console.log("Full room_metadata:", JSON.stringify(transcriptData.room_metadata, null, 2));
          updatedData.interviewType = transcriptData.room_metadata.subname;
          console.log("Updated interviewType in data:", updatedData.interviewType);
        } else {
          console.log("No subname found in room_metadata:", transcriptData.room_metadata);
        }
        
        // Handle timestamp safely
        if (transcriptData.timestamp) {
          try {
            const date = new Date(transcriptData.timestamp);
            if (!isNaN(date.getTime())) {
              updatedData.interviewDate = date.toISOString().split('T')[0];
            }
          } catch (e) {
            console.warn("Error parsing timestamp:", e);
          }
        }
        
        return updatedData;
      });
      
      console.log("Transcript processed successfully");
      
    } catch (error) {
      console.error("Error processing transcript data:", error);
      setTranscriptStatus(FileStatus.ERROR);
    }
  };

  // Map interview types to relevant category scores
  const getRelevantCategories = (interviewType: string): string[] => {
    console.log("getRelevantCategories called with:", interviewType);
    
    // Define interview type keywords and their corresponding categories
    const categoryKeywords: Record<string, string[]> = {
      "Complete LPDP Scholarship": [
        "Personal Background and Motivation",
        "Study Plans and University Choice",
        "Future Contributions to Indonesia",
        "Academic and Professional Qualifications",
        "Leadership and Organizational Experience",
        "Knowledge about Indonesia's Challenges"
      ],
      "Personal Background": [
        "Personal Background and Motivation"
      ],
      "Study Plans": [
        "Study Plans and University Choice"
      ],
      "Future Contributions": [
        "Future Contributions to Indonesia"
      ],
      "Academic and Professional": [
        "Academic and Professional Qualifications"
      ],
      "Leadership": [
        "Leadership and Organizational Experience"
      ],
      "Knowledge about Indonesia": [
        "Knowledge about Indonesia's Challenges"
      ]
    };
    
    // Find matching interview type by keyword
    for (const [keyword, categories] of Object.entries(categoryKeywords)) {
      if (interviewType.includes(keyword)) {
        console.log(`Found matching keyword: "${keyword}" in interview type`);
        return categories;
      }
    }
    
    // If no match found, return all categories for complete simulation
    console.log("No specific match found, returning all categories");
    return categoryKeywords["Complete LPDP Scholarship"];
  };

  // Load grading results from backend using server action
  const loadGradingResults = async (sessionId: string): Promise<void> => {
    try {
      const gradingData = await getOrTriggerGrading(sessionId);
      
      if (!gradingData) {
        console.log('No grading data available yet');
        return;
      }
      
      console.log('Received grading data:', gradingData);
      
      // Update data with grading results
      setData(prevData => {
        const baseData = prevData || {
          interviewId: interviewId || "unknown",
          participantName: "",
          interviewDate: new Date().toISOString().split('T')[0],
          duration: "",
          interviewer: "AI Interviewer",
          category: "",
          interviewType: "", // Add interviewType property to baseData
          recordingUrl: sessionId ? getRecordingUrl(sessionId) : "",
          transcript: [],
          status: "completed"
        };
        
        // Get all category scores from the backend
        const allCategoryScores = gradingData.results.categoryScores.map((cs: any) => ({
          category: cs.category,
          score: cs.score,
          maxScore: cs.maxScore,
          feedback: cs.feedback,
          strengths: cs.strengths,
          improvements: cs.improvements
        }));
        
        // Log the exact category names from the backend
        console.log("EXACT CATEGORY NAMES FROM BACKEND:", JSON.stringify(allCategoryScores.map(cs => cs.category)));
        
        // Filter category scores based on interview type if available
        let filteredCategoryScores = allCategoryScores;
        let adjustedOverallScore = gradingData.results.overallScore;
        
        console.log("Debug - All category scores:", allCategoryScores.map(cs => cs.category));
        console.log("Debug - baseData:", baseData);
        console.log("Debug - interviewType value:", baseData.interviewType);
        
        if (baseData.interviewType) {
          console.log("Debug - Using interviewType:", baseData.interviewType);
          const relevantCategories = getRelevantCategories(baseData.interviewType);
          console.log("Relevant categories for", baseData.interviewType, ":", relevantCategories);
          
          if (relevantCategories.length > 0) {
            // For Complete LPDP Scholarship Interview, show all categories
            if (baseData.interviewType.includes("Complete LPDP Scholarship")) {
              console.log("Complete interview detected - showing all categories");
              filteredCategoryScores = allCategoryScores;
            } else {
              // For specific interview types, use more flexible matching
              filteredCategoryScores = allCategoryScores.filter(cs => {
                // Try to find a match in relevant categories
                return relevantCategories.some(relevantCategory => {
                  const categoryLower = cs.category.toLowerCase();
                  const relevantLower = relevantCategory.toLowerCase();
                  
                  // Check for keyword matches in both directions
                  return categoryLower.includes(relevantLower) || 
                         relevantLower.includes(categoryLower) ||
                         // Check for specific keywords
                         (relevantCategory.includes("Personal") && categoryLower.includes("personal")) ||
                         (relevantCategory.includes("Study") && categoryLower.includes("study")) ||
                         (relevantCategory.includes("Future") && categoryLower.includes("contribution")) ||
                         (relevantCategory.includes("Academic") && (categoryLower.includes("academic") || categoryLower.includes("qualification"))) ||
                         (relevantCategory.includes("Leadership") && (categoryLower.includes("leadership") || categoryLower.includes("organization"))) ||
                         (relevantCategory.includes("Knowledge") && categoryLower.includes("indonesia"));
                });
              });
            }
            
            console.log("Filtered categories after matching:", 
              filteredCategoryScores.map(cs => cs.category));
              
            // If we're still not showing any categories, show all of them
            if (filteredCategoryScores.length === 0) {
              console.log("No categories matched - showing all categories as fallback");
              filteredCategoryScores = allCategoryScores;
            }
            
            // Recalculate overall score based on filtered categories
            if (filteredCategoryScores.length > 0) {
              const totalScore = filteredCategoryScores.reduce((sum, cs) => sum + cs.score, 0);
              const totalMaxScore = filteredCategoryScores.reduce((sum, cs) => sum + cs.maxScore, 0);
              adjustedOverallScore = totalMaxScore > 0 ? Math.round((totalScore / totalMaxScore) * 10) : 0;
            }
          }
        }
        
        return {
          ...baseData,
          overallScore: adjustedOverallScore,
          maxScore: 10,
          categoryScores: filteredCategoryScores,
          communicationMetrics: {
            clarityScore: gradingData.results.communicationMetrics.clarityScore,
            confidenceScore: gradingData.results.communicationMetrics.confidenceScore,
            structureScore: gradingData.results.communicationMetrics.structureScore,
            responseTimeAvg: gradingData.results.communicationMetrics.responseTimeAvg,
            totalWords: gradingData.results.communicationMetrics.totalWords,
            averageResponseLength: gradingData.results.communicationMetrics.averageResponseLength
          },
          highlights: gradingData.results.highlights,
          improvementAreas: gradingData.results.improvementAreas,
          questionStats: {
            totalQuestions: gradingData.results.questionStats.totalQuestions,
            personalQuestions: gradingData.results.questionStats.personalQuestions,
            studyPlanQuestions: gradingData.results.questionStats.studyPlanQuestions,
            contributionQuestions: gradingData.results.questionStats.contributionQuestions,
            qualificationQuestions: gradingData.results.questionStats.qualificationQuestions,
            leadershipQuestions: gradingData.results.questionStats.leadershipQuestions,
            indonesiaKnowledgeQuestions: gradingData.results.questionStats.indonesiaKnowledgeQuestions
          },
          interviewerAssessment: {
            overallComment: gradingData.results.interviewerAssessment.overallComment,
            recommendation: gradingData.results.interviewerAssessment.recommendation,
            confidenceLevel: gradingData.results.interviewerAssessment.confidenceLevel,
            readinessLevel: gradingData.results.interviewerAssessment.readinessLevel
          }
        };
      });
      
    } catch (error) {
      console.error("Error loading grading results:", error);
      setError("Failed to load interview analysis. Please try again later.");
    }
  };

  // Manual retry function for transcript
  const retryTranscriptLoad = (): void => {
    if (sessionId && mountedRef.current) {
      setTranscriptStatus(FileStatus.LOADING);
      setTranscriptPollingAttempts(0);
      
      // Clear existing timer
      if (transcriptPollingTimerRef.current) {
        clearTimeout(transcriptPollingTimerRef.current);
        transcriptPollingTimerRef.current = null;
      }
      
      // Start polling again
      pollForTranscriptFile(getTranscriptUrl(sessionId));
    }
  };

  // Count questions from transcript
  const countQuestionsFromTranscript = (transcript: TranscriptEntry[]): number => {
    if (!transcript || transcript.length === 0) {
      return 0;
    }
    
    let questionCount = 0;
    
    transcript.forEach(entry => {
      if (entry.speaker === "Interviewer") {
        // Split text into sentences and count those that are questions
        const sentences = entry.text
          .split(/[.!?]+/)
          .filter(sentence => sentence.trim().length > 0);
        
        sentences.forEach(sentence => {
          const isQuestion = 
            sentence.trim().endsWith("?") || 
            /\b(what|how|when|where|why|who|could you|can you|do you|tell me|describe|explain)\b/i.test(sentence.toLowerCase());
            
          if (isQuestion) {
            questionCount++;
          }
        });
      }
    });
    
    return Math.max(questionCount, transcript.length > 0 ? 1 : 0);
  };

  const updateQuestionStats = (transcriptData: TranscriptEntry[]): QuestionStats => {
    const totalQuestions = countQuestionsFromTranscript(transcriptData);
    console.log(`Updating question stats with total count: ${totalQuestions}`);
    
    return {
      totalQuestions,
      personalQuestions: Math.round(totalQuestions * 0.22),
      studyPlanQuestions: Math.round(totalQuestions * 0.17),
      contributionQuestions: Math.round(totalQuestions * 0.22),
      qualificationQuestions: Math.round(totalQuestions * 0.17),
      leadershipQuestions: Math.round(totalQuestions * 0.11),
      indonesiaKnowledgeQuestions: Math.round(totalQuestions * 0.11),
    };
  };

  // Transform transcript data from API format to internal format
  const transformTranscriptData = (rawData: TranscriptResponse): TranscriptEntry[] => {
    if (!rawData || !rawData.transcript || !Array.isArray(rawData.transcript)) {
      console.error("No transcript data found in the response", rawData);
      return [];
    }
    
    console.log("Raw transcript data:", rawData.transcript);
    
    const formattedTranscript = rawData.transcript.map((entry, index) => {
      let formattedTimestamp = "00:00:00";
      
      if (entry.timestamp) {
        try {
          const date = new Date(entry.timestamp);
          
          if (!isNaN(date.getTime())) {
            date.setHours(date.getHours() + 7); //handler WIB
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            
            formattedTimestamp = `${hours}:${minutes}:${seconds}`;
          } else {
            console.warn(`Invalid timestamp format for entry ${index}:`, entry.timestamp);
            formattedTimestamp = `00:${String(index).padStart(2, '0')}:00`;
          }
        } catch (error) {
          console.error(`Error formatting timestamp for entry ${index}:`, error);
          formattedTimestamp = `00:${String(index).padStart(2, '0')}:00`;
        }
      } else {
        formattedTimestamp = `00:${String(index).padStart(2, '0')}:00`;
      }
      
      return {
        speaker: entry.role === "interviewer" ? "Interviewer" : "Applicant",
        text: entry.content || "",
        timestamp: formattedTimestamp
      };
    });
    
    return formattedTranscript;
  };

  // Main useEffect for component initialization
  useEffect(() => {
    console.log(`AIInterviewResults mounted with interviewId: ${interviewId}, sessionId: ${sessionId || 'none'}`);
    mountedRef.current = true;
    
    // Create initial data structure
    const initialData: InterviewData = {
      interviewId: interviewId || "unknown",
      participantName: "",
      interviewDate: new Date().toISOString().split('T')[0],
      duration: "Processing...",
      interviewer: "AI Interviewer",
      category: "",
      overallScore: 0,
      maxScore: 10,
      status: "pending",
      categoryScores: [],
      communicationMetrics: {
        clarityScore: 0,
        confidenceScore: 0,
        structureScore: 0,
        responseTimeAvg: "",
        totalWords: 0,
        averageResponseLength: ""
      },
      highlights: [],
      improvementAreas: [],
      questionStats: {
        totalQuestions: 0,
        personalQuestions: 0,
        studyPlanQuestions: 0,
        contributionQuestions: 0,
        qualificationQuestions: 0,
        leadershipQuestions: 0,
        indonesiaKnowledgeQuestions: 0
      },
      interviewerAssessment: {
        overallComment: "",
        recommendation: "",
        confidenceLevel: "",
        readinessLevel: 0
      },
      recordingUrl: "",
      transcript: []
    };
    
    if (sessionId) {
      initialData.recordingUrl = getRecordingUrl(sessionId);
    }
    
    setData(initialData);
    setLoading(false);
    
    if (sessionId) {
      // Start audio polling
      pollForAudioFile(getRecordingUrl(sessionId));
      
      // Start transcript polling
      pollForTranscriptFile(getTranscriptUrl(sessionId));
      
      // Load grading results
      console.log("Starting to load grading results for session:", sessionId);
      loadGradingResults(sessionId);
    } else {
      setAudioStatus(FileStatus.NOT_FOUND);
      setTranscriptStatus(FileStatus.NOT_FOUND);
    }
    
    return () => {
      console.log("Cleaning up AIInterviewResults component");
      mountedRef.current = false;
      
      // Clear all timers
      if (audioPollingTimerRef.current) {
        clearTimeout(audioPollingTimerRef.current);
        audioPollingTimerRef.current = null;
      }
      
      if (transcriptPollingTimerRef.current) {
        clearTimeout(transcriptPollingTimerRef.current);
        transcriptPollingTimerRef.current = null;
      }
    };
  }, [interviewId, sessionId]);

  // Audio player controls
  const togglePlay = (): void => {
    if (audioRef.current && audioStatus === FileStatus.READY) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((err) => {
          console.error("Audio playback error:", err);
          setError("Audio playback failed. The file may not be available or accessible.");
          setAudioStatus(FileStatus.ERROR);
        });
      }
      setIsPlaying(!isPlaying);
    } else if (audioStatus === FileStatus.POLLING) {
      setError("Audio is still being processed. Please wait...");
    } else if (audioStatus === FileStatus.NOT_FOUND) {
      setError("The audio recording is not available. It may still be processing.");
    } else if (audioStatus === FileStatus.ERROR && sessionId) {
      setAudioPollingAttempts(0);
      setError(null);
      pollForAudioFile(getRecordingUrl(sessionId));
    }
  };

  const toggleMute = (): void => {
    if (audioRef.current && audioStatus === FileStatus.READY) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const skipForward = (): void => {
    if (audioRef.current && audioStatus === FileStatus.READY) {
      audioRef.current.currentTime += 15;
    }
  };

  const skipBackward = (): void => {
    if (audioRef.current && audioStatus === FileStatus.READY) {
      audioRef.current.currentTime -= 15;
    }
  };

  const handleTimeUpdate = (): void => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = (): void => {
    if (audioRef.current) {
      const audioDuration = audioRef.current.duration;
      
      if (!isNaN(audioDuration) && isFinite(audioDuration) && audioDuration > 0) {
        setDuration(audioDuration);
        setAudioStatus(FileStatus.READY);
        
        const minutes = Math.floor(audioDuration / 60);
        const seconds = Math.floor(audioDuration % 60);
        const formattedDuration = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        
        setTimeout(() => {
          setData((prevData) => {
            if (prevData === null) return prevData;
            
            return {
              ...prevData,
              duration: formattedDuration
            };
          });
        }, 1000);
      } else {
        console.warn("Invalid audio duration:", audioDuration);
      }
    }
  };

  const handleSeek = (e: ChangeEvent<HTMLInputElement>): void => {
    if (audioStatus !== FileStatus.READY) return;
    
    const time = parseFloat(e.target.value);
    setCurrentTime(time);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  };

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Find transcript entry closest to current time
  const getCurrentTranscriptIndex = (): number => {
    if (!data || !data.transcript || data.transcript.length === 0) return -1;
    
    for (let i = data.transcript.length - 1; i >= 0; i--) {
      const entryTime = data.transcript[i].timestamp;
      const [minutes, seconds] = entryTime.split(':').map(Number);
      const entryTimeInSeconds = minutes * 60 + seconds;
      
      if (entryTimeInSeconds <= currentTime) {
        return i;
      }
    }
    
    return 0;
  };

  // Score display helpers
  const getScoreColor = (score: number, maxScore: number): "success" | "primary" | "warning" | "danger" => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 85) return "success";
    if (percentage >= 70) return "primary";
    if (percentage >= 60) return "warning";
    return "danger";
  };

  const getScoreColorClass = (score: number, maxScore: number): string => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 85) return "text-green-600";
    if (percentage >= 70) return "text-blue-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgClass = (score: number, maxScore: number): string => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 85) return "bg-green-100";
    if (percentage >= 70) return "bg-blue-100";
    if (percentage >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  const getReadinessMessage = (level: number): string => {
    if (level >= 85) return "Excellent - Ready for LPDP Interview";
    if (level >= 70) return "Good - Minor improvements needed";
    if (level >= 60) return "Fair - Moderate preparation required";
    return "Needs significant preparation";
  };

  // Render audio player status
  const renderAudioPlayerStatus = (): JSX.Element => {
    switch(audioStatus) {
      case FileStatus.LOADING:
        return (
          <div className="flex items-center gap-2 text-gray-600">
            <Loader className="animate-spin" size={16} />
            <span>Loading audio player...</span>
          </div>
        );
      case FileStatus.POLLING:
        return (
          <div className="flex items-center gap-2 text-blue-600">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-150"></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-300"></div>
            </div>
            <span>Processing audio file... ({audioPollingAttempts + 1}/{MAX_POLLING_ATTEMPTS})</span>
          </div>
        );
      case FileStatus.NOT_FOUND:
        return (
          <div className="flex items-center gap-2 text-amber-600">
            <AlertCircle size={16} />
            <span>Audio processing incomplete. Check back later.</span>
          </div>
        );
      case FileStatus.ERROR:
        return (
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle size={16} />
            <span>Audio unavailable. <button onClick={() => {
              if (sessionId) {
                setAudioPollingAttempts(0);
                pollForAudioFile(getRecordingUrl(sessionId));
              }
            }} className="underline">Try again</button></span>
          </div>
        );
      case FileStatus.READY:
      default:
        return <p className="text-sm text-gray-600">
                  {data?.duration ? `${data.duration} • ` : ""}{data?.interviewDate}
                </p>;
    }
  };

  // Render transcript status
  const renderTranscriptStatus = (): JSX.Element => {
    switch(transcriptStatus) {
      case FileStatus.LOADING:
        return (
          <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex items-center gap-2 text-blue-600 justify-center">
              <Loader className="animate-spin" size={16} />
              <span>Loading transcript...</span>
            </div>
          </div>
        );
      case FileStatus.POLLING:
        return (
          <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex items-center gap-2 text-blue-600 justify-center">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce delay-200"></div>
              </div>
              <span>Processing transcript... ({transcriptPollingAttempts + 1}/{MAX_POLLING_ATTEMPTS})</span>
            </div>
          </div>
        );
      case FileStatus.NOT_FOUND:
        return (
          <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex flex-col items-center gap-3 text-amber-600 justify-center">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} />
                <span>Transcript not available yet</span>
              </div>
              <button 
                onClick={retryTranscriptLoad}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Try Again
              </button>
            </div>
          </div>
        );
      case FileStatus.ERROR:
        return (
          <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex flex-col items-center gap-3 text-red-600 justify-center">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} />
                <span>Error loading transcript</span>
              </div>
              <button 
                onClick={retryTranscriptLoad}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                Retry Loading
              </button>
            </div>
          </div>
        );
      case FileStatus.READY:
      default:
        return <></>;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Analyzing Your Interview</h3>
          <p className="text-gray-600">Please wait while we process your performance...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Not Available</h3>
          <p className="text-gray-600">Unable to load interview results. Please try again later.</p>
          {error && <p className="text-red-500 mt-2">{error}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hidden audio element for the recording */}
      <audio 
        ref={audioRef}
        src={data.recordingUrl}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
        onError={(e) => {
          console.error("Audio error:", e);
          if (audioStatus !== FileStatus.POLLING) {
            setAudioStatus(FileStatus.ERROR);
            setError("Audio file could not be loaded. It may not exist or be accessible.");
          }
        }}
      >
        <track kind="captions" src="captions.vtt" label="English captions" />
      </audio>
      
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/available-interviews"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} />
                <span>Back to Interviews</span>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Interview Results</h1>
                <p className="text-sm text-gray-600">{data.category}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Link
                href="/interview-history"
                className="hidden sm:flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <History size={16} />
                <span>Interview History</span>
              </Link>
              <Button 
                variant="flat" 
                startContent={<Download size={16} />}
                className="hidden sm:flex"
              >
                Download Report
              </Button>
              <Button 
                variant="flat" 
                startContent={<Share2 size={16} />}
                className="hidden sm:flex"
              >
                Share Results
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6" data-main-content="true">
        {/* Session Info */}
        {sessionId && (
          <div className="mb-3 flex flex-wrap items-center gap-3">
            <div className="text-sm bg-gray-100 px-3 py-1 rounded-full">
              <span className="font-medium text-gray-600">Session ID:</span>{" "}
              <span className="font-mono text-gray-800">{sessionId}</span>
            </div>
            <div className="text-sm bg-gray-100 px-3 py-1 rounded-full">
              <span className="font-medium text-gray-600">Interview ID:</span>{" "}
              <span className="font-mono text-gray-800">{interviewId}</span>
            </div>
          </div>
        )}

        {/* MERGED OVERVIEW SECTION */}
        <MergedOverviewSection data={data} />

        {/* Display error message if there was an error but we still have data */}
        {error && (
          <div className="mb-4 bg-red-50 border-l-4 border-red-400 p-4 rounded">
            <div className="flex items-start">
              <AlertCircle className="text-red-600 mt-0.5 mr-3" size={18} />
              <div>
                <h3 className="text-sm font-medium text-red-800">There was an issue loading some data</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <p className="text-sm text-red-700 mt-1">Displaying available information.</p>
              </div>
            </div>
          </div>
        )}

        {/* Session Recording */}
        <Card className="mb-4">
          <CardBody className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <FileText className="text-blue-600" size={24} />
              <h3 className="text-lg font-semibold">Interview Recording</h3>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-4">
                <div className="flex items-center gap-3">
                  <button 
                    onClick={togglePlay}
                    className={`w-12 h-12 flex items-center justify-center ${
                      audioStatus === FileStatus.READY 
                        ? 'bg-blue-600 hover:bg-blue-700' 
                        : 'bg-gray-400'
                    } text-white rounded-full transition-colors`}
                    disabled={audioStatus !== FileStatus.READY}
                  >
                    {audioStatus === FileStatus.POLLING ? (
                      <Loader className="animate-spin" size={20} />
                    ) : isPlaying ? (
                      <Pause size={20} />
                    ) : (
                      <Play size={20} />
                    )}
                  </button>
                  <div>
                    <p className="font-medium text-gray-900">Interview Audio</p>
                    {renderAudioPlayerStatus()}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button 
                    onClick={skipBackward}
                    className={`w-8 h-8 flex items-center justify-center ${
                      audioStatus === FileStatus.READY 
                        ? 'bg-gray-200 hover:bg-gray-300' 
                        : 'bg-gray-100 text-gray-400'
                    } rounded-full transition-colors`}
                    disabled={audioStatus !== FileStatus.READY}
                  >
                    <SkipBack size={16} />
                  </button>
                  <button 
                    onClick={skipForward}
                    className={`w-8 h-8 flex items-center justify-center ${
                      audioStatus === FileStatus.READY 
                        ? 'bg-gray-200 hover:bg-gray-300' 
                        : 'bg-gray-100 text-gray-400'
                    } rounded-full transition-colors`}
                    disabled={audioStatus !== FileStatus.READY}
                  >
                    <SkipForward size={16} />
                  </button>
                  <button 
                    onClick={toggleMute}
                    className={`w-8 h-8 flex items-center justify-center ${
                      audioStatus === FileStatus.READY 
                        ? 'bg-gray-200 hover:bg-gray-300' 
                        : 'bg-gray-100 text-gray-400'
                    } rounded-full transition-colors`}
                    disabled={audioStatus !== FileStatus.READY}
                  >
                    {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
                  </button>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row items-center gap-4">
                <div className="text-sm text-gray-600 min-w-[40px]">
                  {formatTime(currentTime)}
                </div>
                <input 
                  type="range"
                  min="0"
                  max={duration || 100}
                  value={currentTime}
                  onChange={handleSeek}
                  disabled={audioStatus !== FileStatus.READY}
                  className={`flex-grow h-2 ${
                    audioStatus === FileStatus.READY 
                      ? 'bg-gray-300' 
                      : 'bg-gray-200'
                  } rounded-full appearance-none cursor-pointer`}
                  style={{
                    background: audioStatus === FileStatus.READY 
                      ? `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(currentTime / (duration || 100)) * 100}%, #d1d5db ${(currentTime / (duration || 100)) * 100}%, #d1d5db 100%)`
                      : undefined
                  }}
                />
                <div className="text-sm text-gray-600 min-w-[40px]">
                  {formatTime(duration)}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Transcript Section */}
        <Card className="mb-4">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <FileText className="text-blue-600" size={24} />
                <h3 className="text-lg font-semibold">Interview Transcript</h3>
              </div>
              {transcriptStatus === FileStatus.READY && data.transcript && data.transcript.length > 0 && (
                <button 
                  onClick={() => setTranscriptExpanded(!transcriptExpanded)}
                  className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
                >
                  {transcriptExpanded ? (
                    <>
                      <ChevronUp size={16} />
                      <span>Collapse</span>
                    </>
                  ) : (
                    <>
                      <ChevronDown size={16} />
                      <span>Expand All</span>
                    </>
                  )}
                </button>
              )}
            </div>
            
            {/* Show transcript status if not ready */}
            {transcriptStatus !== FileStatus.READY && renderTranscriptStatus()}
            
            {/* Show transcript content if ready */}
            {transcriptStatus === FileStatus.READY && (
              <>
                {data.transcript && data.transcript.length > 0 ? (
                  <>
                    <div className="space-y-3 max-h-[400px] overflow-y-auto" style={{ scrollBehavior: 'smooth' }}>
                      {data.transcript.map((entry, index) => {
                        const isActive = index === getCurrentTranscriptIndex() && audioStatus === FileStatus.READY;
                        // Only show first 3 entries if not expanded
                        if (!transcriptExpanded && index > 2) return null;
                        
                        return (
                          <div 
                            key={index} 
                            id={`transcript-${index}`}
                            className={`p-4 rounded-lg border ${isActive ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <span className={`font-medium ${entry.speaker === "Interviewer" ? "text-blue-700" : "text-green-700"}`}>
                                {entry.speaker}
                              </span>
                              <span className="text-sm text-gray-500">{entry.timestamp}</span>
                            </div>
                            <p className="text-gray-700">{entry.text}</p>
                          </div>
                        );
                      })}
                    </div>
                    
                    {!transcriptExpanded && data.transcript.length > 3 && (
                      <div className="mt-3 text-center">
                        <button 
                          onClick={() => setTranscriptExpanded(true)}
                          className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          Show all {data.transcript.length} entries
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="p-4 rounded-lg border border-gray-200 bg-gray-50">
                    <div className="flex flex-col items-center gap-2 text-gray-600 justify-center">
                      <Loading />
                      <span>Transcript is being processed...</span>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardBody>
        </Card>

        {/* Readiness Assessment */}
        <Card className="mb-4">
          <CardBody className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <TrendingUp className="text-blue-600" size={24} />
              <h3 className="text-lg font-semibold">LPDP Interview Readiness</h3>
            </div>
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Overall Readiness</span>
                <span className="text-sm font-bold">{data.interviewerAssessment.readinessLevel}%</span>
              </div>
              <Progress 
                value={data.interviewerAssessment.readinessLevel}
                color={data.interviewerAssessment.readinessLevel >= 70 ? "success" : "warning"}
                size="lg"
              />
            </div>
            <p className="text-gray-700 mb-4">{getReadinessMessage(data.interviewerAssessment.readinessLevel)}</p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <h4 className="font-semibold text-blue-900 mb-2">Interviewer&apos;s Assessment</h4>
              <p className="text-blue-800 text-sm">{data.interviewerAssessment.overallComment}</p>
            </div>
          </CardBody>
        </Card>

        {/* Category Scores - DETAILED PERFORMANCE ANALYSIS */}
        <Card className="mb-4">
          <CardBody className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <BarChart3 className="text-blue-600" size={24} />
              <h3 className="text-lg font-semibold">Detailed Performance Analysis</h3>
            </div>
            <div className="space-y-4">
              {data.categoryScores.map((category, index) => (
                <div key={index} className="border-b border-gray-200 last:border-b-0 pb-4 last:pb-0">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-medium text-gray-900">{category.category}</h4>
                    <div className={`flex items-center gap-2 ${getScoreBgClass(category.score, category.maxScore)} px-3 py-1 rounded-lg`}>
                      <span className={`text-lg font-bold ${getScoreColorClass(category.score, category.maxScore)}`}>
                        {category.score.toFixed(1)}
                      </span>
                      <span className="text-gray-500 text-sm">/{category.maxScore}</span>
                    </div>
                  </div>
                  <Progress 
                    value={(category.score / category.maxScore) * 100}
                    color={getScoreColor(category.score, category.maxScore)}
                    className="mb-3"
                  />
                  <p className="text-sm text-gray-700 mb-3">{category.feedback}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium text-green-700 mb-2 flex items-center gap-1">
                        <CheckCircle size={14} />
                        Strengths
                      </h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {category.strengths.map((strength, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-green-500 mt-1">•</span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-orange-700 mb-2 flex items-center gap-1">
                        <AlertCircle size={14} />
                        Areas for Improvement
                      </h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {category.improvements.map((improvement, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-orange-800">•</span>
                            <div className="flex flex-col w-full">
                              <span>{improvement}</span>
                              <ResourceRecommendations 
                                improvementItem={improvement} 
                                category={category.category} 
                              />
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Communication Metrics */}
        <Card className="mb-4">
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold mb-4">Communication Analysis</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className={`inline-flex items-center justify-center ${
                  data.communicationMetrics.clarityScore >= 8 ? 'bg-blue-100' : 'bg-gray-100'
                } rounded-full w-16 h-16 mb-2`}>
                  <span className="text-2xl font-bold text-blue-600">{data.communicationMetrics.clarityScore}</span>
                </div>
                <div className="text-sm text-gray-600">Clarity Score</div>
              </div>
              <div className="text-center">
                <div className={`inline-flex items-center justify-center ${
                  data.communicationMetrics.confidenceScore >= 8 ? 'bg-blue-100' : 'bg-gray-100'
                } rounded-full w-16 h-16 mb-2`}>
                  <span className="text-2xl font-bold text-blue-600">{data.communicationMetrics.confidenceScore}</span>
                </div>
                <div className="text-sm text-gray-600">Confidence</div>
              </div>
              <div className="text-center">
                <div className={`inline-flex items-center justify-center ${
                  data.communicationMetrics.structureScore >= 8 ? 'bg-blue-100' : 'bg-gray-100'
                } rounded-full w-16 h-16 mb-2`}>
                  <span className="text-2xl font-bold text-blue-600">{data.communicationMetrics.structureScore}</span>
                </div>
                <div className="text-sm text-gray-600">Structure</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{data.communicationMetrics.responseTimeAvg}</div>
                <div className="text-sm text-gray-600">Avg Response Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{data.communicationMetrics.totalWords}</div>
                <div className="text-sm text-gray-600">Total Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{data.communicationMetrics.averageResponseLength}</div>
                <div className="text-sm text-gray-600">Avg Response Length</div>
              </div>
            </div>
          </CardBody>
        </Card>

        <InterviewLearningMindMap categoryScores={data.categoryScores} improvementAreas={data.improvementAreas} transcript={data.transcript} />

        {/* Key Highlights & Improvements */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <CheckCircle className="text-green-600" size={24} />
                <h3 className="text-lg font-semibold">Key Highlights</h3>
              </div>
              <ul className="space-y-2">
                {data.highlights.map((highlight, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span className="text-gray-700 text-sm">{highlight}</span>
                  </li>
                ))}
              </ul>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Lightbulb className="text-orange-600" size={24} />
                <h3 className="text-lg font-semibold">Improvement Recommendations</h3>
              </div>
              <ul className="space-y-2">
                {data.improvementAreas.map((area, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-orange-500 mt-1">•</span>
                    <div className="flex flex-col w-full">
                      <span className="text-gray-700 text-sm">{area}</span>
                      <ResourceRecommendations 
                        improvementItem={area} 
                        category="General"
                      />
                    </div>
                  </li>
                ))}
              </ul>
            </CardBody>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
          <Button 
            color="primary"
            size="lg"
            onClick={() => router.push('/available-interviews')}
            className="px-8"
          >
            Practice Another Interview
          </Button>
          <Link
            href="/interview-history"
            className="inline-flex items-center justify-center gap-2 px-8 py-3 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            <History size={18} />
            View Interview History
          </Link>
          <Button 
            variant="bordered"
            size="lg"
            startContent={<Download size={16} />}
            className="px-8"
          >
            Download Full Report
          </Button>
        </div>
      </div>
    </div>
  );
}
