-- Create the free_daerah_3t table
CREATE TABLE IF NOT EXISTS free_daerah_3t (
    id character varying(26) NOT NULL,
    user_email character varying(255) NOT NULL,
    category_id character varying(26) NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    modified_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT fk_free_daerah_3t_user FOREIGN KEY (user_email) REFERENCES users(email),
    CONSTRAINT fk_free_daerah_3t_category FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Add indexes
CREATE INDEX idx_free_daerah_3t_user_email ON free_daerah_3t USING btree (user_email);
CREATE INDEX idx_free_daerah_3t_category_id ON free_daerah_3t USING btree (category_id);

-- Create ULID generation function for the table
CREATE OR REPLACE FUNCTION insert_free_daerah_3t_with_ulid()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id := generate_ulid();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for ULID generation
CREATE TRIGGER set_free_daerah_3t_ulid
BEFORE INSERT ON free_daerah_3t
FOR EACH ROW EXECUTE FUNCTION insert_free_daerah_3t_with_ulid();

-- Create trigger for modified_at
CREATE TRIGGER free_daerah_3t_modified_at 
BEFORE UPDATE ON free_daerah_3t 
FOR EACH ROW EXECUTE FUNCTION update_modified_at();