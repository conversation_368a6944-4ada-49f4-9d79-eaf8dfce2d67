package bundle

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/terang-ai/backend-service/lib"
)

// ExamBundle represents the exam_bundles table structure
type ExamBundle struct {
	ID                 string         `db:"id" json:"id"`
	Name               string         `db:"name" json:"name"`
	Description        string         `db:"description" json:"description"`
	Price              float64        `db:"price" json:"price"`
	DiscountPercentage sql.NullInt32  `db:"discount_percentage" json:"discountPercentage,omitempty"`
	ThumbnailURL       sql.NullString `db:"thumbnail_url" json:"thumbnailUrl,omitempty"`
	BannerURL          sql.NullString `db:"banner_url" json:"bannerUrl,omitempty"`
	Visibility         string         `db:"visibility" json:"visibility"`
	ValidFrom          sql.NullTime   `db:"valid_from" json:"validFrom,omitempty"`
	ValidUntil         sql.NullTime   `db:"valid_until" json:"validUntil,omitempty"`
	Metadata           string         `db:"metadata" json:"metadata,omitempty"` // JSONB stored as string
	CreatedAt          time.Time      `db:"created_at" json:"createdAt"`
	ModifiedAt         time.Time      `db:"modified_at" json:"modifiedAt"`
	UserID             string         `db:"user_id" json:"userId"`
}

// ExamBundleItem represents the exam_bundle_items table structure
type ExamBundleItem struct {
	ID       string `db:"id" json:"id"`
	BundleID string `db:"bundle_id" json:"bundleId"`
	ExamID   string `db:"exam_id" json:"examId"`
}

// ExamBundleWithItems represents a bundle with its associated exam items
type ExamBundleWithItems struct {
	Bundle ExamBundle       `json:"bundle"`
	Exams  []ExamBundleItem `json:"exams"`
}

// CreateBundleRequest represents the request body for creating a bundle
type CreateBundleRequest struct {
	Name               string                 `json:"name" binding:"required"`
	Description        string                 `json:"description" binding:"required"`
	Price              float64                `json:"price" binding:"required"`
	DiscountPercentage *int                   `json:"discountPercentage"`
	ThumbnailURL       string                 `json:"thumbnailUrl"`
	BannerURL          string                 `json:"bannerUrl"`
	Visibility         string                 `json:"visibility"`
	ValidFrom          *time.Time             `json:"validFrom"`
	ValidUntil         *time.Time             `json:"validUntil"`
	Metadata           map[string]interface{} `json:"metadata"`
	ExamIDs            []string               `json:"examIds" binding:"required"`
}

// UpdateBundleRequest represents the request body for updating a bundle
type UpdateBundleRequest struct {
	Name               string                 `json:"name"`
	Description        string                 `json:"description"`
	Price              *float64               `json:"price"`
	DiscountPercentage *int                   `json:"discountPercentage"`
	ThumbnailURL       string                 `json:"thumbnailUrl"`
	BannerURL          string                 `json:"bannerUrl"`
	Visibility         string                 `json:"visibility"`
	ValidFrom          *time.Time             `json:"validFrom"`
	ValidUntil         *time.Time             `json:"validUntil"`
	Metadata           map[string]interface{} `json:"metadata"`
	ExamIDs            []string               `json:"examIds"`
}

type PaginationInfo struct {
	TotalData   int `json:"total_data"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
}

// BundleResult holds the bundle query results
// BundleWithCategoryDetail adds category information to a bundle with exams
type BundleWithCategoryDetail struct {
	Bundle      ExamBundle       `json:"bundle"`
	Exams       []ExamBundleItem `json:"exams"`
	Category    CategoryInfo     `json:"category,omitempty"`
	HasCategory bool             `json:"-"`
}

// CategoryInfo represents basic category information
type CategoryInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// BundleWithCategory adds category information to a bundle
type BundleWithCategory struct {
	Bundle      ExamBundle   `json:"bundle"`
	Category    CategoryInfo `json:"category,omitempty"`
	HasCategory bool         `json:"-"`
}

// FlattenedBundleWithCategory combines bundle properties directly with category
type FlattenedBundleWithCategory struct {
	// Bundle properties directly embedded
	ID                 string         `json:"id"`
	Name               string         `json:"name"`
	Description        string         `json:"description"`
	Price              float64        `json:"price"`
	DiscountPercentage sql.NullInt32  `json:"discountPercentage,omitempty"`
	ThumbnailURL       sql.NullString `json:"thumbnailUrl,omitempty"`
	BannerURL          sql.NullString `json:"bannerUrl,omitempty"`
	Visibility         string         `json:"visibility"`
	ValidFrom          sql.NullTime   `json:"validFrom,omitempty"`
	ValidUntil         sql.NullTime   `json:"validUntil,omitempty"`
	Metadata           string         `json:"metadata,omitempty"`
	CreatedAt          time.Time      `json:"createdAt"`
	ModifiedAt         time.Time      `json:"modifiedAt"`
	UserID             string         `json:"userId"`

	// Category as a nested field
	Category     CategoryInfo `json:"category,omitempty"`
	IsFreeAccess bool         `json:"isFreeAccess"` // NEW field
}

// Updated BundlesResult with flattened structure
type BundlesResult struct {
	Bundles          []ExamBundle                  `json:"-"` // Original bundles (not returned to client)
	EnhancedBundles  []BundleWithCategory          `json:"-"` // Bundles with category info (not used anymore)
	FlattenedBundles []FlattenedBundleWithCategory `json:"-"` // Flattened bundles with category
	PaginationInfo   PaginationInfo                `json:"pagination"`
	Error            error                         `json:"-"`
}

// Updated BundleResult for detail view
type BundleResult struct {
	Bundle          *ExamBundleWithItems         `json:"-"` // Original bundle (not returned to client)
	EnhancedBundle  *BundleWithCategoryDetail    `json:"-"` // Bundle with category info
	FlattenedBundle *FlattenedBundleWithCategory `json:"-"` // Flattened bundle with category
	Error           error                        `json:"-"`
}

// RegisterBundleRoutes registers the bundle related routes
func RegisterBundleRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.GET("/exam-bundles", listBundles(dbx))
		v0.GET("/exam-bundles/:bundleId", getBundleByID(dbx))
		v0.POST("/exam-bundles/:userId", createBundle(dbx))
		v0.PUT("/exam-bundles/:userId/:bundleId", updateBundle(dbx))
		v0.DELETE("/exam-bundles/:userId/:bundleId", deleteBundle(dbx))
		v0.POST("/exam-bundles/:userId/:bundleId/exams", addExamToBundle(dbx))
		v0.DELETE("/exam-bundles/:userId/:bundleId/exams/:examId", removeExamFromBundle(dbx))

		// New endpoint for purchased bundles
		v0.GET("/exam-bundles/purchased-bundles/:userId", getPurchasedBundles(dbx))
		v0.GET("/exam-bundles/purchased-bundles/:userId/:bundleId", getPurchasedBundleDetails(dbx))
	}
}

// listBundles handles listing all bundles with pagination and category info
// Modified listBundles function with daerah 3T support
// listBundles handles listing all bundles with pagination and category info
// Modified listBundles function with daerah 3T support
func listBundles(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Parse query parameters (existing code)
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		if page < 1 {
			page = 1
		}

		pageSize, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
		if pageSize < 1 || pageSize > 100 {
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		visibility := c.Query("visibility")
		categoryID := c.Query("categoryId")

		// NEW: Get user email for daerah 3T check
		userEmail := c.Query("email")

		// Debug log
		fmt.Printf("listBundles called with categoryId: %s, email: %s\n", categoryID, userEmail)

		// Set timeout context (existing code)
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		resultCh := make(chan BundlesResult, 1)

		go func() {
			var bundles []ExamBundle
			var totalData int
			var query string
			var args []interface{}

			// Base queries for bundles (existing code)
			baseQuery := `
				SELECT b.id, b.name, b.description, b.price, b.discount_percentage, b.thumbnail_url, 
				       b.banner_url, b.visibility, b.valid_from, b.valid_until, b.metadata, 
				       b.created_at, b.modified_at, b.user_id
				FROM exam_bundles b
			`

			baseCountQuery := `
				SELECT COUNT(DISTINCT b.id) FROM exam_bundles b
			`

			// If we have a category filter, join with the necessary tables (existing code)
			if categoryID != "" {
				baseQuery = `
					SELECT DISTINCT b.id, b.name, b.description, b.price, b.discount_percentage, b.thumbnail_url, 
					       b.banner_url, b.visibility, b.valid_from, b.valid_until, b.metadata, 
					       b.created_at, b.modified_at, b.user_id
					FROM exam_bundles b
					JOIN exam_bundle_items bi ON b.id = bi.bundle_id
					JOIN exam_categories ec ON bi.exam_id = ec.exam_id
				`
				baseCountQuery = `
					SELECT COUNT(DISTINCT b.id) 
					FROM exam_bundles b
					JOIN exam_bundle_items bi ON b.id = bi.bundle_id
					JOIN exam_categories ec ON bi.exam_id = ec.exam_id
				`

				baseQuery += " WHERE ec.category_id = $1"
				baseCountQuery += " WHERE ec.category_id = $1"
				args = append(args, categoryID)
			}

			// Apply visibility filter (existing code)
			if visibility != "" {
				if len(args) == 0 {
					baseQuery += " WHERE b.visibility = $1"
					baseCountQuery += " WHERE b.visibility = $1"
					args = append(args, visibility)
				} else {
					baseQuery += " AND b.visibility = $" + strconv.Itoa(len(args)+1)
					baseCountQuery += " AND b.visibility = $" + strconv.Itoa(len(args)+1)
					args = append(args, visibility)
				}
			}

			// Add ordering and pagination (existing code)
			query = baseQuery + " ORDER BY b.created_at DESC LIMIT $" + strconv.Itoa(len(args)+1) +
				" OFFSET $" + strconv.Itoa(len(args)+2)
			args = append(args, pageSize, offset)

			// Debug final query (existing code)
			fmt.Printf("Final Query: %s\n", query)
			fmt.Printf("Query Args: %v\n", args)

			// Execute count query (existing code)
			err := dbx.GetContext(ctx, &totalData, baseCountQuery, args[:len(args)-2]...)
			if err != nil {
				fmt.Printf("Count query error: %v\n", err)
				resultCh <- BundlesResult{Error: fmt.Errorf("failed to count bundles: %w", err)}
				return
			}

			// Execute main query (existing code)
			err = dbx.SelectContext(ctx, &bundles, query, args...)
			if err != nil {
				fmt.Printf("Main query error: %v\n", err)
				resultCh <- BundlesResult{Error: fmt.Errorf("failed to fetch bundles: %w", err)}
				return
			}

			// Fetch category information for each bundle (existing code)
			bundleCategories := make(map[string]CategoryInfo)

			if len(bundles) > 0 {
				// Collect bundle IDs code (existing code)
				bundleIDs := make([]string, len(bundles))
				for i, bundle := range bundles {
					bundleIDs[i] = bundle.ID
				}

				// Category query (existing code)
				categoryQuery := `
					WITH FirstExamPerBundle AS (
						SELECT DISTINCT ON (bi.bundle_id) 
							bi.bundle_id, 
							bi.exam_id
						FROM exam_bundle_items bi
						WHERE bi.bundle_id = ANY($1)
						ORDER BY bi.bundle_id, bi.id
					),
					BundleCategories AS (
						SELECT DISTINCT ON (fe.bundle_id)
							fe.bundle_id,
							c.id AS category_id,
							c.name AS category_name
						FROM FirstExamPerBundle fe
						JOIN exam_categories ec ON fe.exam_id = ec.exam_id
						JOIN categories c ON ec.category_id = c.id
						ORDER BY fe.bundle_id, c.id
					)
					SELECT * FROM BundleCategories
				`

				var categoryRows []struct {
					BundleID     string `db:"bundle_id"`
					CategoryID   string `db:"category_id"`
					CategoryName string `db:"category_name"`
				}

				err = dbx.SelectContext(ctx, &categoryRows, categoryQuery, pq.Array(bundleIDs))
				if err != nil {
					fmt.Printf("Category query error: %v\n", err)
					resultCh <- BundlesResult{Error: fmt.Errorf("failed to fetch bundle categories: %w", err)}
					return
				}

				// Build the category map (existing code)
				for _, row := range categoryRows {
					bundleCategories[row.BundleID] = CategoryInfo{
						ID:   row.CategoryID,
						Name: row.CategoryName,
					}
				}

				// NEW: Check for daerah 3T access if email is provided
				bundleFreeDaerah3TAccess := make(map[string]bool)
				if userEmail != "" && len(bundles) > 0 {
					// Debug statement
					fmt.Printf("Checking daerah 3T access for user: %s with %d bundles\n", userEmail, len(bundleIDs))

					// Get all categories that this user has free access to
					freeCategoriesQuery := `
						SELECT category_id 
						FROM free_daerah_3t 
						WHERE user_email = $1 AND is_active = true
					`

					var freeCategoryIDs []string
					err := dbx.SelectContext(ctx, &freeCategoryIDs, freeCategoriesQuery, userEmail)
					if err != nil {
						fmt.Printf("Free categories query error: %v\n", err)
						// Continue without daerah 3T info - non-critical
					}

					// Debug statement
					fmt.Printf("User has free access to %d categories: %v\n", len(freeCategoryIDs), freeCategoryIDs)

					if len(freeCategoryIDs) > 0 {
						// More efficient approach: query all free bundles at once
						freeBundlesQuery := `
							SELECT DISTINCT bi.bundle_id
							FROM exam_bundle_items bi
							JOIN exam_categories ec ON bi.exam_id = ec.exam_id
							JOIN free_daerah_3t fd ON ec.category_id = fd.category_id
							WHERE bi.bundle_id = ANY($1)
							AND fd.user_email = $2
							AND fd.is_active = true
						`

						var freeBundleIDs []string
						err := dbx.SelectContext(ctx, &freeBundleIDs, freeBundlesQuery, pq.Array(bundleIDs), userEmail)
						if err != nil {
							fmt.Printf("Free bundles query error: %v\n", err)
							// Continue without daerah 3T info - non-critical
						} else {
							// Convert the slice to a map for easier lookup
							for _, bundleID := range freeBundleIDs {
								bundleFreeDaerah3TAccess[bundleID] = true
								fmt.Printf("Bundle %s has free access\n", bundleID)
							}
						}
					}
				}

				// Calculate total pages (existing code)
				totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))

				// Create standardized pagination info (existing code)
				paginationInfo := PaginationInfo{
					TotalData:   totalData,
					TotalPages:  totalPages,
					CurrentPage: page,
					PageSize:    pageSize,
				}

				// Create flattened bundles with category information
				flattenedBundles := make([]FlattenedBundleWithCategory, len(bundles))
				for i, bundle := range bundles {
					category, exists := bundleCategories[bundle.ID]

					// NEW: Check if this bundle has free access via daerah 3T
					hasFreeAccess, _ := bundleFreeDaerah3TAccess[bundle.ID]

					// Debug statement
					fmt.Printf("Bundle %s: has free access: %v\n", bundle.ID, hasFreeAccess)

					// Create a copy of the bundle price to modify if needed
					price := bundle.Price
					if hasFreeAccess {
						// If the user has daerah 3T access, set the price to 0
						price = 0
						fmt.Printf("Setting price to 0 for bundle %s\n", bundle.ID)
					}

					// Create flattened bundle (modified)
					flattenedBundles[i] = FlattenedBundleWithCategory{
						ID:                 bundle.ID,
						Name:               bundle.Name,
						Description:        bundle.Description,
						Price:              price, // Use the potentially modified price
						DiscountPercentage: bundle.DiscountPercentage,
						ThumbnailURL:       bundle.ThumbnailURL,
						BannerURL:          bundle.BannerURL,
						Visibility:         bundle.Visibility,
						ValidFrom:          bundle.ValidFrom,
						ValidUntil:         bundle.ValidUntil,
						Metadata:           bundle.Metadata,
						CreatedAt:          bundle.CreatedAt,
						ModifiedAt:         bundle.ModifiedAt,
						UserID:             bundle.UserID,
						IsFreeAccess:       hasFreeAccess, // NEW: Add free access flag
					}

					// Only add category if it exists (existing code)
					if exists {
						flattenedBundles[i].Category = category
					}
				}

				resultCh <- BundlesResult{
					Bundles:          bundles,
					FlattenedBundles: flattenedBundles,
					PaginationInfo:   paginationInfo,
					Error:            nil,
				}
			} else {
				// Handle case with no bundles
				resultCh <- BundlesResult{
					Bundles:          []ExamBundle{},
					FlattenedBundles: []FlattenedBundleWithCategory{},
					PaginationInfo: PaginationInfo{
						TotalData:   0,
						TotalPages:  0,
						CurrentPage: page,
						PageSize:    pageSize,
					},
					Error: nil,
				}
			}
		}()

		// Wait for result or timeout (existing code)
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"bundles":    result.FlattenedBundles,
				"pagination": result.PaginationInfo,
			})
		}
	}
}

// getBundleByID handles retrieving a single bundle with its exams
// getBundleByID handles retrieving a single bundle with its exams
func getBundleByID(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		bundleID := c.Param("bundleId")

		// NEW: Get user email for daerah 3T check
		userEmail := c.Query("email")

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		resultCh := make(chan BundleResult, 1)

		go func() {
			// First get the bundle
			var bundle ExamBundle
			bundleQuery := `
				SELECT id, name, description, price, discount_percentage, thumbnail_url, 
				       banner_url, visibility, valid_from, valid_until, metadata, 
				       created_at, modified_at, user_id
				FROM exam_bundles
				WHERE id = $1
			`

			err := dbx.GetContext(ctx, &bundle, bundleQuery, bundleID)
			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- BundleResult{Error: fmt.Errorf("bundle not found")}
					return
				}
				resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch bundle: %w", err)}
				return
			}

			// Then get all exams in the bundle
			var exams []ExamBundleItem
			examsQuery := `
				SELECT id, bundle_id, exam_id
				FROM exam_bundle_items
				WHERE bundle_id = $1
			`

			err = dbx.SelectContext(ctx, &exams, examsQuery, bundleID)
			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch bundle exams: %w", err)}
				return
			}

			// Get representative category (from the first exam in the bundle)
			var category CategoryInfo
			var hasCategory bool

			if len(exams) > 0 {
				// Get the first exam's category
				categoryQuery := `
					SELECT c.id, c.name
					FROM exam_categories ec
					JOIN categories c ON ec.category_id = c.id
					WHERE ec.exam_id = $1
					LIMIT 1
				`

				err = dbx.GetContext(ctx, &category, categoryQuery, exams[0].ExamID)
				if err != nil && err != sql.ErrNoRows {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch bundle category: %w", err)}
					return
				}

				hasCategory = err != sql.ErrNoRows
			}

			// NEW: Check for daerah 3T access if email is provided
			hasFreeAccess := false
			if userEmail != "" && len(exams) > 0 {
				// Debug statement
				fmt.Printf("Checking daerah 3T access for user: %s with bundle %s\n", userEmail, bundleID)

				// Query to check if the bundle has free access
				freeAccessQuery := `
					SELECT EXISTS (
						SELECT 1
						FROM exam_bundle_items bi
						JOIN exam_categories ec ON bi.exam_id = ec.exam_id
						JOIN free_daerah_3t fd ON ec.category_id = fd.category_id
						WHERE bi.bundle_id = $1
						AND fd.user_email = $2
						AND fd.is_active = true
					) AS has_free_access
				`

				err := dbx.GetContext(ctx, &hasFreeAccess, freeAccessQuery, bundleID, userEmail)
				if err != nil {
					fmt.Printf("Free access check error: %v\n", err)
					// Continue without daerah 3T info - non-critical
				}

				fmt.Printf("Bundle %s has free access: %v\n", bundleID, hasFreeAccess)
			}

			// Set price to 0 if the user has free access
			price := bundle.Price
			if hasFreeAccess {
				price = 0
				fmt.Printf("Setting price to 0 for bundle %s\n", bundleID)
			}

			// Create flattened bundle response
			flattenedBundle := &FlattenedBundleWithCategory{
				ID:                 bundle.ID,
				Name:               bundle.Name,
				Description:        bundle.Description,
				Price:              price, // Use potentially modified price
				DiscountPercentage: bundle.DiscountPercentage,
				ThumbnailURL:       bundle.ThumbnailURL,
				BannerURL:          bundle.BannerURL,
				Visibility:         bundle.Visibility,
				ValidFrom:          bundle.ValidFrom,
				ValidUntil:         bundle.ValidUntil,
				Metadata:           bundle.Metadata,
				CreatedAt:          bundle.CreatedAt,
				ModifiedAt:         bundle.ModifiedAt,
				UserID:             bundle.UserID,
				IsFreeAccess:       hasFreeAccess, // NEW: Add free access flag
			}

			// Only add category if it exists
			if hasCategory {
				flattenedBundle.Category = category
			}

			resultCh <- BundleResult{
				Bundle:          &ExamBundleWithItems{Bundle: bundle, Exams: exams},
				FlattenedBundle: flattenedBundle,
				Error:           nil,
			}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				if result.Error.Error() == "bundle not found" {
					c.JSON(http.StatusNotFound, gin.H{"error": "Bundle not found"})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			// Use flattened bundle in response
			c.JSON(http.StatusOK, gin.H{
				"bundle": result.FlattenedBundle,
				"exams":  result.Bundle.Exams,
			})
		}
	}
}

// createBundle handles creating a new bundle with its exams
func createBundle(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req CreateBundleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Validate exam IDs
		if len(req.ExamIDs) == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "At least one exam is required in the bundle"})
			return
		}

		// Get user ID from URL parameter
		userID := c.Param("userId")
		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		// Convert metadata to JSON
		metadataJSON, err := json.Marshal(req.Metadata)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid metadata format"})
			return
		}

		resultCh := make(chan BundleResult, 1)

		go func() {
			// Start transaction
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to start transaction: %w", err)}
				return
			}
			defer tx.Rollback()

			// Verify user exists
			var userExists bool
			err = tx.GetContext(ctx, &userExists, `SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)`, userID)
			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to verify user: %w", err)}
				return
			}

			if !userExists {
				resultCh <- BundleResult{Error: fmt.Errorf("user not found")}
				return
			}

			// Generate new ID for bundle
			bundleID := lib.GenerateULID()

			// Prepare SQL parameters
			var discountPercentage sql.NullInt32
			if req.DiscountPercentage != nil {
				discountPercentage.Int32 = int32(*req.DiscountPercentage)
				discountPercentage.Valid = true
			}

			var thumbnailURL sql.NullString
			if req.ThumbnailURL != "" {
				thumbnailURL.String = req.ThumbnailURL
				thumbnailURL.Valid = true
			}

			var bannerURL sql.NullString
			if req.BannerURL != "" {
				bannerURL.String = req.BannerURL
				bannerURL.Valid = true
			}

			var validFrom sql.NullTime
			if req.ValidFrom != nil {
				validFrom.Time = *req.ValidFrom
				validFrom.Valid = true
			}

			var validUntil sql.NullTime
			if req.ValidUntil != nil {
				validUntil.Time = *req.ValidUntil
				validUntil.Valid = true
			}

			// Default visibility to DRAFT if not provided
			visibility := req.Visibility
			if visibility == "" {
				visibility = "DRAFT"
			}

			// Insert bundle
			bundleQuery := `
				INSERT INTO exam_bundles (
					id, name, description, price, discount_percentage, 
					thumbnail_url, banner_url, visibility, valid_from, valid_until, 
					metadata, user_id
				) VALUES (
					$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
				) RETURNING id, name, description, price, discount_percentage, 
				thumbnail_url, banner_url, visibility, valid_from, valid_until, 
				metadata, created_at, modified_at, user_id
			`

			var bundle ExamBundle
			err = tx.GetContext(ctx, &bundle, bundleQuery,
				bundleID, req.Name, req.Description, req.Price, discountPercentage,
				thumbnailURL, bannerURL, visibility, validFrom, validUntil,
				string(metadataJSON), userID,
			)
			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to create bundle: %w", err)}
				return
			}

			// Verify all exam IDs exist
			for _, examID := range req.ExamIDs {
				var examExists bool
				err = tx.GetContext(ctx, &examExists, `
					SELECT EXISTS(SELECT 1 FROM available_exams WHERE id = $1)
				`, examID)

				if err != nil {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to verify exam existence: %w", err)}
					return
				}

				if !examExists {
					resultCh <- BundleResult{Error: fmt.Errorf("exam with ID %s not found", examID)}
					return
				}
			}

			// Insert bundle items
			exams := make([]ExamBundleItem, 0, len(req.ExamIDs))
			for _, examID := range req.ExamIDs {
				itemID := lib.GenerateULID()

				_, err = tx.ExecContext(ctx, `
					INSERT INTO exam_bundle_items (id, bundle_id, exam_id)
					VALUES ($1, $2, $3)
				`, itemID, bundleID, examID)

				if err != nil {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to add exam to bundle: %w", err)}
					return
				}

				exams = append(exams, ExamBundleItem{
					ID:       itemID,
					BundleID: bundleID,
					ExamID:   examID,
				})
			}

			// Get category information if available (from first exam)
			var category CategoryInfo
			var hasCategory bool

			if len(req.ExamIDs) > 0 {
				categoryQuery := `
					SELECT c.id, c.name
					FROM exam_categories ec
					JOIN categories c ON ec.category_id = c.id
					WHERE ec.exam_id = $1
					LIMIT 1
				`

				err = tx.GetContext(ctx, &category, categoryQuery, req.ExamIDs[0])
				if err != nil && err != sql.ErrNoRows {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch category: %w", err)}
					return
				}

				hasCategory = err != sql.ErrNoRows
			}

			// Create flattened bundle
			flattenedBundle := &FlattenedBundleWithCategory{
				ID:                 bundle.ID,
				Name:               bundle.Name,
				Description:        bundle.Description,
				Price:              bundle.Price,
				DiscountPercentage: bundle.DiscountPercentage,
				ThumbnailURL:       bundle.ThumbnailURL,
				BannerURL:          bundle.BannerURL,
				Visibility:         bundle.Visibility,
				ValidFrom:          bundle.ValidFrom,
				ValidUntil:         bundle.ValidUntil,
				Metadata:           bundle.Metadata,
				CreatedAt:          bundle.CreatedAt,
				ModifiedAt:         bundle.ModifiedAt,
				UserID:             bundle.UserID,
			}

			// Only add category if it exists
			if hasCategory {
				flattenedBundle.Category = category
			}

			// Commit transaction
			if err := tx.Commit(); err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to commit transaction: %w", err)}
				return
			}

			resultCh <- BundleResult{
				Bundle: &ExamBundleWithItems{
					Bundle: bundle,
					Exams:  exams,
				},
				FlattenedBundle: flattenedBundle,
				Error:           nil,
			}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				if result.Error.Error() == "user not found" {
					c.JSON(http.StatusNotFound, gin.H{"error": result.Error.Error()})
					return
				}
				if result.Error.Error()[0:13] == "exam with ID " {
					c.JSON(http.StatusNotFound, gin.H{"error": result.Error.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			c.JSON(http.StatusCreated, gin.H{
				"message": "Bundle created successfully",
				"bundle":  result.FlattenedBundle,
				"exams":   result.Bundle.Exams,
			})
		}
	}
}

// updateBundle handles updating an existing bundle
func updateBundle(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		bundleID := c.Param("bundleId")
		userID := c.Param("userId")

		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
			return
		}

		var req UpdateBundleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		resultCh := make(chan BundleResult, 1)

		go func() {
			// Start transaction
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to start transaction: %w", err)}
				return
			}
			defer tx.Rollback()

			// Check if bundle exists and belongs to the user
			var checkID string
			err = tx.GetContext(ctx, &checkID, `
				SELECT id FROM exam_bundles 
				WHERE id = $1 AND user_id = $2
			`, bundleID, userID)

			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- BundleResult{Error: fmt.Errorf("bundle not found or not owned by user")}
					return
				}
				resultCh <- BundleResult{Error: fmt.Errorf("failed to verify bundle ownership: %w", err)}
				return
			}

			// Build dynamic update query
			updateParts := make([]string, 0)
			args := make([]interface{}, 0)
			argIndex := 1

			if req.Name != "" {
				updateParts = append(updateParts, fmt.Sprintf("name = $%d", argIndex))
				args = append(args, req.Name)
				argIndex++
			}

			if req.Description != "" {
				updateParts = append(updateParts, fmt.Sprintf("description = $%d", argIndex))
				args = append(args, req.Description)
				argIndex++
			}

			if req.Price != nil {
				updateParts = append(updateParts, fmt.Sprintf("price = $%d", argIndex))
				args = append(args, *req.Price)
				argIndex++
			}

			if req.DiscountPercentage != nil {
				updateParts = append(updateParts, fmt.Sprintf("discount_percentage = $%d", argIndex))
				args = append(args, *req.DiscountPercentage)
				argIndex++
			} else if req.DiscountPercentage == nil && req.Price != nil {
				// If price was updated but discount is nil, set discount to NULL
				updateParts = append(updateParts, "discount_percentage = NULL")
			}

			if req.ThumbnailURL != "" {
				updateParts = append(updateParts, fmt.Sprintf("thumbnail_url = $%d", argIndex))
				args = append(args, req.ThumbnailURL)
				argIndex++
			}

			if req.BannerURL != "" {
				updateParts = append(updateParts, fmt.Sprintf("banner_url = $%d", argIndex))
				args = append(args, req.BannerURL)
				argIndex++
			}

			if req.Visibility != "" {
				updateParts = append(updateParts, fmt.Sprintf("visibility = $%d", argIndex))
				args = append(args, req.Visibility)
				argIndex++
			}

			if req.ValidFrom != nil {
				updateParts = append(updateParts, fmt.Sprintf("valid_from = $%d", argIndex))
				args = append(args, *req.ValidFrom)
				argIndex++
			}

			if req.ValidUntil != nil {
				updateParts = append(updateParts, fmt.Sprintf("valid_until = $%d", argIndex))
				args = append(args, *req.ValidUntil)
				argIndex++
			}

			if req.Metadata != nil {
				metadataJSON, err := json.Marshal(req.Metadata)
				if err != nil {
					resultCh <- BundleResult{Error: fmt.Errorf("invalid metadata format: %w", err)}
					return
				}

				updateParts = append(updateParts, fmt.Sprintf("metadata = $%d", argIndex))
				args = append(args, metadataJSON)
				argIndex++
			}

			// Add modified_at update
			updateParts = append(updateParts, "modified_at = CURRENT_TIMESTAMP")

			// If nothing to update, just continue with exam updates if any
			if len(updateParts) > 1 { // > 1 because we always have modified_at
				// Join update parts with commas
				updatePartsStr := strings.Join(updateParts, ", ")

				// Build the update query
				updateQuery := fmt.Sprintf("UPDATE exam_bundles SET %s WHERE id = $%d",
					updatePartsStr, argIndex)
				args = append(args, bundleID)

				_, err = tx.ExecContext(ctx, updateQuery, args...)
				if err != nil {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to update bundle: %w", err)}
					return
				}
			}

			// Update exam items if provided
			if req.ExamIDs != nil && len(req.ExamIDs) > 0 {
				// Verify all exam IDs exist
				for _, examID := range req.ExamIDs {
					var examExists bool
					err = tx.GetContext(ctx, &examExists, `
						SELECT EXISTS(SELECT 1 FROM available_exams WHERE id = $1)
					`, examID)

					if err != nil {
						resultCh <- BundleResult{Error: fmt.Errorf("failed to verify exam existence: %w", err)}
						return
					}

					if !examExists {
						resultCh <- BundleResult{Error: fmt.Errorf("exam with ID %s not found", examID)}
						return
					}
				}

				// Delete all existing items first
				_, err = tx.ExecContext(ctx, `
					DELETE FROM exam_bundle_items WHERE bundle_id = $1
				`, bundleID)

				if err != nil {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to update bundle items: %w", err)}
					return
				}

				// Insert new items
				for _, examID := range req.ExamIDs {
					itemID := lib.GenerateULID()

					_, err = tx.ExecContext(ctx, `
						INSERT INTO exam_bundle_items (id, bundle_id, exam_id)
						VALUES ($1, $2, $3)
					`, itemID, bundleID, examID)

					if err != nil {
						resultCh <- BundleResult{Error: fmt.Errorf("failed to add exam to bundle: %w", err)}
						return
					}
				}
			}

			// Commit transaction
			if err := tx.Commit(); err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to commit transaction: %w", err)}
				return
			}

			// Fetch updated bundle with items
			var bundle ExamBundle
			err = dbx.GetContext(ctx, &bundle, `
				SELECT id, name, description, price, discount_percentage, thumbnail_url, 
				       banner_url, visibility, valid_from, valid_until, metadata, 
				       created_at, modified_at, user_id
				FROM exam_bundles
				WHERE id = $1
			`, bundleID)

			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch updated bundle: %w", err)}
				return
			}

			var exams []ExamBundleItem
			err = dbx.SelectContext(ctx, &exams, `
				SELECT id, bundle_id, exam_id
				FROM exam_bundle_items
				WHERE bundle_id = $1
			`, bundleID)

			if err != nil {
				resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch updated bundle items: %w", err)}
				return
			}

			// Get category information if available (from first exam)
			var category CategoryInfo
			var hasCategory bool

			if len(exams) > 0 {
				categoryQuery := `
					SELECT c.id, c.name
					FROM exam_categories ec
					JOIN categories c ON ec.category_id = c.id
					WHERE ec.exam_id = $1
					LIMIT 1
				`

				err = dbx.GetContext(ctx, &category, categoryQuery, exams[0].ExamID)
				if err != nil && err != sql.ErrNoRows {
					resultCh <- BundleResult{Error: fmt.Errorf("failed to fetch category: %w", err)}
					return
				}

				hasCategory = err != sql.ErrNoRows
			}

			// Create flattened bundle
			flattenedBundle := &FlattenedBundleWithCategory{
				ID:                 bundle.ID,
				Name:               bundle.Name,
				Description:        bundle.Description,
				Price:              bundle.Price,
				DiscountPercentage: bundle.DiscountPercentage,
				ThumbnailURL:       bundle.ThumbnailURL,
				BannerURL:          bundle.BannerURL,
				Visibility:         bundle.Visibility,
				ValidFrom:          bundle.ValidFrom,
				ValidUntil:         bundle.ValidUntil,
				Metadata:           bundle.Metadata,
				CreatedAt:          bundle.CreatedAt,
				ModifiedAt:         bundle.ModifiedAt,
				UserID:             bundle.UserID,
			}

			// Only add category if it exists
			if hasCategory {
				flattenedBundle.Category = category
			}

			resultCh <- BundleResult{
				Bundle: &ExamBundleWithItems{
					Bundle: bundle,
					Exams:  exams,
				},
				FlattenedBundle: flattenedBundle,
				Error:           nil,
			}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				if result.Error.Error() == "bundle not found or not owned by user" {
					c.JSON(http.StatusNotFound, gin.H{"error": result.Error.Error()})
					return
				}
				if result.Error.Error()[0:13] == "exam with ID " {
					c.JSON(http.StatusNotFound, gin.H{"error": result.Error.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Bundle updated successfully",
				"bundle":  result.FlattenedBundle,
				"exams":   result.Bundle.Exams,
			})
		}
	}
}

// deleteBundle handles deleting a bundle
// deleteBundle handles deleting a bundle
func deleteBundle(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		bundleID := c.Param("bundleId")
		userID := c.Param("userId")

		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		resultCh := make(chan error, 1)

		go func() {
			// Start transaction
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- fmt.Errorf("failed to start transaction: %w", err)
				return
			}
			defer tx.Rollback()

			// Check if bundle exists and belongs to the user
			var checkID string
			err = tx.GetContext(ctx, &checkID, `
				SELECT id FROM exam_bundles 
				WHERE id = $1 AND user_id = $2
			`, bundleID, userID)

			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- fmt.Errorf("bundle not found or not owned by user")
					return
				}
				resultCh <- fmt.Errorf("failed to verify bundle ownership: %w", err)
				return
			}

			// Check if bundle is referenced in orders
			var orderCount int
			err = tx.GetContext(ctx, &orderCount, `
				SELECT COUNT(*) FROM orders WHERE bundle_id = $1
			`, bundleID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to check bundle references: %w", err)
				return
			}

			if orderCount > 0 {
				resultCh <- fmt.Errorf("cannot delete bundle that has associated orders")
				return
			}

			// Delete bundle items first (ON DELETE CASCADE would handle this, but being explicit)
			_, err = tx.ExecContext(ctx, `
				DELETE FROM exam_bundle_items WHERE bundle_id = $1
			`, bundleID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to delete bundle items: %w", err)
				return
			}

			// Delete the bundle
			_, err = tx.ExecContext(ctx, `
				DELETE FROM exam_bundles WHERE id = $1
			`, bundleID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to delete bundle: %w", err)
				return
			}

			// Commit transaction
			if err := tx.Commit(); err != nil {
				resultCh <- fmt.Errorf("failed to commit transaction: %w", err)
				return
			}

			resultCh <- nil
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case err := <-resultCh:
			if err != nil {
				if err.Error() == "bundle not found or not owned by user" {
					c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
					return
				}
				if err.Error() == "cannot delete bundle that has associated orders" {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Bundle deleted successfully",
			})
		}
	}
}

// addExamToBundle adds an exam to a bundle
func addExamToBundle(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		bundleID := c.Param("bundleId")
		userID := c.Param("userId")

		var req struct {
			ExamID string `json:"examId" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		if userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		resultCh := make(chan error, 1)

		go func() {
			// Start transaction
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- fmt.Errorf("failed to start transaction: %w", err)
				return
			}
			defer tx.Rollback()

			// Check if bundle exists and belongs to the user
			var checkID string
			err = tx.GetContext(ctx, &checkID, `
				SELECT id FROM exam_bundles 
				WHERE id = $1 AND user_id = $2
			`, bundleID, userID)

			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- fmt.Errorf("bundle not found or not owned by user")
					return
				}
				resultCh <- fmt.Errorf("failed to verify bundle ownership: %w", err)
				return
			}

			// Check if exam exists
			var examExists bool
			err = tx.GetContext(ctx, &examExists, `
				SELECT EXISTS(SELECT 1 FROM available_exams WHERE id = $1)
			`, req.ExamID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to verify exam existence: %w", err)
				return
			}

			if !examExists {
				resultCh <- fmt.Errorf("exam not found")
				return
			}

			// Check if exam is already in bundle
			var examInBundle bool
			err = tx.GetContext(ctx, &examInBundle, `
				SELECT EXISTS(SELECT 1 FROM exam_bundle_items 
				WHERE bundle_id = $1 AND exam_id = $2)
			`, bundleID, req.ExamID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to check if exam is in bundle: %w", err)
				return
			}

			if examInBundle {
				resultCh <- fmt.Errorf("exam is already in this bundle")
				return
			}

			// Add exam to bundle
			itemID := lib.GenerateULID()
			_, err = tx.ExecContext(ctx, `
				INSERT INTO exam_bundle_items (id, bundle_id, exam_id)
				VALUES ($1, $2, $3)
			`, itemID, bundleID, req.ExamID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to add exam to bundle: %w", err)
				return
			}

			// Commit transaction
			if err := tx.Commit(); err != nil {
				resultCh <- fmt.Errorf("failed to commit transaction: %w", err)
				return
			}

			resultCh <- nil
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case err := <-resultCh:
			if err != nil {
				if err.Error() == "bundle not found or not owned by user" {
					c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
					return
				}
				if err.Error() == "exam not found" {
					c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
					return
				}
				if err.Error() == "exam is already in this bundle" {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Exam added to bundle successfully",
			})
		}
	}
}

// removeExamFromBundle removes an exam from a bundle
func removeExamFromBundle(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		bundleID := c.Param("bundleId")
		examID := c.Param("examId")
		userID := c.Param("userId")

		if userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		resultCh := make(chan error, 1)

		go func() {
			// Start transaction
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- fmt.Errorf("failed to start transaction: %w", err)
				return
			}
			defer tx.Rollback()

			// Check if bundle exists and belongs to the user
			var checkID string
			err = tx.GetContext(ctx, &checkID, `
				SELECT id FROM exam_bundles 
				WHERE id = $1 AND user_id = $2
			`, bundleID, userID)

			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- fmt.Errorf("bundle not found or not owned by user")
					return
				}
				resultCh <- fmt.Errorf("failed to verify bundle ownership: %w", err)
				return
			}

			// Check if exam is in bundle
			var itemID string
			err = tx.GetContext(ctx, &itemID, `
				SELECT id FROM exam_bundle_items 
				WHERE bundle_id = $1 AND exam_id = $2
			`, bundleID, examID)

			if err != nil {
				if err == sql.ErrNoRows {
					resultCh <- fmt.Errorf("exam is not in this bundle")
					return
				}
				resultCh <- fmt.Errorf("failed to check if exam is in bundle: %w", err)
				return
			}

			// Count total exams in bundle
			var examCount int
			err = tx.GetContext(ctx, &examCount, `
				SELECT COUNT(*) FROM exam_bundle_items 
				WHERE bundle_id = $1
			`, bundleID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to count exams in bundle: %w", err)
				return
			}

			// Prevent removing the last exam
			if examCount <= 1 {
				resultCh <- fmt.Errorf("cannot remove the last exam from a bundle")
				return
			}

			// Remove exam from bundle
			_, err = tx.ExecContext(ctx, `
				DELETE FROM exam_bundle_items 
				WHERE id = $1
			`, itemID)

			if err != nil {
				resultCh <- fmt.Errorf("failed to remove exam from bundle: %w", err)
				return
			}

			// Commit transaction
			if err := tx.Commit(); err != nil {
				resultCh <- fmt.Errorf("failed to commit transaction: %w", err)
				return
			}

			resultCh <- nil
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case err := <-resultCh:
			if err != nil {
				if err.Error() == "bundle not found or not owned by user" {
					c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
					return
				}
				if err.Error() == "exam is not in this bundle" {
					c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
					return
				}
				if err.Error() == "cannot remove the last exam from a bundle" {
					c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Exam removed from bundle successfully",
			})
		}
	}
}
