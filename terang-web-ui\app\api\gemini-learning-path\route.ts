import { NextRequest, NextResponse } from 'next/server';

// TypeScript interfaces
interface GeminiRequestBody {
  contents: Array<{
    parts: Array<{
      text: string;
    }>;
  }>;
  generationConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
    thinkingConfig?: {
      thinkingBudget: number;
    };
  };
  safetySettings: Array<{
    category: string;
    threshold: string;
  }>;
}

interface GeminiResponse {
  candidates?: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason?: string;
  }>;
}

interface LearningStep {
  title: string;
  description: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
  resources: string[];
  exercises: string[];
}

interface LearningPathData {
  steps: LearningStep[];
  connections: string[];
  totalTime: string;
}

interface APIRequest {
  prompt: string;
}

const GEMINI_API_KEY = process.env.GOOGLE_GEMINI_API_KEY;
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent';

// Updated retry configuration
const MAX_RETRIES = 4;
const RETRY_DELAY_MS = 1000;

// Enhanced JSON extraction function
function extractAndRepairJSON(text: string): string {
  console.log('Starting JSON extraction from text length:', text.length);
  
  let cleaned = text.trim();
  
  // Remove any markdown formatting
  cleaned = cleaned.replace(/^```json\s*/i, '').replace(/\s*```$/i, '');
  cleaned = cleaned.replace(/^```\s*/i, '').replace(/\s*```$/i, '');
  
  // First, try to find a complete JSON object
  let jsonStart = -1;
  let jsonEnd = -1;
  let braceCount = 0;
  let inString = false;
  let escaped = false;
  
  for (let i = 0; i < cleaned.length; i++) {
    const char = cleaned[i];
    
    if (escaped) {
      escaped = false;
      continue;
    }
    
    if (char === '\\') {
      escaped = true;
      continue;
    }
    
    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }
    
    if (!inString) {
      if (char === '{') {
        if (jsonStart === -1) {
          jsonStart = i;
        }
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount === 0 && jsonStart !== -1) {
          jsonEnd = i;
          break;
        }
      }
    }
  }
  
  if (jsonStart !== -1 && jsonEnd !== -1) {
    const extracted = cleaned.substring(jsonStart, jsonEnd + 1);
    console.log('Found complete JSON object');
    try {
      JSON.parse(extracted);
      return extracted;
    } catch (error) {
      console.log('Complete JSON needs repair');
      return repairIncompleteJSON(extracted);
    }
  }
  
  // If no complete JSON found, try to repair from the first brace
  const possibleJsonStart = cleaned.indexOf('{');
  if (possibleJsonStart !== -1) {
    const remaining = cleaned.substring(possibleJsonStart);
    console.log('Attempting repair on truncated JSON from first brace');
    return repairIncompleteJSON(remaining);
  }
  
  throw new Error('No valid JSON structure found in response');
}

function repairIncompleteJSON(jsonStr: string): string {
  console.log('Attempting to repair JSON of length:', jsonStr.length);
  
  let repaired = jsonStr.trim();
  
  // Handle truncated strings by closing any open quotes
  let inString = false;
  let escaped = false;
  let lastValidPosition = 0;
  
  for (let i = 0; i < repaired.length; i++) {
    const char = repaired[i];
    
    if (escaped) {
      escaped = false;
      continue;
    }
    
    if (char === '\\') {
      escaped = true;
      continue;
    }
    
    if (char === '"' && !escaped) {
      inString = !inString;
      if (!inString) {
        lastValidPosition = i;
      }
    }
    
    if (!inString && (char === ',' || char === ':' || char === '{' || char === '}' || char === '[' || char === ']')) {
      lastValidPosition = i;
    }
  }
  
  // If we're in the middle of a string, close it
  if (inString) {
    repaired += '"';
    console.log('Closed unclosed string');
  }
  
  // Remove any trailing incomplete content
  const patterns = [
    /,\s*$/,           // trailing comma
    /:\s*$/,           // trailing colon
    /,\s*"[^"]*$/,     // incomplete key after comma
    /:\s*"[^"]*$/,     // incomplete value after colon
    /,\s*\{[^}]*$/,    // incomplete object after comma
  ];
  
  for (const pattern of patterns) {
    if (pattern.test(repaired)) {
      const match = repaired.match(pattern);
      if (match) {
        repaired = repaired.substring(0, match.index);
        console.log('Removed incomplete content:', match[0]);
        break;
      }
    }
  }
  
  // Balance brackets
  let bracketStack: string[] = [];
  inString = false;
  escaped = false;
  
  for (let i = 0; i < repaired.length; i++) {
    const char = repaired[i];
    
    if (escaped) {
      escaped = false;
      continue;
    }
    
    if (char === '\\') {
      escaped = true;
      continue;
    }
    
    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }
    
    if (!inString) {
      if (char === '{') {
        bracketStack.push('}');
      } else if (char === '[') {
        bracketStack.push(']');
      } else if (char === '}' || char === ']') {
        if (bracketStack.length > 0 && bracketStack[bracketStack.length - 1] === char) {
          bracketStack.pop();
        }
      }
    }
  }
  
  // Close any remaining open brackets
  while (bracketStack.length > 0) {
    const closingBracket = bracketStack.pop();
    repaired += closingBracket;
    console.log('Added closing bracket:', closingBracket);
  }
  
  try {
    JSON.parse(repaired);
    console.log('JSON repair successful');
    return repaired;
  } catch (error) {
    console.log('JSON repair failed:', error);
    throw new Error(`Failed to repair JSON: ${error}`);
  }
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function callGeminiWithRetry(requestBody: GeminiRequestBody, retryCount = 0): Promise<GeminiResponse> {
  try {
    console.log(`Attempting Gemini API call (attempt ${retryCount + 1}/${MAX_RETRIES + 1})`);
    
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Gemini API Error:', errorData);
      throw new Error(`Gemini API returned ${response.status}: ${errorData}`);
    }

    const geminiResponse: GeminiResponse = await response.json();
    
    if (!geminiResponse.candidates || geminiResponse.candidates.length === 0) {
      throw new Error('No candidates returned from Gemini API');
    }

    const generatedText: string = geminiResponse.candidates[0].content.parts[0].text;
    const finishReason = geminiResponse.candidates[0].finishReason;
    
    console.log('Raw Gemini response length:', generatedText.length);
    console.log('Finish reason:', finishReason);
    
    // Check if response was truncated
    if (finishReason === 'MAX_TOKENS') {
      throw new Error('Response was truncated due to token limit');
    }

    return geminiResponse;
  } catch (error) {
    console.error(`Gemini API call failed (attempt ${retryCount + 1}):`, error);
    
    if (retryCount < MAX_RETRIES) {
      const delay = RETRY_DELAY_MS * Math.pow(2, retryCount);
      console.log(`Retrying in ${delay}ms...`);
      await sleep(delay);
      return callGeminiWithRetry(requestBody, retryCount + 1);
    } else {
      throw error;
    }
  }
}

async function parseWithRetry(prompt: string, retryCount = 0): Promise<LearningPathData> {
  const enhancedPrompt = `
    ${prompt}

    CRITICAL INSTRUCTIONS FOR COMPLETE RESPONSE:
    - You MUST respond with ONLY valid, complete JSON
    - NO markdown formatting, NO explanations, NO additional text
    - Create exactly 3 learning steps with COMPLETE information
    - Use Bahasa Indonesia casual style (aku-kamu) for descriptions
    - Focus specifically on LPDP interview preparation
    - Ensure ALL fields are properly filled and quoted
    - NO truncation allowed - complete the entire JSON structure
    - IMPORTANT: You need to give description language based on transcript language, e.g. EN or ID. The response should be EN or ID and follow the transcript language!

    FORMATTING REQUIREMENTS:
    - Use \\n for line breaks within descriptions
    - Keep descriptions detailed but manageable (300-500 words each)
    - Use bullet-style formatting with \\n• for key points
    - Include specific examples from interview transcripts
    - Make content actionable and practical

    Return this EXACT JSON structure (MUST be complete):
    {
      "steps": [
        {
          "title": "Langkah 1: [Title Here]",
          "description": "Detailed description with context.\\n\\nPoin-poin utama:\\n• First key point with specific example\\n• Second key point with improvement needed\\n• Third key point with target achievement\\n\\n> **Contoh perbaikan dari transkrip:**\\n> ❌ **Yang perlu diperbaiki (timestamp):** 'original problematic quote from transcript'\\n> ✅ **Versi yang lebih baik:** 'improved version with specific details'\\n> 💡 **Kenapa lebih baik:** explanation of why this improvement works for LPDP interviews",
          "difficulty": "Beginner",
          "estimatedTime": "1 minggu",
          "resources": ["Resource 1", "Resource 2", "Resource 3"],
          "exercises": ["Exercise 1", "Exercise 2", "Exercise 3"]
        },
        {
          "title": "Langkah 2: [Title Here]",
          "description": "Detailed description with context.\\n\\nPoin-poin utama:\\n• First key point with specific example\\n• Second key point with improvement needed\\n• Third key point with target achievement\\n\\n> **Contoh perbaikan dari transkrip:**\\n> ❌ **Yang perlu diperbaiki (timestamp):** 'exact quote from transcript that needs improvement'\\n> ✅ **Versi yang lebih baik:** 'enhanced version with concrete examples and STAR method'\\n> 💡 **Kenapa lebih baik:** explanation focusing on LPDP criteria and effectiveness",
          "difficulty": "Intermediate", 
          "estimatedTime": "1 minggu",
          "resources": ["Resource 1", "Resource 2", "Resource 3"],
          "exercises": ["Exercise 1", "Exercise 2", "Exercise 3"]
        },
        {
          "title": "Langkah 3: [Title Here]",
          "description": "Detailed description with context.\\n\\nPoin-poin utama:\\n• First key point with specific example\\n• Second key point with improvement needed\\n• Third key point with target achievement\\n\\n> **Contoh perbaikan dari transkrip:**\\n> ❌ **Yang perlu diperbaiki (timestamp):** 'problematic quote from interview transcript'\\n> ✅ **Versi yang lebih baik:** 'enhanced version showing clear improvement strategy'\\n> 💡 **Kenapa lebih baik:** specific explanation of how this addresses LPDP evaluation criteria",
          "difficulty": "Advanced",
          "estimatedTime": "1 minggu", 
          "resources": ["Resource 1", "Resource 2", "Resource 3"],
          "exercises": ["Exercise 1", "Exercise 2", "Exercise 3"]
        }
      ],
      "connections": ["Communication Skills", "Interview Preparation", "Storytelling", "Confidence Building"],
      "totalTime": "3 minggu"
    }

    IMPORTANT: Complete the entire JSON structure. Do not truncate or leave incomplete.
    `;

  // Significantly increase token limits to prevent truncation
  const baseTokens = 8192; // Increased base tokens
  const maxTokens = Math.min(8192, baseTokens + (retryCount * 512));
  const temperature = Math.max(0.1, 0.4 - (retryCount * 0.1));

  const requestBody: GeminiRequestBody = {
    contents: [
      {
        parts: [
          {
            text: enhancedPrompt
          }
        ]
      }
    ],
    generationConfig: {
      temperature,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: maxTokens, // Much higher token limit
    },
    safetySettings: [
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: "BLOCK_ONLY_HIGH"
      },
      {
        category: "HARM_CATEGORY_HATE_SPEECH", 
        threshold: "BLOCK_ONLY_HIGH"
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: "BLOCK_ONLY_HIGH"
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: "BLOCK_ONLY_HIGH"
      }
    ]
  };

  try {
    const geminiResponse = await callGeminiWithRetry(requestBody);
    const generatedText: string = geminiResponse.candidates![0].content.parts[0].text;
    const finishReason = geminiResponse.candidates![0].finishReason;
    
    console.log(`Parse attempt ${retryCount + 1}: Response length:`, generatedText.length);
    console.log(`Parse attempt ${retryCount + 1}: Finish reason:`, finishReason);
    
    // Check for truncation at API level
    if (finishReason === 'MAX_TOKENS') {
      throw new Error('Response was truncated by Gemini API due to token limit');
    }
    
    const jsonString: string = extractAndRepairJSON(generatedText);
    const parsedData: LearningPathData = JSON.parse(jsonString) as LearningPathData;
    
    // Enhanced validation
    if (!parsedData.steps || !Array.isArray(parsedData.steps) || parsedData.steps.length !== 3) {
      throw new Error(`Invalid steps array: expected 3 steps, got ${parsedData.steps?.length || 0}`);
    }
    
    // Validate each step thoroughly
    parsedData.steps = parsedData.steps.map((step, index) => {
      if (!step.title || !step.description || step.description.length < 50) {
        throw new Error(`Step ${index + 1} has incomplete title or description`);
      }
      
      if (!step.resources || !Array.isArray(step.resources) || step.resources.length === 0) {
        throw new Error(`Step ${index + 1} missing resources array`);
      }
      
      if (!step.exercises || !Array.isArray(step.exercises) || step.exercises.length === 0) {
        throw new Error(`Step ${index + 1} missing exercises array`);
      }
      
      return {
        title: step.title.trim(),
        description: step.description.trim(),
        difficulty: (['Beginner', 'Intermediate', 'Advanced'].includes(step.difficulty)) 
          ? step.difficulty as 'Beginner' | 'Intermediate' | 'Advanced'
          : 'Intermediate',
        estimatedTime: step.estimatedTime || '1 minggu',
        resources: step.resources.filter(r => r && r.trim().length > 0),
        exercises: step.exercises.filter(e => e && e.trim().length > 0)
      };
    });
    
    if (!Array.isArray(parsedData.connections) || parsedData.connections.length === 0) {
      throw new Error('Missing or empty connections array');
    }
    
    if (!parsedData.totalTime || parsedData.totalTime.trim().length === 0) {
      throw new Error('Missing totalTime field');
    }
    
    console.log(`Successfully parsed complete JSON with ${parsedData.steps.length} steps on attempt ${retryCount + 1}`);
    
    // Log the total content size for verification
    const totalDescriptionLength = parsedData.steps.reduce((total, step) => total + step.description.length, 0);
    console.log(`Total description content length: ${totalDescriptionLength} characters`);
    
    return parsedData;
    
  } catch (error) {
    console.error(`Parse attempt ${retryCount + 1} failed:`, error);
    
    if (retryCount < MAX_RETRIES) {
      console.log(`Retrying parse (attempt ${retryCount + 2}/${MAX_RETRIES + 1})...`);
      await sleep(1000 + (retryCount * 500));
      return parseWithRetry(prompt, retryCount + 1);
    } else {
      throw new Error(`Failed to generate complete response after ${MAX_RETRIES + 1} attempts. Last error: ${error}`);
    }
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: APIRequest = await request.json();
    const { prompt } = body;
    
    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      );
    }
    
    const parsedData = await parseWithRetry(prompt);

    return NextResponse.json({
      success: true,
      data: parsedData,
      message: 'Learning path generated successfully without truncation'
    });

  } catch (error: unknown) {
    console.error('Final API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      message: 'Failed to generate complete learning path'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  return NextResponse.json({
    message: 'Enhanced Gemini Learning Path API - Anti-Truncation Version',
    status: 'Ready',
    improvements: [
      'Increased maxOutputTokens to 8192 to prevent truncation',
      'Enhanced validation for complete responses',
      'Better error detection for truncated responses', 
      'Improved JSON repair for partial content',
      'Stricter content validation requirements',
      'Simplified but clear before/after formatting'
    ],
    configuration: {
      maxRetries: MAX_RETRIES,
      maxTokens: '8192 (expandable)',
      truncationPrevention: true
    }
  });
}