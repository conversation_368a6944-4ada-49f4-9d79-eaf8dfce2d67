-- Add target_score and learning_challenges columns to user_demographics table
ALTER TABLE user_demographics 
ADD COLUMN target_score INTEGER,
ADD COLUMN learning_challenges JSONB;

-- Add comment to explain the fields
COMMENT ON COLUMN user_demographics.target_score IS 'Target exam score the user aims to achieve';
COMMENT ON COLUMN user_demographics.learning_challenges IS 'Specific challenges faced during learning (stored as JSONB array)';

-- Add name columns to user_demographics table
ALTER TABLE user_demographics 
ADD COLUMN province_name VARCHAR(100),
ADD COLUMN city_name VARCHAR(100),
ADD COLUMN district_name VARCHAR(100),
ADD COLUMN village_name VARCHAR(100),
ADD COLUMN education_level_name VARCHAR(100),
ADD COLUMN program_study_name VARCHAR(150),
ADD COLUMN target_university VARCHAR(255),
ADD COLUMN target_major VARCHAR(255);

-- Add indexes for the new name columns
CREATE INDEX idx_user_demographics_province_name ON user_demographics(province_name);
CREATE INDEX idx_user_demographics_city_name ON user_demographics(city_name);