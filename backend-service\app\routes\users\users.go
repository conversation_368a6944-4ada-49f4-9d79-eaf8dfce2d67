package users

import (
	"bytes"
	"context"
	"crypto/md5"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"path"
	"strings"

	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"github.com/microcosm-cc/bluemonday"
	"github.com/redis/go-redis/v9"
	users "github.com/terang-ai/backend-service/controller/users"
	"google.golang.org/api/iterator"
)

var policy = bluemonday.StrictPolicy()

// Debug helper function
func debugLog(prefix string, data string) {
	log.Printf("[DEBUG] %s: %s", prefix, data)
}

type PasswordCompareRequest struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type PasswordCompareResponse struct {
	Match bool   `json:"match"`
	Error string `json:"error,omitempty"`
}

type UserResponse struct {
	ID              string         `json:"id"`
	Username        string         `json:"username"`
	Email           string         `json:"email"`
	FirstName       sql.NullString `json:"-"` // Hide the original field
	LastName        sql.NullString `json:"-"`
	Picture         sql.NullString `json:"-"`
	Banner          sql.NullString `json:"-"`
	Role            string         `json:"role"`
	Tier            string         `json:"tier"`
	IsVerified      bool           `json:"is_verified"`
	IsBanned        bool           `json:"is_banned"`
	PersonalWebsite sql.NullString `json:"-"`
	PhoneNumber     sql.NullString `json:"-"`
	Address         sql.NullString `json:"-"`
	Country         sql.NullString `json:"-"`
	Bio             sql.NullString `json:"-"`
	Preferences     []byte         `json:"preferences,omitempty"`
	SocialLinks     []byte         `json:"social_links,omitempty"`
}

func (u UserResponse) MarshalJSON() ([]byte, error) {
	type Alias UserResponse // Prevent recursive MarshalJSON calls

	return json.Marshal(&struct {
		*Alias
		FirstName       *string `json:"first_name,omitempty"`
		LastName        *string `json:"last_name,omitempty"`
		Picture         *string `json:"picture,omitempty"`
		Banner          *string `json:"banner,omitempty"`
		PersonalWebsite *string `json:"personal_website,omitempty"`
		PhoneNumber     *string `json:"phone_number,omitempty"`
		Address         *string `json:"address,omitempty"`
		Country         *string `json:"country,omitempty"`
		Bio             *string `json:"bio,omitempty"`
	}{
		Alias:           (*Alias)(&u),
		FirstName:       nullStringToPtr(u.FirstName),
		LastName:        nullStringToPtr(u.LastName),
		Picture:         nullStringToPtr(u.Picture),
		Banner:          nullStringToPtr(u.Banner),
		PersonalWebsite: nullStringToPtr(u.PersonalWebsite),
		PhoneNumber:     nullStringToPtr(u.PhoneNumber),
		Address:         nullStringToPtr(u.Address),
		Country:         nullStringToPtr(u.Country),
		Bio:             nullStringToPtr(u.Bio),
	})
}

// Helper function to convert sql.NullString to *string
func nullStringToPtr(n sql.NullString) *string {
	if !n.Valid {
		return nil
	}
	return &n.String
}

func RegisterRoutes(r *gin.Engine, db *sql.DB, redis *redis.Client) {
	controller := users.NewUserController(db, redis)
	controllerUserToken := users.NewUserVerificationTokenController(db, redis)

	// v1 routes remain unchanged
	v1 := r.Group("/v1")
	{
		v1.POST("/users", controller.InsertUser)
		v1.GET("/users", controller.GetAllUsers)
		v1.GET("/users/:id", controller.GetOneUser)
		v1.PUT("/users/:id", controller.UpdateUser)
		v1.DELETE("/users/:id", controller.DeleteUser)
		v1.GET("/users/emails/:email", controller.GetOneUserByEmail)
		v1.PUT("/users/emails/:email", controller.UpdateUserByEmail)
		v1.POST("/users/tokens", controllerUserToken.InsertUserVerificationToken)
		v1.GET("/users/tokens/:token", controllerUserToken.GetUserVerificationByToken)
		v1.PUT("/users/tokens/:token", controllerUserToken.UpdateUserVerificationByToken)

		// Add profile picture upload endpoint
		v1.POST("/users/:id/profile-picture", uploadProfilePicture(db))
	}

	// API for serving storage files
	r.GET("/api/storage/users/images/profile/:userId/:fileName", serveStorageFile())

	// v0 routes
	v0 := r.Group("/v0")
	{
		v0.POST("/users/compare-password", comparePassword(db))
		v0.GET("/users/username/:username", getUserByUsername(db))
	}
}

// uploadProfilePicture handles uploading user profile pictures to Google Cloud Storage
func uploadProfilePicture(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from URL parameter
		userID := policy.Sanitize(c.Param("id"))
		debugLog("Uploading profile picture for user", userID)

		// Check if user exists
		var exists bool
		err := db.QueryRow("SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)", userID).Scan(&exists)
		if err != nil {
			debugLog("Database error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"status": "failed",
				"error":  "Internal server error",
			})
			return
		}

		if !exists {
			c.JSON(http.StatusNotFound, gin.H{
				"status": "failed",
				"error":  "User not found",
			})
			return
		}

		// Get file from form data
		file, header, err := c.Request.FormFile("file")
		if err != nil {
			debugLog("File upload error", err.Error())
			c.JSON(http.StatusBadRequest, gin.H{
				"status": "failed",
				"error":  "Invalid file upload",
			})
			return
		}
		defer file.Close()

		// Check file size (limit to 5MB)
		if header.Size > 5*1024*1024 {
			c.JSON(http.StatusBadRequest, gin.H{
				"status": "failed",
				"error":  "File too large. Maximum size is 5MB",
			})
			return
		}

		// Check file type
		fileType := header.Header.Get("Content-Type")
		if !isAllowedImageType(fileType) {
			c.JSON(http.StatusBadRequest, gin.H{
				"status": "failed",
				"error":  "Invalid file type. Only JPG, JPEG, and PNG are allowed",
			})
			return
		}

		// Read file content
		fileBytes, err := io.ReadAll(file)
		if err != nil {
			debugLog("File read error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"status": "failed",
				"error":  "Unable to read file",
			})
			return
		}

		// Generate MD5 hash for filename
		fileHash := md5.Sum(fileBytes)
		fileName := hex.EncodeToString(fileHash[:])

		// Add extension based on content type
		extension := getExtensionFromContentType(fileType)
		fileName = fileName + extension

		// Delete existing profile pictures for this user
		err = deleteOldProfilePictures(c.Request.Context(), userID, fileName)
		if err != nil {
			debugLog("Error deleting old profile pictures", err.Error())
			// Continue with the upload even if deletion fails
		}

		// Upload to Google Cloud Storage
		imageURL, err := uploadToGCS(c.Request.Context(), fileBytes, userID, fileName)
		if err != nil {
			debugLog("GCS upload error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"status": "failed",
				"error":  "Failed to upload image",
			})
			return
		}

		// Update user's profile picture in database
		_, err = db.Exec("UPDATE users SET picture = $1 WHERE id = $2", imageURL, userID)
		if err != nil {
			debugLog("Database update error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"status": "failed",
				"error":  "Failed to update user profile",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Profile picture updated successfully",
			"data": gin.H{
				"picture_url": imageURL,
			},
		})
	}
}

// deleteOldProfilePictures deletes all profile pictures for a user except the new one
func deleteOldProfilePictures(ctx context.Context, userID, newFileName string) error {
	bucketName := "terang-ai-assets"
	prefix := fmt.Sprintf("users/images/profile/%s/", userID)

	// Create client
	client, err := storage.NewClient(ctx)
	if err != nil {
		return fmt.Errorf("failed to create client: %v", err)
	}
	defer client.Close()

	// Get bucket handle
	bucket := client.Bucket(bucketName)

	// List objects in the user's profile directory
	it := bucket.Objects(ctx, &storage.Query{
		Prefix: prefix,
	})

	// Delete all objects except the new file
	for {
		attrs, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error listing objects: %v", err)
		}

		// Extract just the filename from the full path
		objectName := path.Base(attrs.Name)

		// Skip the new file if it somehow already exists
		if objectName == newFileName {
			continue
		}

		// Delete the object
		if err := bucket.Object(attrs.Name).Delete(ctx); err != nil {
			return fmt.Errorf("error deleting object %s: %v", attrs.Name, err)
		}

		debugLog("Deleted old profile picture", attrs.Name)
	}

	return nil
}

// uploadToGCS uploads a file to Google Cloud Storage
func uploadToGCS(ctx context.Context, fileBytes []byte, userID, fileName string) (string, error) {
	bucketName := "terang-ai-assets"
	objectPath := fmt.Sprintf("users/images/profile/%s/%s", userID, fileName)

	// Create a client using application default credentials
	// This will use credentials from the environment, metadata server, or workload identity
	client, err := storage.NewClient(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to create client: %v", err)
	}
	defer client.Close()

	// Get a handle to the bucket
	bucket := client.Bucket(bucketName)

	// Get a handle to the object
	obj := bucket.Object(objectPath)

	// Create a writer
	w := obj.NewWriter(ctx)
	w.ContentType = getContentTypeFromExtension(path.Ext(fileName))
	w.CacheControl = "public, max-age=86400" // Cache for 1 day

	// Write the file bytes to the object
	if _, err := w.Write(fileBytes); err != nil {
		return "", fmt.Errorf("failed to write to object: %v", err)
	}

	// Close the writer to flush and save the object
	if err := w.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %v", err)
	}

	// For environments without signed URL ability, return a storage URL
	// The URL will work if either:
	// 1. Your application has a proxy/middleware to serve these files
	// 2. The object has some existing access control rules allowing it to be accessed
	return fmt.Sprintf("/api/storage/users/images/profile/%s/%s", userID, fileName), nil
}

// isAllowedImageType checks if the content type is allowed
func isAllowedImageType(contentType string) bool {
	allowedTypes := map[string]bool{
		"image/jpeg": true,
		"image/jpg":  true,
		"image/png":  true,
	}

	return allowedTypes[contentType]
}

// getExtensionFromContentType returns the file extension based on content type
func getExtensionFromContentType(contentType string) string {
	switch contentType {
	case "image/jpeg", "image/jpg":
		return ".jpg"
	case "image/png":
		return ".png"
	default:
		return ""
	}
}

// getContentTypeFromExtension returns the content type based on file extension
func getContentTypeFromExtension(ext string) string {
	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	default:
		return "application/octet-stream"
	}
}

func comparePassword(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Read the raw body first for debugging
		rawData, err := io.ReadAll(c.Request.Body)
		if err != nil {
			debugLog("Error reading raw body", err.Error())
			c.JSON(http.StatusBadRequest, PasswordCompareResponse{
				Match: false,
				Error: "Invalid request data",
			})
			return
		}
		debugLog("Raw request", string(rawData))

		// Restore the body
		c.Request.Body = io.NopCloser(bytes.NewBuffer(rawData))

		var req PasswordCompareRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			debugLog("JSON binding error", err.Error())
			c.JSON(http.StatusBadRequest, PasswordCompareResponse{
				Match: false,
				Error: "Invalid request format",
			})
			return
		}

		// Debug original values
		debugLog("Original Email", req.Email)
		debugLog("Original Password", req.Password)

		// Sanitize inputs
		sanitizedEmail := policy.Sanitize(req.Email)
		sanitizedPassword := policy.Sanitize(req.Password)

		// Debug sanitized values
		debugLog("Sanitized Email", sanitizedEmail)
		debugLog("Sanitized Password", sanitizedPassword)

		// Get the user's password from the database
		var storedPassword string
		err = db.QueryRow("SELECT password FROM users WHERE email = $1", sanitizedEmail).Scan(&storedPassword)
		if err != nil {
			if err == sql.ErrNoRows {
				debugLog("Database", "No user found")
				c.JSON(http.StatusOK, PasswordCompareResponse{
					Match: false,
				})
				return
			}

			debugLog("Database error", err.Error())
			c.JSON(http.StatusInternalServerError, PasswordCompareResponse{
				Match: false,
				Error: "Internal server error",
			})
			return
		}

		// Compare passwords
		match := storedPassword == sanitizedPassword
		debugLog("Password comparison result", fmt.Sprintf("%v", match))

		c.JSON(http.StatusOK, PasswordCompareResponse{
			Match: match,
		})
	}
}

func getUserByUsername(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		username := policy.Sanitize(c.Param("username"))
		debugLog("Looking up user", username)

		query := `
			SELECT 
				id, username, email, first_name, last_name, 
				picture, banner, role, tier, is_verified, is_banned,
				personal_website, phone_number, address, country,
				bio, preferences, social_links
			FROM users 
			WHERE username = $1
		`

		var user UserResponse
		err := db.QueryRow(query, username).Scan(
			&user.ID,
			&user.Username,
			&user.Email,
			&user.FirstName,
			&user.LastName,
			&user.Picture,
			&user.Banner,
			&user.Role,
			&user.Tier,
			&user.IsVerified,
			&user.IsBanned,
			&user.PersonalWebsite,
			&user.PhoneNumber,
			&user.Address,
			&user.Country,
			&user.Bio,
			&user.Preferences,
			&user.SocialLinks,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				debugLog("Database", "No user found")
				c.JSON(http.StatusNotFound, gin.H{
					"error": "User not found",
				})
				return
			}

			debugLog("Database error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": user,
		})
	}
}

// serveStorageFile serves files from GCS as a proxy
func serveStorageFile() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get parameters from the URL
		userId := policy.Sanitize(c.Param("userId"))
		fileName := policy.Sanitize(c.Param("fileName"))

		// Create GCS path
		bucketName := "terang-ai-assets"
		objectPath := fmt.Sprintf("users/images/profile/%s/%s", userId, fileName)

		// Create client
		ctx := c.Request.Context()
		client, err := storage.NewClient(ctx)
		if err != nil {
			debugLog("GCS client error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			return
		}
		defer client.Close()

		// Get object
		bucket := client.Bucket(bucketName)
		obj := bucket.Object(objectPath)

		// Check if object exists
		_, err = obj.Attrs(ctx)
		if err != nil {
			if err == storage.ErrObjectNotExist {
				c.JSON(http.StatusNotFound, gin.H{
					"error": "File not found",
				})
				return
			}
			debugLog("GCS object error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			return
		}

		// Read object
		reader, err := obj.NewReader(ctx)
		if err != nil {
			debugLog("GCS reader error", err.Error())
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
			})
			return
		}
		defer reader.Close()

		// Set content type based on file extension
		contentType := getContentTypeFromExtension(path.Ext(fileName))
		c.Writer.Header().Set("Content-Type", contentType)
		c.Writer.Header().Set("Cache-Control", "public, max-age=86400")

		// Stream file to response
		_, err = io.Copy(c.Writer, reader)
		if err != nil {
			debugLog("GCS streaming error", err.Error())
		}
	}
}
