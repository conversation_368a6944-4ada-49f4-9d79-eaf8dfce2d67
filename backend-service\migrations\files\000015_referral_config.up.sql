-- Backup existing configurations
CREATE TABLE referral_configurations_backup AS 
SELECT * FROM referral_configurations;

-- Drop existing foreign key constraints if any reference the configurations table
ALTER TABLE IF EXISTS referral_uses 
DROP CONSTRAINT IF EXISTS fk_referral_config;

-- Drop the existing table
DROP TABLE referral_configurations;

-- Create the new table with JSON configuration
CREATE TABLE referral_configurations (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26), -- NULL means system-wide default
    type referral_type NOT NULL,
    config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_referral_configs_user ON referral_configurations(user_id);
CREATE INDEX idx_referral_configs_type ON referral_configurations(type);
CREATE INDEX idx_referral_configs_active ON referral_configurations(is_active);

-- Add trigger for modified_at
CREATE TRIGGER referral_configs_modified_at
    BEFORE UPDATE ON referral_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();

-- Migrate existing data to the new structure
INSERT INTO referral_configurations (
    id,
    user_id,
    type,
    config,
    is_active,
    created_at,
    modified_at
)
SELECT 
    id,
    NULL as user_id,
    type,
    jsonb_build_object(
        'referrer', jsonb_build_object(
            'type', referrer_reward_type,
            'amount', referrer_reward_amount,
            'min_amount', null,
            'max_amount', null
        ),
        'referee', jsonb_build_object(
            'type', referee_reward_type,
            'amount', referee_reward_amount,
            'min_amount', null,
            'max_amount', null
        ),
        'validity_days', 365,
        'max_uses', null,
        'conditions', CASE 
            WHEN type = 'REGISTRATION' THEN 
                jsonb_build_array(
                    jsonb_build_object(
                        'field', 'registration_status',
                        'operator', 'equals',
                        'value', 'COMPLETED'
                    )
                )
            WHEN type = 'PURCHASE' THEN 
                jsonb_build_array(
                    jsonb_build_object(
                        'field', 'payment_status',
                        'operator', 'equals',
                        'value', 'PAID'
                    ),
                    jsonb_build_object(
                        'field', 'transaction_amount',
                        'operator', 'greaterThan',
                        'value', 0
                    )
                )
            WHEN type = 'SUBSCRIPTION' THEN 
                jsonb_build_array(
                    jsonb_build_object(
                        'field', 'subscription_status',
                        'operator', 'equals',
                        'value', 'ACTIVE'
                    )
                )
            END
    ) as config,
    is_active,
    created_at,
    modified_at
FROM referral_configurations_backup;

-- Create a view for easier access to effective configurations
CREATE OR REPLACE VIEW effective_referral_configurations AS
WITH ranked_configs AS (
    SELECT 
        rc.*,
        COALESCE(user_id, 'default') as config_level,
        ROW_NUMBER() OVER (
            PARTITION BY COALESCE(user_id, 'default'), type
            ORDER BY created_at DESC
        ) as rn
    FROM referral_configurations rc
    WHERE is_active = true
)
SELECT *
FROM ranked_configs
WHERE rn = 1;

-- Drop the backup table if everything is successful
DROP TABLE referral_configurations_backup;