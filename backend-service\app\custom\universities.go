package custom

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// University represents a university entry
type University struct {
	Name          string   `json:"name"`
	Domains       []string `json:"domains"`
	WebPages      []string `json:"web_pages"`
	Country       string   `json:"country"`
	AlphaTwoCode  string   `json:"alpha_two_code"`
	StateProvince *string  `json:"state-province"`
}

// UniversityIndex manages indexed data for efficient searching
type UniversityIndex struct {
	mu           sync.RWMutex
	data         []University
	countryIndex map[string][]University
	nameIndex    map[string][]University
	domainIndex  map[string][]University
	lastUpdated  time.Time
	dataLoaded   bool
}

// NewUniversityIndex creates a new university index
func NewUniversityIndex() *UniversityIndex {
	return &UniversityIndex{
		countryIndex: make(map[string][]University),
		nameIndex:    make(map[string][]University),
		domainIndex:  make(map[string][]University),
	}
}

// load fetches and populates university data
// load fetches and populates university data with goroutines
func (ui *UniversityIndex) load() error {
	ui.mu.Lock()
	defer ui.mu.Unlock()

	// Fetch data from GitHub
	resp, err := http.Get("https://raw.githubusercontent.com/terang-ai/university-domains-list/refs/heads/master/world_universities_and_domains.json")
	if err != nil {
		return fmt.Errorf("failed to fetch university data: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}

	var universities []University
	if err := json.Unmarshal(body, &universities); err != nil {
		return fmt.Errorf("failed to parse university data: %v", err)
	}

	// Reset indexes
	ui.data = universities
	ui.countryIndex = make(map[string][]University)
	ui.nameIndex = make(map[string][]University)
	ui.domainIndex = make(map[string][]University)

	// Use waitgroup to track goroutines
	var wg sync.WaitGroup

	// Create channel-safe maps with their own mutexes
	countryMap := &sync.Map{}
	nameMap := &sync.Map{}
	domainMap := &sync.Map{}

	// Divide work among goroutines
	chunks := 4 // Adjust based on your needs
	chunkSize := (len(universities) + chunks - 1) / chunks

	for i := 0; i < chunks; i++ {
		wg.Add(1)

		start := i * chunkSize
		end := start + chunkSize
		if end > len(universities) {
			end = len(universities)
		}

		go func(start, end int) {
			defer wg.Done()

			for _, uni := range universities[start:end] {
				// Country index
				lowerCountry := strings.ToLower(uni.Country)
				val, _ := countryMap.LoadOrStore(lowerCountry, []University{})
				countryList := val.([]University)
				countryMap.Store(lowerCountry, append(countryList, uni))

				// Name index
				lowerName := strings.ToLower(uni.Name)
				val, _ = nameMap.LoadOrStore(lowerName, []University{})
				nameList := val.([]University)
				nameMap.Store(lowerName, append(nameList, uni))

				// Split name index
				nameParts := strings.Split(uni.Name, " ")
				if len(nameParts) > 1 {
					for _, part := range nameParts[1:] {
						lowerPart := strings.ToLower(part)
						val, _ = nameMap.LoadOrStore(lowerPart, []University{})
						nameList := val.([]University)
						nameMap.Store(lowerPart, append(nameList, uni))
					}
				}

				// Domain index
				for _, domain := range uni.Domains {
					val, _ = domainMap.LoadOrStore(domain, []University{})
					domainList := val.([]University)
					domainMap.Store(domain, append(domainList, uni))
				}
			}
		}(start, end)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// Transfer data from sync.Map to regular maps
	countryMap.Range(func(key, value interface{}) bool {
		ui.countryIndex[key.(string)] = value.([]University)
		return true
	})

	nameMap.Range(func(key, value interface{}) bool {
		ui.nameIndex[key.(string)] = value.([]University)
		return true
	})

	domainMap.Range(func(key, value interface{}) bool {
		ui.domainIndex[key.(string)] = value.([]University)
		return true
	})

	ui.lastUpdated = time.Now()
	ui.dataLoaded = true
	return nil
}

// RegisterUniversityRoutes sets up routes for university search
func RegisterUniversityRoutes(r *gin.Engine, uniIndex *UniversityIndex) {
	// Ensure data is loaded
	if err := uniIndex.load(); err != nil {
		panic(fmt.Sprintf("Failed to load initial university data: %v", err))
	}

	// Root endpoint
	r.GET("/v0/universities", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"author": gin.H{
				"name":    "hipo",
				"website": "http://hipolabs.com",
			},
			"example": "http://universities.hipolabs.com/search?name=middle&country=Turkey",
			"github":  "https://github.com/Hipo/university-domains-list",
		})
	})

	// Search endpoint
	r.GET("/v0/universities/search", func(c *gin.Context) {
		// Parse query parameters
		country := c.Query("country")
		name := c.Query("name")
		nameContains := c.Query("name_contains")
		domain := c.Query("domain")

		// Parse limit and offset
		limit := parseIntUniversity(c.Query("limit"))
		offset := parseIntUniversity(c.Query("offset"))

		// Perform search
		var filtered []University

		if name != "" && country != "" {
			lowerName := strings.ToLower(name)
			lowerCountry := strings.ToLower(country)
			for _, uni := range uniIndex.data {
				if strings.ToLower(uni.Name) == lowerName && strings.ToLower(uni.Country) == lowerCountry {
					filtered = append(filtered, uni)
				}
			}
		} else if nameContains != "" && country != "" {
			lowerNameContains := strings.ToLower(nameContains)
			lowerCountry := strings.ToLower(country)
			regex := regexp.MustCompile(fmt.Sprintf(`\b%s`, lowerNameContains))
			for _, uni := range uniIndex.data {
				if regex.MatchString(strings.ToLower(uni.Name)) && strings.ToLower(uni.Country) == lowerCountry {
					filtered = append(filtered, uni)
				}
			}
		} else if nameContains != "" {
			lowerNameContains := strings.ToLower(nameContains)
			regex := regexp.MustCompile(fmt.Sprintf(`\b%s`, lowerNameContains))
			for _, uni := range uniIndex.data {
				if regex.MatchString(strings.ToLower(uni.Name)) {
					filtered = append(filtered, uni)
				}
			}
		} else if name != "" {
			lowerName := strings.ToLower(name)
			filtered = uniIndex.nameIndex[lowerName]
		} else if country != "" {
			lowerCountry := strings.ToLower(country)
			filtered = uniIndex.countryIndex[lowerCountry]
		} else if domain != "" {
			filtered = uniIndex.domainIndex[domain]
		} else {
			filtered = uniIndex.data
		}

		// Apply offset
		if offset > 0 {
			if offset > len(filtered) {
				filtered = []University{}
			} else {
				filtered = filtered[offset:]
			}
		}

		// Apply limit
		if limit > 0 && limit < len(filtered) {
			filtered = filtered[:limit]
		}

		c.JSON(http.StatusOK, filtered)
	})

	// Update endpoint
	r.GET("/v0/universities/update", func(c *gin.Context) {
		// Check if enough time has passed since last update (24 hours)
		if time.Since(uniIndex.lastUpdated) < 24*time.Hour {
			c.JSON(http.StatusBadRequest, gin.H{
				"status":  "error",
				"message": "Dataset had been updated recently. Try again later.",
			})
			return
		}

		// Attempt to reload data
		if err := uniIndex.load(); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"status":  "error",
				"message": fmt.Sprintf("Failed to update dataset: %v", err),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Dataset updated!",
		})
	})
}

// parseIntUniversity parses a string to an integer with fallback to 0
func parseIntUniversity(s string) int {
	var i int
	_, err := fmt.Sscanf(s, "%d", &i)
	if err != nil {
		return 0
	}
	return i
}
