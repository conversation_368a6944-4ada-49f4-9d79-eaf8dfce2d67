// components/sections/InterviewTypesSection.tsx
import React, { useState, useEffect } from 'react';
import { ArrowRight } from 'lucide-react';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

interface InterviewType {
  name: string;
  category: string;
  duration: string;
  color: string;
  iconBg: string;
  icon: string;
  available: boolean;
  status: string;
  badge?: string;
}

interface InterviewTypesSectionProps {
  router: AppRouterInstance;
}

const InterviewTypesSection: React.FC<InterviewTypesSectionProps> = ({ router }) => {
  const [isVisible, setIsVisible] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('[data-animate]');
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const interviewTypes: InterviewType[] = [
    { 
      name: "LPDP Scholarship", 
      category: "Beasiswa", 
      duration: "45-60 min",
      color: "from-orange-400 to-red-500",
      iconBg: "bg-gradient-to-r from-orange-100 to-red-100",
      icon: "https://cdn.terang.ai/landingpage-assets/degree.svg",
      available: true,
      status: "Tersedia Sekarang",
      badge: "GRATIS"
    },
    { 
      name: "Job Interview", 
      category: "Karir", 
      duration: "30-45 min",
      color: "from-blue-400 to-indigo-500",
      iconBg: "bg-gradient-to-r from-blue-100 to-indigo-100",
      icon: "https://cdn.terang.ai/landingpage-assets/work.svg",
      available: false,
      status: "Coming Soon"
    },
    { 
      name: "Academic Interview", 
      category: "Akademik", 
      duration: "40-50 min",
      color: "from-purple-400 to-pink-500",
      iconBg: "bg-gradient-to-r from-purple-100 to-pink-100",
      icon: "https://cdn.terang.ai/landingpage-assets/books.svg",
      available: false,
      status: "Coming Soon"
    },
    { 
      name: "International Scholarship", 
      category: "Beasiswa", 
      duration: "50-70 min",
      color: "from-emerald-400 to-teal-500",
      iconBg: "bg-gradient-to-r from-emerald-100 to-teal-100",
      icon: "https://cdn.terang.ai/landingpage-assets/international.svg",
      available: false,
      status: "Coming Soon"
    }
  ];

  const handleInterviewTypeClick = (type: InterviewType) => {
    if (type.available) {
      router.push('/available-interviews');
    }
  };

  return (
    <section 
      id="interviews" 
      className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100 relative"
      data-animate="true"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`text-center mb-20 ${isVisible.interviews ? 'animate-fade-in-up' : 'opacity-0'}`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-full text-green-800 text-sm font-medium mb-6">
            <img src="https://cdn.terang.ai/landingpage-assets/target.svg" alt="Target" className="w-4 h-4 mr-2" />
            Pilihan Interview Terlengkap
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Jenis Wawancara yang 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600"> Tersedia</span>
          </h2>
          <p className="text-xl text-gray-600">
            LPDP Interview sudah tersedia! Interview lainnya akan segera hadir untuk melengkapi persiapan karir kamu
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {interviewTypes.map((type, index) => (
            <button 
              key={index} 
              className={`group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all border border-gray-100 relative overflow-hidden w-full text-left ${
                type.available 
                  ? 'transform hover:scale-105 hover:-translate-y-2 cursor-pointer' 
                  : 'opacity-75 cursor-not-allowed'
              }`}
              style={{ animationDelay: `${index * 200}ms` }}
              onClick={() => handleInterviewTypeClick(type)}
              disabled={!type.available}
              type="button"
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${type.color} opacity-0 ${type.available ? 'group-hover:opacity-10' : ''} transition-opacity`}></div>
              
              {/* Status Badge */}
              <div className="absolute top-4 right-4 space-y-2">
                {type.available ? (
                  <>
                    <div className="flex items-center">
                      <span className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                      <span className="text-xs font-bold text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        LIVE
                      </span>
                    </div>
                    {type.badge && (
                      <div className="text-xs font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full animate-bounce">
                        {type.badge}
                      </div>
                    )}
                  </>
                ) : (
                  <span className="text-xs font-bold text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    SOON
                  </span>
                )}
              </div>
              
              <div className="relative z-10">
                <div className={`w-16 h-16 ${type.iconBg} rounded-2xl flex items-center justify-center mb-6 ${
                  type.available ? 'group-hover:scale-110' : ''
                } transition-transform`}>
                  <img src={type.icon} alt={type.name} className="w-10 h-10" />
                </div>
                
                <div className="mb-4">
                  <span className={`inline-block px-4 py-2 bg-gradient-to-r ${type.color} text-white text-sm font-bold rounded-full shadow-lg`}>
                    {type.category}
                  </span>
                </div>
                
                <h3 className={`text-xl font-bold text-gray-900 mb-3 ${
                  type.available ? 'group-hover:text-gray-700' : ''
                } transition-colors`}>
                  {type.name}
                </h3>
                
                <div className="mb-4">
                  <p className="text-gray-600 text-sm mb-2 flex items-center">
                    <span className="mr-2">⏱️</span>
                    Durasi: {type.duration}
                  </p>
                  <p className={`text-sm font-medium flex items-center ${
                    type.available ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    <span className="mr-2">{type.available ? '✅' : '🔔'}</span>
                    {type.status}
                  </p>
                </div>
                
                {type.available ? (
                  <div className={`w-full bg-gradient-to-r ${type.color} text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all transform group-hover:scale-105 flex items-center justify-center`}>
                    Mulai Interview
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                ) : (
                  <div className="w-full bg-gray-300 text-gray-500 py-3 rounded-xl font-bold cursor-not-allowed flex items-center justify-center">
                    <span className="mr-2">🔔</span>
                    Notify Me
                  </div>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export default InterviewTypesSection;