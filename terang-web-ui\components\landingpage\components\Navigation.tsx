import React, { useState, useRef, useEffect } from "react";
import Button from "./Button";
import Logo from "./Logo";
import ProductDropdown from "./ProductDropdown";
import styled, { keyframes } from "styled-components";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { Avatar, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import RoundTextBlack from "@/public/landingpage-assets/learn-ai-text-rounded.png";
import LanguageToggle from "./LanguageToggle";
import { useLanguage } from "@/app/language-wrapper";
import { Grid } from "lucide-react";

interface Props {
  additionalPage?: boolean;
}

interface MenuProps {
  $click: boolean;
}

interface HamburgerMenuProps {
  $click: boolean;
}

const Section = styled.section`
  width: 100vw;
  background-color: ${(props) => props.theme.body};
  z-index: 50;
`;

const NavBar = styled.nav`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 85%;
  height: ${(props) => props.theme.navHeight};
  margin: 0 auto;

  .mobile {
    display: none;
  }

  @media (max-width: 64em) {
    .desktop {
      display: none;
    }
    .mobile {
      display: inline-block;
    }
  }
`;

const rotate = keyframes`
  100% {
    transform: rotate(1turn);
  }
`;

const Circle = styled.span`
  width: 50%;
  height: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: ${(props) => props.theme.text};
  color: ${(props) => props.theme.body};
  font-size: clamp(1rem, 2vw, 1.5rem);
`;

const Round = styled.div`
  position: absolute;
  bottom: 2rem;
  right: 90%;
  width: clamp(4rem, 8vw, 6rem);
  height: clamp(4rem, 8vw, 6rem);
  border: 1px solid ${(props) => props.theme.text};
  border-radius: 50%;
  z-index: 100;

  img {
    width: 100%;
    height: auto;
    animation: ${rotate} 6s linear infinite reverse;
  }

  @media (max-width: 48em) {
    right: 0;
    top: 0%;
    position: relative;
    z-index: 9999;
  }
`;

const StyledAvatar = styled.div`
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00b4d8, #0077b6);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid transparent;
    background: linear-gradient(45deg, #00b4d8, #0077b6) border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box, 
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    opacity: 0.8;
  }

  &:hover {
    transform: scale(1.05);
    
    &::after {
      opacity: 0.2;
    }
    
    &::before {
      opacity: 1;
    }
  }
`;

const OnlineIndicator = styled.div`
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border-radius: 50%;
  border: 2px solid ${(props) => props.theme.body};
  z-index: 1;
`;

const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const Menu = styled.ul<MenuProps>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  list-style: none;

  @media (max-width: 64em) {
    position: fixed;
    top: ${(props) => props.theme.navHeight};
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: ${(props) => `calc(100vh - ${props.theme.navHeight})`};
    z-index: 50;
    background-color: ${(props) => `rgba(${props.theme.bodyRgba},0.85)`};
    backdrop-filter: blur(2px);

    transform: ${(props) =>
      props.$click ? "translateY(0)" : `translateY(1000%)`};
    transition: all 0.3s ease;
    flex-direction: column;
    justify-content: center;
    touch-action: none;
  }
`;

const MenuItem = styled.li`
  margin: 0 1rem;
  color: ${(props) => props.theme.text};
  cursor: pointer;
  font-size: ${(props) => props.theme.fontlg};
  font-weight: 400;

  &::after {
    content: " ";
    display: block;
    width: 0%;
    height: 2px;
    background: ${(props) => props.theme.text};
    transition: width 0.3s ease;
  }
  &:hover::after {
    width: 100%;
  }

  @media (max-width: 64em) {
    margin: 1rem 0;

    &::after {
      display: none;
    }
  }
`;

const HamburgerMenu = styled.span<HamburgerMenuProps>`
  width: ${(props) => (props.$click ? "2rem" : "1.5rem")};
  height: 2px;
  background: ${(props) => props.theme.text};

  position: absolute;
  top: 2rem;
  left: 50%;
  transform: ${(props) =>
    props.$click
      ? "translateX(-50%) rotate(90deg)"
      : "translateX(-50%) rotate(0)"};

  display: none;
  justify-content: center;
  align-items: center;

  cursor: pointer;
  transition: all 0.3s ease;

  @media (max-width: 64em) {
    top: 6%;
    display: flex;
  }

  &::after,
  &::before {
    content: " ";
    width: ${(props) => (props.$click ? "1rem" : "1.5rem")};
    height: 2px;
    right: ${(props) => (props.$click ? "-2px" : "0")};
    background: ${(props) => props.theme.text};
    position: absolute;
    transition: all 0.3s ease;
  }

  &::after {
    top: ${(props) => (props.$click ? "0.3rem" : "0.5rem")};
    transform: ${(props) => (props.$click ? "rotate(-40deg)" : "rotate(0)")};
  }
  &::before {
    bottom: ${(props) => (props.$click ? "0.3rem" : "0.5rem")};
    transform: ${(props) => (props.$click ? "rotate(40deg)" : "rotate(0)")};
  }
`;

const HamburgerMenuWrapper = styled.div`
  position: relative;
  width: 44px;
  height: 44px;
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 100;
  
  @media (max-width: 64em) {
    display: flex;
  }
`;

const Navigation: React.FC<Props> = ({ additionalPage }) => {
  const router = useRouter();
  const [click, setClick] = useState<boolean>(false);
  const { data: session } = useSession();
  const { t } = useLanguage();

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 1024); // 64em = 1024px
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const scrollTo = (id: string) => {
    const element = document.getElementById(id);

    element?.scrollIntoView({
      behavior: "smooth",
      block: "start",
      inline: "nearest",
    });

    setClick(!click);
  };

  const handleMenuItemClick = () => {
    setClick(false); // Close mobile menu when any item is clicked
  };

  const UserAvatar = () => (
    <Dropdown>
      <DropdownTrigger>
        <StyledAvatar>
          <Avatar
            className="cursor-pointer"
            src={session?.user?.image || ""}
            name={session?.user?.name || "User"}
            size="md"
            classNames={{
              base: "bg-gradient-to-br from-[#00b4d8] to-[#0077b6]",
              img: "opacity-90 hover:opacity-100"
            }}
          />
          <OnlineIndicator />
        </StyledAvatar>
      </DropdownTrigger>
      <DropdownMenu 
        aria-label="User actions"
        className="w-60"
        disabledKeys={[]}  // ← Add this
      >
        <DropdownItem
          key="profile"
          className="h-14 gap-2"
          textValue="Profile"
          onPress={() => {router.push('/dashboard')}}
        >
          <div className="flex gap-2 items-center">
            <Avatar
              size="sm"
              src={session?.user?.image || ""}
              name={session?.user?.name || "User"}
            />
            <div className="flex flex-col">
              <span className="text-sm font-semibold">{session?.user?.name}</span>
              <span className="text-xs text-default-500">{session?.user?.email}</span>
            </div>
          </div>
        </DropdownItem>
        <DropdownItem 
          key="dashboard" 
          onPress={() => {router.push('/dashboard')}}
          startContent={<Grid size={18} />}
        >
          {t('dashboard')}
        </DropdownItem>
        <DropdownItem key="settings" onPress={() => {router.push('/settings')}}>
          {t('settings')}
        </DropdownItem>
        <DropdownItem key="help" onPress={() => {router.push('/feedback')}}>
          {t('feedback')}
        </DropdownItem>
        <DropdownItem 
          key="logout" 
          className="text-danger" 
          color="danger"
          onClick={() => signOut()}
        >
          {t('logout')}
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );

  // Common menu items for both authenticated and non-authenticated users
  const getCommonMenuItems = () => {
    if (additionalPage) {
      return (
        <>
          <MenuItem>
            <Link href="/">{t('home')}</Link>
          </MenuItem>
          <MenuItem>
            <ProductDropdown 
              t={t} 
              isMobile={isMobile} 
              onItemClick={handleMenuItemClick} 
            />
          </MenuItem>
          <MenuItem>
            <Link href="/">{t('roadmap')}</Link>
          </MenuItem>
          <MenuItem>
            <Link href="/">{t('showcase')}</Link>
          </MenuItem>
          <MenuItem>
            <Link href="/">{t('team')}</Link>
          </MenuItem>
          <MenuItem>
            <Link href="/">{t('faq')}</Link>
          </MenuItem>
        </>
      );
    } else {
      return (
        <>
          <MenuItem onClick={() => scrollTo("home")}>{t('home')}</MenuItem>
          <MenuItem>
            <ProductDropdown 
              t={t} 
              isMobile={isMobile} 
              onItemClick={handleMenuItemClick} 
            />
          </MenuItem>
          <MenuItem onClick={() => scrollTo("roadmap")}>{t('roadmap')}</MenuItem>
          <MenuItem onClick={() => scrollTo("showcase")}>{t('showcase')}</MenuItem>
          <MenuItem onClick={() => scrollTo("team")}>{t('team')}</MenuItem>
          <MenuItem onClick={() => scrollTo("FAQ")}>{t('faq')}</MenuItem>
        </>
      );
    }
  };

  return (
    <Section className="p-3" id="navigation">
      <NavBar>
        <Logo />
        <HamburgerMenuWrapper onClick={() => setClick(!click)}>
          <HamburgerMenu $click={click}>&nbsp;</HamburgerMenu>
        </HamburgerMenuWrapper>
        <Menu $click={click}>
          {getCommonMenuItems()}
          
          <MenuItem className="sm:hidden">
            <LanguageToggle />
          </MenuItem>
          
          <MenuItem>
            <div className="mobile">
              {session ? (
                <UserAvatar />
              ) : (
                <Button link="/register" text={t('daftar_di_sini')} />
              )}
            </div>
          </MenuItem>
        </Menu>
        <ActionsContainer className="desktop">
          <LanguageToggle />
          
          {session ? (
            <UserAvatar />
          ) : (
            <Button link="/register" text={t('daftar_di_sini')} />
          )}
        </ActionsContainer>
        <Round>
          <Circle>&#x2193;</Circle>
          <Image
            alt="AI-LEARNING"
            src={RoundTextBlack}
            width={500}
            height={400}
            priority
            placeholder="blur"
          />
        </Round>
      </NavBar>
    </Section>
  );
};

export default Navigation;