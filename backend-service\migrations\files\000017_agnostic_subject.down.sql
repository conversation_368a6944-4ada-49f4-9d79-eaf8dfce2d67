-- Function to remove the exam_type field from metadata_scores
CREATE OR REPLACE FUNCTION remove_exam_type(metadata_scores JSONB)
RETURNS JSONB AS $$
BEGIN
    RETURN metadata_scores - 'exam_type';
END;
$$ LANGUAGE plpgsql;

-- Remove exam_type field from all exam scores
UPDATE exam_scores
SET metadata_scores = remove_exam_type(metadata_scores)
WHERE metadata_scores->>'exam_type' IS NOT NULL;

-- Drop the function
DROP FUNCTION remove_exam_type(JSONB);

-- Drop created indexes
DROP INDEX IF EXISTS idx_metadata_exam_type;
DROP INDEX IF EXISTS idx_metadata_scores_subject;