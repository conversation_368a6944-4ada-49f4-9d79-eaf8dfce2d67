package controller

import "github.com/gin-gonic/gin"

type TermsOfServiceControllerInterface interface {
	InsertTermsOfService(*gin.Context)
	GetAllTermsOfServices(*gin.Context)
	GetOneTermsOfService(*gin.Context)
	UpdateTermsOfService(*gin.Context)
	DeleteTermsOfService(*gin.Context)
	GetAllTermsOfServicePublic(*gin.Context)
	GetAllTermsOfServicesByArtistId(*gin.Context)
	GetOneTermsOfServicesByArtistId(*gin.Context)
}
