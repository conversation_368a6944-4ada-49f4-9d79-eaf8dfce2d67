package custom

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/redis/go-redis/v9"
)

// Keep existing structs for difficulty tracking
type QuestionDifficulty struct {
	Score        float64 `json:"score"`
	Level        string  `json:"level"`
	TimeFactor   float64 `json:"timeFactor"`
	ErrorFactor  float64 `json:"errorFactor"`
	ExpectedTime float64 `json:"expectedTime"`
}

// Keep existing Metadata struct
type QuestionMetadata struct {
	Level int    `json:"level"`
	Name  string `json:"name"`
	Value string `json:"value"`
}

// Keep existing TimeManagement struct
type QuestionTimeManagement struct {
	QuestionID     string             `json:"questionId"`
	Title          string             `json:"title"`
	TimeSpent      float64            `json:"timeSpent"`
	AverageTime    float64            `json:"averageTime"`
	TimeDifference float64            `json:"timeDifference"`
	IsCorrect      bool               `json:"isCorrect"`
	IsAttempted    bool               `json:"isAttempted"`
	UserAnswer     string             `json:"userAnswer"`
	CorrectAnswer  string             `json:"correctAnswer"`
	TimeStatus     string             `json:"timeStatus"`
	AttemptedAt    time.Time          `json:"attemptedAt"`
	Metadata       []QuestionMetadata `json:"metadata"`
}

// Define new structs that map to normalized tables
type ExamQuestion struct {
	ID       string `db:"id"`
	ExamID   string `db:"exam_id"`
	Title    string `db:"title"`
	Metadata []QuestionMetadata
	Options  []QuestionOption
}

type QuestionOption struct {
	ID         string `db:"id"`
	QuestionID string `db:"question_id"`
	ExamID     string `db:"exam_id"`
	Content    string `db:"content"`
	IsCorrect  bool   `db:"is_correct"`
}

// Keep existing structs and helper functions
func calculateDifficultyLevel(score float64) string {
	switch {
	case score <= 0.2:
		return "VERY_EASY"
	case score <= 0.4:
		return "EASY"
	case score <= 0.6:
		return "MODERATE"
	case score <= 0.8:
		return "HARD"
	default:
		return "VERY_HARD"
	}
}

// Keep old struct for backward compatibility until fully migrated
type questionData struct {
	ID        string             `json:"id"`
	Metadata  []QuestionMetadata `json:"metadata"`
	IsCorrect bool               `json:"is_correct"`
	Question  []struct {
		Contents []struct {
			Content string `json:"content"`
		} `json:"contents"`
	} `json:"question"`
	Options struct {
		Values []struct {
			ID        string `json:"id"`
			IsCorrect bool   `json:"is_correct"`
		} `json:"values"`
	} `json:"options"`
}

// Add this struct for PostgreSQL interval handling
type PostgresDuration struct {
	time.Duration
}

func (d *PostgresDuration) Scan(src interface{}) error {
	var interval string
	switch v := src.(type) {
	case []byte:
		interval = string(v)
	case string:
		interval = v
	case nil:
		return nil
	default:
		return fmt.Errorf("cannot scan type %T into PostgresDuration", src)
	}

	// Parse PostgreSQL interval format
	// Try parsing different formats
	if strings.Contains(interval, ":") {
		// Format: HH:MM:SS
		parts := strings.Split(interval, ":")
		if len(parts) != 3 {
			return fmt.Errorf("invalid interval format: %s", interval)
		}
		hours, _ := strconv.Atoi(parts[0])
		minutes, _ := strconv.Atoi(parts[1])
		seconds, _ := strconv.Atoi(strings.Split(parts[2], ".")[0])

		d.Duration = time.Duration(hours)*time.Hour +
			time.Duration(minutes)*time.Minute +
			time.Duration(seconds)*time.Second
	} else {
		// Try to parse as a PostgreSQL interval
		d.Duration, _ = time.ParseDuration(interval)
	}

	return nil
}

// Update the exam struct
type examDetails struct {
	Name     string           `db:"name"`
	Duration PostgresDuration `db:"duration"`
}

// Update the SubjectGlobalStats struct to include more metrics
type SubjectGlobalStats struct {
	Subject                 string  `db:"subject"`
	TotalAttempts           int     `db:"total_attempts"`
	TotalQuestionsAttempted int     `db:"questions_attempted"`
	TotalQuestions          int     `db:"total_questions"`
	TotalCorrect            int     `db:"total_correct"`
	TotalErrors             int     `db:"total_errors"`
	AccuracyRate            float64 `db:"accuracy_rate"`
}

// Modify your GlobalStats struct to include subject stats
type GlobalStats struct {
	AverageTimeSpent    float64                       `json:"averageTimeSpent"`
	TotalAttempts       int                           `json:"totalAttempts"`
	AverageAccuracyRate float64                       `json:"averageAccuracyRate"`
	FastestTime         float64                       `json:"fastestTime"`
	SlowestTime         float64                       `json:"slowestTime"`
	SubjectStats        map[string]SubjectGlobalStats `json:"subjectStats"` // Add this field
}

type ExamDifficultyInfo struct {
	Score     float64                       `json:"score"`
	Level     string                        `json:"level"`
	BySubject map[string]QuestionDifficulty `json:"bySubject"`
}

// Add new time status constants for clarity
const (
	TimeStatusFastCorrect      = "fast_correct"
	TimeStatusFastIncorrect    = "fast_incorrect"
	TimeStatusAverageCorrect   = "average_correct"
	TimeStatusAverageIncorrect = "average_incorrect"
	TimeStatusSlowCorrect      = "slow_correct"
	TimeStatusSlowIncorrect    = "slow_incorrect"
	TimeStatusNotAttempted     = "not_attempted"
)

// Update TimeDistribution to include correctness
type TimeDistribution struct {
	FastCorrect      int `json:"fastCorrect"`
	FastIncorrect    int `json:"fastIncorrect"`
	AverageCorrect   int `json:"averageCorrect"`
	AverageIncorrect int `json:"averageIncorrect"`
	SlowCorrect      int `json:"slowCorrect"`
	SlowIncorrect    int `json:"slowIncorrect"`
	NotAttempted     int `json:"notAttempted"`
}

// Update SessionReport to use new TimeDistribution
type SessionReport struct {
	ExamID           string                   `json:"examId"`
	ExamName         string                   `json:"examName"`
	SessionID        string                   `json:"sessionId"`
	TotalQuestions   int                      `json:"totalQuestions"`
	ExpectedBaseTime float64                  `json:"expectedBaseTime"`
	UserStats        UserStats                `json:"userStats"`
	GlobalStats      GlobalStats              `json:"globalStats"`
	TimeDistribution TimeDistribution         `json:"timeDistribution"`
	Questions        []QuestionTimeManagement `json:"questions"`
}

// Add this new struct for user subject stats
type SubjectUserStats struct {
	Subject            string  `json:"subject"`
	QuestionsAttempted int     `json:"questionsAttempted"`
	TotalQuestions     int     `json:"totalQuestions"`
	CorrectAnswers     int     `json:"correctAnswers"`
	AccuracyRate       float64 `json:"accuracyRate"`
}

// Update UserStats to include subject stats
type UserStats struct {
	TimeSpent          float64                     `json:"timeSpent"`
	AverageTimeSpent   float64                     `json:"averageTimeSpent"`
	CorrectAnswers     int                         `json:"correctAnswers"`
	AttemptedQuestions int                         `json:"attemptedQuestions"`
	AccuracyRate       float64                     `json:"accuracyRate"`
	FastCorrectRate    float64                     `json:"fastCorrectRate"`
	AverageCorrectRate float64                     `json:"averageCorrectRate"`
	SlowCorrectRate    float64                     `json:"slowCorrectRate"`
	SubjectStats       map[string]SubjectUserStats `json:"subjectStats"` // Add this field
}

// Update TimeManagementReport to include Sessions
type TimeManagementReport struct {
	UserID           string                   `json:"userId"`
	ExamID           string                   `json:"examId"`
	ExamName         string                   `json:"examName"`
	SessionID        string                   `json:"sessionId"`
	UserStats        UserStats                `json:"userStats"`
	GlobalStats      GlobalStats              `json:"globalStats"`
	TotalQuestions   int                      `json:"totalQuestions"`
	ExamDifficulty   ExamDifficultyInfo       `json:"examDifficulty"`
	TimeDistribution TimeDistribution         `json:"timeDistribution"`
	ExpectedBaseTime float64                  `json:"expectedBaseTime"`
	Questions        []QuestionTimeManagement `json:"questions"`
	Sessions         []SessionReport          `json:"sessions"` // Add this field
}

// Helper struct for batch processing
type sessionQueryData struct {
	sessionID string
	examID    string
	userID    string
	answers   json.RawMessage
}

// Helper struct for batch question times
type sessionTimesData struct {
	sessionID     string
	questionTimes map[string]float64
}

// Helper struct for batch question data
type examQuestionsData struct {
	examID    string
	questions []ExamQuestion // Changed from questionData to ExamQuestion
}

// Cache key generators
func getSessionReportCacheKey(sessionID string) string {
	return fmt.Sprintf("report:session:%s", sessionID)
}

func getUserReportCacheKey(userID string) string {
	return fmt.Sprintf("report:user:%s", userID)
}

func getLatestReportCacheKey(userID string) string {
	return fmt.Sprintf("report:user:%s:latest", userID)
}

// Cache duration - 1 day
const cacheDuration = 1440 * time.Minute

func generateTimeManagementReport(dbx *sqlx.DB, sessionID string) (*TimeManagementReport, error) {
	// Define a struct to hold our query results and errors
	type queryResult struct {
		session *struct {
			ExamID  string          `db:"exam_id"`
			UserID  string          `db:"user_id"`
			Answers json.RawMessage `db:"answers"`
		}
		exam         *examDetails
		gamification *struct {
			QuestionTimes json.RawMessage `db:"question_times"`
		}
		globalStats *struct {
			AvgTime       float64 `db:"avg_time"`
			FastestTime   float64 `db:"fastest_time"`
			SlowestTime   float64 `db:"slowest_time"`
			TotalAttempts int     `db:"total_attempts"`
			TotalErrors   int     `db:"total_errors"`
			TotalTime     float64 `db:"total_time"`
		}
		subjectStats *[]SubjectGlobalStats
		avgTimes     *[]struct {
			QuestionID  string  `db:"question_id"`
			AverageTime float64 `db:"avg_time"`
			Attempts    int     `db:"attempts"`
		}
		err error
	}

	// Initialize a WaitGroup to synchronize goroutines
	var wg sync.WaitGroup

	// Use channels to collect query results
	sessionChan := make(chan queryResult)
	examChan := make(chan queryResult)
	gamificationChan := make(chan queryResult)
	globalStatsChan := make(chan queryResult)
	subjectStatsChan := make(chan queryResult)
	avgTimesChan := make(chan queryResult)

	// 1. Get session details and user's answers - run as goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			session: &struct {
				ExamID  string          `db:"exam_id"`
				UserID  string          `db:"user_id"`
				Answers json.RawMessage `db:"answers"`
			}{},
		}

		result.err = dbx.Get(result.session, `
			SELECT exam_id, user_id, answers 
			FROM exam_sessions 
			WHERE session_id = $1`, sessionID)

		sessionChan <- result
	}()

	// Wait for session to complete as we need exam_id for other queries
	sessionResult := <-sessionChan
	if sessionResult.err != nil {
		return nil, fmt.Errorf("error fetching session: %v", sessionResult.err)
	}

	examID := sessionResult.session.ExamID

	// 2. Get exam details with duration
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			exam: &examDetails{},
		}

		result.err = dbx.Get(result.exam, `
			SELECT name, duration 
			FROM available_exams 
			WHERE id = $1`, examID)

		examChan <- result
	}()

	// 3. Get user's question times
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			gamification: &struct {
				QuestionTimes json.RawMessage `db:"question_times"`
			}{},
		}

		result.err = dbx.Get(result.gamification, `
			SELECT question_times 
			FROM exam_gamification 
			WHERE exam_session_id = $1`, sessionID)

		gamificationChan <- result
	}()

	// 5. Get global stats
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			globalStats: &struct {
				AvgTime       float64 `db:"avg_time"`
				FastestTime   float64 `db:"fastest_time"`
				SlowestTime   float64 `db:"slowest_time"`
				TotalAttempts int     `db:"total_attempts"`
				TotalErrors   int     `db:"total_errors"`
				TotalTime     float64 `db:"total_time"`
			}{},
		}

		globalStatsQuery := `
			WITH session_stats AS (
				SELECT 
					es.exam_id,
					eg.exam_session_id,
					SUM(CAST(value AS DOUBLE PRECISION)) as total_time,
					COUNT(*) as questions_attempted,
					JSONB_OBJECT_AGG(key, value) as times,
					COUNT(CASE WHEN es.answers->>key != qo.id 
							THEN 1 END) as error_count
				FROM exam_gamification eg
				JOIN exam_sessions es ON eg.exam_session_id = es.session_id
				CROSS JOIN LATERAL jsonb_each_text(eg.question_times) times(key, value)
				JOIN exam_questions eq ON es.exam_id = eq.exam_id AND key = eq.id
				JOIN question_options qo ON eq.id = qo.question_id AND eq.exam_id = qo.exam_id AND qo.is_correct = true
				WHERE es.exam_id = $1 AND es.status = 'COMPLETED'
				GROUP BY es.exam_id, eg.exam_session_id
			)
			SELECT 
				ROUND(AVG(total_time/NULLIF(questions_attempted, 0))::numeric, 3) as avg_time,
				ROUND(MIN(total_time/NULLIF(questions_attempted, 0))::numeric, 3) as fastest_time,
				ROUND(MAX(total_time/NULLIF(questions_attempted, 0))::numeric, 3) as slowest_time,
				COUNT(DISTINCT exam_session_id) as total_attempts,
				SUM(error_count) as total_errors,
				ROUND(SUM(total_time)::numeric, 3) as total_time
			FROM session_stats`

		result.err = dbx.Get(result.globalStats, globalStatsQuery, examID)

		globalStatsChan <- result
	}()

	// Get subject-wise stats
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			subjectStats: &[]SubjectGlobalStats{},
		}

		subjectStatsQuery := `
			WITH questions_with_subject AS (
			-- Get all questions with their subjects
			SELECT
				q.id as question_id,
				q.exam_id,
				qm.value as subject,
				qo.id as correct_answer_id
			FROM exam_questions q
			JOIN question_metadata qm ON q.id = qm.question_id AND q.exam_id = qm.exam_id
			JOIN question_options qo ON q.id = qo.question_id AND q.exam_id = qo.exam_id AND qo.is_correct = true
			WHERE q.exam_id = $1 AND qm.name = 'subject'
			),
			answer_stats AS (
			-- Then join with session data to get attempt/correct info
			SELECT 
				qs.subject,
				qs.question_id,
				CASE WHEN es.answers->qs.question_id IS NOT NULL THEN 1 ELSE 0 END as is_attempted,
				CASE 
				WHEN es.answers->qs.question_id#>>'{}'= qs.correct_answer_id THEN 1 
				ELSE 0 
				END as is_correct
			FROM questions_with_subject qs
			LEFT JOIN exam_sessions es ON 
				es.exam_id = $1 
				AND es.status = 'COMPLETED'
				AND es.answers ? qs.question_id
			)
			SELECT 
			subject,
			COUNT(DISTINCT question_id) as total_questions,
			SUM(is_attempted) as questions_attempted,
			SUM(is_correct) as total_correct,
			SUM(CASE WHEN is_attempted = 1 THEN 1 - is_correct ELSE 0 END) as total_errors,
			CASE 
				WHEN SUM(is_attempted) > 0 
				THEN ROUND((SUM(is_correct)::float / SUM(is_attempted) * 100)::numeric, 1)
				ELSE 0 
			END as accuracy_rate
			FROM answer_stats
			WHERE subject IS NOT NULL
			GROUP BY subject;`

		result.err = dbx.Select(result.subjectStats, subjectStatsQuery, examID)

		subjectStatsChan <- result
	}()

	// 6. Get per-question averages
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := queryResult{
			avgTimes: &[]struct {
				QuestionID  string  `db:"question_id"`
				AverageTime float64 `db:"avg_time"`
				Attempts    int     `db:"attempts"`
			}{},
		}

		avgQuery := `
			WITH question_times_expanded AS (
				SELECT 
					eg.exam_session_id,
					es.exam_id,
					(jsonb_each_text(eg.question_times)).*
				FROM exam_gamification eg
				JOIN exam_sessions es ON eg.exam_session_id = es.session_id
				WHERE es.exam_id = $1
				AND es.status = 'COMPLETED'
			)
			SELECT 
				key as question_id,
				ROUND(AVG(CAST(value AS DOUBLE PRECISION))::numeric, 3) as avg_time,
				COUNT(*) as attempts
			FROM question_times_expanded
			GROUP BY key`

		result.err = dbx.Select(result.avgTimes, avgQuery, examID)

		avgTimesChan <- result
	}()

	// Collect remaining results from channels
	examResult := <-examChan
	if examResult.err != nil {
		return nil, fmt.Errorf("error fetching exam details: %v", examResult.err)
	}

	gamificationResult := <-gamificationChan
	if gamificationResult.err != nil {
		return nil, fmt.Errorf("error fetching timing data: %v", gamificationResult.err)
	}

	globalStatsResult := <-globalStatsChan
	if globalStatsResult.err != nil {
		return nil, fmt.Errorf("error fetching global stats: %v", globalStatsResult.err)
	}

	subjectStatsResult := <-subjectStatsChan
	if subjectStatsResult.err != nil {
		return nil, fmt.Errorf("error fetching subject stats: %v", subjectStatsResult.err)
	}

	avgTimesResult := <-avgTimesChan
	if avgTimesResult.err != nil {
		return nil, fmt.Errorf("error fetching average times: %v", avgTimesResult.err)
	}

	// Parse user answers and times
	var userAnswers map[string]string
	var userTimes map[string]float64

	// Process results in parallel
	var parseWg sync.WaitGroup
	var parseErr error
	var mu sync.Mutex

	parseWg.Add(2)

	// Parse user answers
	go func() {
		defer parseWg.Done()
		if err := json.Unmarshal(sessionResult.session.Answers, &userAnswers); err != nil {
			mu.Lock()
			parseErr = fmt.Errorf("error parsing user answers: %v", err)
			mu.Unlock()
		}
	}()

	// Parse question times
	go func() {
		defer parseWg.Done()
		if err := json.Unmarshal(gamificationResult.gamification.QuestionTimes, &userTimes); err != nil {
			mu.Lock()
			parseErr = fmt.Errorf("error parsing question times: %v", err)
			mu.Unlock()
		}
	}()

	parseWg.Wait()

	if parseErr != nil {
		return nil, parseErr
	}

	// Fetch questions from the new normalized tables
	type questionWithMetadata struct {
		QuestionID string `db:"id"`
		Title      string `db:"title"`
		ExamID     string `db:"exam_id"`
		MetaName   string `db:"meta_name"`
		MetaValue  string `db:"meta_value"`
		MetaLevel  int    `db:"meta_level"`
	}

	var questionMetadataResults []questionWithMetadata
	questionsQuery := `
		SELECT 
			q.id, 
			q.title, 
			q.exam_id,
			m.name AS meta_name, 
			m.value AS meta_value,
			m.level AS meta_level
		FROM exam_questions q
		LEFT JOIN question_metadata m ON q.id = m.question_id AND q.exam_id = m.exam_id
		WHERE q.exam_id = $1
	`
	if err := dbx.Select(&questionMetadataResults, questionsQuery, examID); err != nil {
		return nil, fmt.Errorf("error fetching questions with metadata: %v", err)
	}

	// Fetch question options separately
	type questionOption struct {
		QuestionID string `db:"question_id"`
		OptionID   string `db:"id"`
		IsCorrect  bool   `db:"is_correct"`
	}

	var optionsResults []questionOption
	optionsQuery := `
		SELECT 
			question_id, 
			id, 
			is_correct
		FROM question_options
		WHERE exam_id = $1
	`
	if err := dbx.Select(&optionsResults, optionsQuery, examID); err != nil {
		return nil, fmt.Errorf("error fetching question options: %v", err)
	}

	// Create maps for faster lookups
	questions := make(map[string]*ExamQuestion)
	correctAnswers := make(map[string]string)

	// Process question metadata results
	for _, qm := range questionMetadataResults {
		if _, exists := questions[qm.QuestionID]; !exists {
			questions[qm.QuestionID] = &ExamQuestion{
				ID:       qm.QuestionID,
				ExamID:   qm.ExamID,
				Title:    qm.Title,
				Metadata: []QuestionMetadata{},
				Options:  []QuestionOption{},
			}
		}

		// Only add metadata if it exists
		if qm.MetaName != "" {
			questions[qm.QuestionID].Metadata = append(questions[qm.QuestionID].Metadata, QuestionMetadata{
				Name:  qm.MetaName,
				Value: qm.MetaValue,
				Level: qm.MetaLevel,
			})
		}
	}

	// Process options results
	for _, opt := range optionsResults {
		// Store correct answer for quick lookup
		if opt.IsCorrect {
			correctAnswers[opt.QuestionID] = opt.OptionID
		}

		// Add option to question
		if q, exists := questions[opt.QuestionID]; exists {
			q.Options = append(q.Options, QuestionOption{
				ID:         opt.OptionID,
				QuestionID: opt.QuestionID,
				ExamID:     examID,
				IsCorrect:  opt.IsCorrect,
			})
		}
	}

	// Convert map to slice for consistent processing
	questionsList := make([]ExamQuestion, 0, len(questions))
	for _, q := range questions {
		questionsList = append(questionsList, *q)
	}

	// Create averages map for easier lookup
	avgTimesMap := make(map[string]float64)
	for _, avg := range *avgTimesResult.avgTimes {
		avgTimesMap[avg.QuestionID] = avg.AverageTime
	}

	// Calculate expected base time
	durationSeconds := examResult.exam.Duration.Seconds()
	expectedBaseTime := durationSeconds / float64(len(questions))

	// Initialize subject-related maps with mutex protection
	var subjectMapsMutex sync.RWMutex
	allSubjects := make(map[string]bool)
	subjectTotalQuestions := make(map[string]int)
	subjectTimes := make(map[string]float64)
	subjectAttempts := make(map[string]int)
	subjectErrors := make(map[string]int)

	// First pass: collect all subjects from questions concurrently
	subjectWg := sync.WaitGroup{}
	for _, q := range questionsList {
		subjectWg.Add(1)
		go func(q ExamQuestion) {
			defer subjectWg.Done()

			for _, meta := range q.Metadata {
				if meta.Name == "subject" {
					subject := meta.Value

					subjectMapsMutex.Lock()
					allSubjects[subject] = true
					subjectTotalQuestions[subject]++
					subjectMapsMutex.Unlock()

					break
				}
			}
		}(q)
	}
	subjectWg.Wait()
	// Initialize the report
	report := &TimeManagementReport{
		UserID:           sessionResult.session.UserID,
		ExamID:           sessionResult.session.ExamID,
		ExamName:         examResult.exam.Name,
		SessionID:        sessionID,
		TotalQuestions:   len(questions),
		ExpectedBaseTime: expectedBaseTime,
		ExamDifficulty: ExamDifficultyInfo{
			BySubject: make(map[string]QuestionDifficulty),
		},
		GlobalStats: GlobalStats{
			AverageTimeSpent: globalStatsResult.globalStats.AvgTime,
			TotalAttempts:    globalStatsResult.globalStats.TotalAttempts,
			FastestTime:      globalStatsResult.globalStats.FastestTime,
			SlowestTime:      globalStatsResult.globalStats.SlowestTime,
			SubjectStats:     make(map[string]SubjectGlobalStats),
		},
	}

	// Populate the subject stats map
	for _, stat := range *subjectStatsResult.subjectStats {
		report.GlobalStats.SubjectStats[stat.Subject] = stat
	}

	// Initialize user stats
	userStats := UserStats{
		AttemptedQuestions: len(userTimes),
		FastCorrectRate:    0,
		AverageCorrectRate: 0,
		SlowCorrectRate:    0,
	}

	// Use atomic operations for counters
	var userStatsTimeSpent int64
	var userStatsCorrectAnswers int32
	var fastCorrect, fastIncorrect int32
	var averageCorrect, averageIncorrect int32
	var slowCorrect, slowIncorrect int32
	var notAttempted int32

	// Use mutex for time distribution
	var timeDistMutex sync.Mutex
	timeDistribution := TimeDistribution{}

	// Process each question in parallel
	tempQuestions := make([]QuestionTimeManagement, len(questionsList))

	// Channel to collect processed question results
	questionResultChan := make(chan struct {
		index    int
		question QuestionTimeManagement
	}, len(questionsList))

	for i, q := range questionsList {
		wg.Add(1)
		go func(i int, q ExamQuestion) {
			defer wg.Done()

			userAns, attempted := userAnswers[q.ID]
			timeSpent, hasTime := userTimes[q.ID]
			avgTime := avgTimesMap[q.ID]

			// ADDED: Normalize the time data to handle anomalies
			if hasTime {
				timeSpent = normalizeTimeData(timeSpent, avgTime, expectedBaseTime)
			}

			// Get subject from metadata
			var subject string
			for _, meta := range q.Metadata {
				if meta.Name == "subject" {
					subject = meta.Value
					break
				}
			}

			// Get correct answer
			correctAnswer := correctAnswers[q.ID]

			questionReport := QuestionTimeManagement{
				QuestionID:    q.ID,
				Title:         q.Title,
				IsAttempted:   attempted,
				AverageTime:   avgTime,
				CorrectAnswer: correctAnswer,
				TimeStatus:    TimeStatusNotAttempted,
				Metadata:      q.Metadata,
			}

			// Track stats for this question
			if subject != "" {
				if attempted && hasTime {
					subjectMapsMutex.Lock()
					subjectTimes[subject] += timeSpent
					subjectAttempts[subject]++
					if userAns != correctAnswer {
						subjectErrors[subject]++
					}
					subjectMapsMutex.Unlock()
				} else {
					// Count unattempted questions as errors
					subjectMapsMutex.Lock()
					subjectErrors[subject]++
					subjectMapsMutex.Unlock()
				}
			}

			if attempted && hasTime {
				questionReport.TimeSpent = timeSpent
				questionReport.TimeDifference = timeSpent - avgTime
				questionReport.UserAnswer = userAns
				questionReport.IsCorrect = userAns == correctAnswer

				// Atomically update user stats
				atomic.AddInt64((*int64)(&userStatsTimeSpent), int64(timeSpent*1000000)) // Scale for precision
				if questionReport.IsCorrect {
					atomic.AddInt32(&userStatsCorrectAnswers, 1)
				}

				// Determine time status with correctness
				timeDistMutex.Lock()
				if timeSpent < avgTime*0.8 {
					if questionReport.IsCorrect {
						questionReport.TimeStatus = TimeStatusFastCorrect
						timeDistribution.FastCorrect++
						atomic.AddInt32(&fastCorrect, 1)
					} else {
						questionReport.TimeStatus = TimeStatusFastIncorrect
						timeDistribution.FastIncorrect++
						atomic.AddInt32(&fastIncorrect, 1)
					}
				} else if timeSpent > avgTime*1.2 {
					if questionReport.IsCorrect {
						questionReport.TimeStatus = TimeStatusSlowCorrect
						timeDistribution.SlowCorrect++
						atomic.AddInt32(&slowCorrect, 1)
					} else {
						questionReport.TimeStatus = TimeStatusSlowIncorrect
						timeDistribution.SlowIncorrect++
						atomic.AddInt32(&slowIncorrect, 1)
					}
				} else {
					if questionReport.IsCorrect {
						questionReport.TimeStatus = TimeStatusAverageCorrect
						timeDistribution.AverageCorrect++
						atomic.AddInt32(&averageCorrect, 1)
					} else {
						questionReport.TimeStatus = TimeStatusAverageIncorrect
						timeDistribution.AverageIncorrect++
						atomic.AddInt32(&averageIncorrect, 1)
					}
				}
				timeDistMutex.Unlock()
			} else {
				timeDistMutex.Lock()
				timeDistribution.NotAttempted++
				timeDistMutex.Unlock()
				atomic.AddInt32(&notAttempted, 1)
			}

			// Send result to channel
			questionResultChan <- struct {
				index    int
				question QuestionTimeManagement
			}{i, questionReport}
		}(i, q)
	}

	// Collect question results
	for range questionsList {
		result := <-questionResultChan
		tempQuestions[result.index] = result.question
	}

	// Calculate difficulty for each subject in parallel
	difficultyWg := sync.WaitGroup{}
	difficultyMapMutex := sync.Mutex{}
	difficultyMap := make(map[string]QuestionDifficulty)

	for subject := range allSubjects {
		difficultyWg.Add(1)
		go func(subject string) {
			defer difficultyWg.Done()

			subjectMapsMutex.RLock()
			totalQuestionsInSubject := float64(subjectTotalQuestions[subject])
			attemptsCount := subjectAttempts[subject]
			timesTotal := subjectTimes[subject]
			errorsCount := subjectErrors[subject]
			subjectMapsMutex.RUnlock()

			if totalQuestionsInSubject == 0 {
				return
			}

			// Calculate average time for attempted questions
			var avgTime float64
			if attemptsCount > 0 {
				avgTime = timesTotal / float64(attemptsCount)

				// ADDED: Normalize the average time
				avgTime = normalizeTimeData(avgTime, 0, expectedBaseTime)
			}

			// Calculate factors using total questions as denominator
			timeFactor := math.Min((avgTime/expectedBaseTime), 1.0) * 0.5
			errorFactor := float64(errorsCount) / totalQuestionsInSubject * 0.5

			difficultyScore := timeFactor + errorFactor

			difficultyMapMutex.Lock()
			difficultyMap[subject] = QuestionDifficulty{
				Score:        difficultyScore,
				Level:        calculateDifficultyLevel(difficultyScore),
				TimeFactor:   timeFactor * 2.5,
				ErrorFactor:  errorFactor * 2.5,
				ExpectedTime: expectedBaseTime,
			}
			difficultyMapMutex.Unlock()
		}(subject)
	}

	difficultyWg.Wait()

	// Copy to report
	for subject, difficulty := range difficultyMap {
		report.ExamDifficulty.BySubject[subject] = difficulty
	}

	// Initialize subject stats map
	userStats.SubjectStats = make(map[string]SubjectUserStats)

	// Calculate per-subject stats for user in parallel
	subjectUserStatsWg := sync.WaitGroup{}
	subjectUserStatsMutex := sync.Mutex{}
	subjectUserStatsMap := make(map[string]SubjectUserStats)

	for subject := range allSubjects {
		subjectUserStatsWg.Add(1)
		go func(subject string) {
			defer subjectUserStatsWg.Done()

			var attempted, correct int

			subjectMapsMutex.RLock()
			totalInSubject := subjectTotalQuestions[subject]
			subjectMapsMutex.RUnlock()

			// Count attempted and correct answers for this subject
			for _, q := range questionsList {
				// Get subject from metadata
				var qSubject string
				for _, meta := range q.Metadata {
					if meta.Name == "subject" && meta.Value == subject {
						qSubject = meta.Value
						break
					}
				}

				if qSubject == subject {
					if userAns, ok := userAnswers[q.ID]; ok {
						attempted++
						// Check if answer is correct
						if userAns == correctAnswers[q.ID] {
							correct++
						}
					}
				}
			}

			// Calculate accuracy rate
			accuracyRate := 0.0
			if attempted > 0 {
				accuracyRate = float64(correct) / float64(attempted) * 100
			}

			// Store stats
			subjectUserStatsMutex.Lock()
			subjectUserStatsMap[subject] = SubjectUserStats{
				Subject:            subject,
				QuestionsAttempted: attempted,
				TotalQuestions:     totalInSubject,
				CorrectAnswers:     correct,
				AccuracyRate:       accuracyRate,
			}
			subjectUserStatsMutex.Unlock()
		}(subject)
	}

	subjectUserStatsWg.Wait()

	// Copy subject user stats to the report
	for subject, stats := range subjectUserStatsMap {
		userStats.SubjectStats[subject] = stats
	}

	// Calculate overall exam difficulty using total questions
	if globalStatsResult.globalStats.TotalAttempts > 0 {
		globalAvgTimePerQuestion := globalStatsResult.globalStats.AvgTime

		// ADDED: Normalize the global average time
		globalAvgTimePerQuestion = normalizeTimeData(globalAvgTimePerQuestion, 0, expectedBaseTime)

		timeDifficulty := math.Min((globalAvgTimePerQuestion/expectedBaseTime), 1.0) * 0.5

		totalQuestions := float64(len(questions))
		totalPossibleAnswers := float64(globalStatsResult.globalStats.TotalAttempts) * totalQuestions

		totalErrors := float64(globalStatsResult.globalStats.TotalErrors)
		unattemptedPerSession := totalQuestions - float64(globalStatsResult.globalStats.TotalAttempts)
		totalErrors += (unattemptedPerSession * float64(globalStatsResult.globalStats.TotalAttempts))

		errorDifficulty := (totalErrors / totalPossibleAnswers) * 0.5

		// Apply boundaries to ensure score is always between 0 and 1
		report.ExamDifficulty.Score = math.Max(0, math.Min(1, timeDifficulty+errorDifficulty))
		report.ExamDifficulty.Level = calculateDifficultyLevel(report.ExamDifficulty.Score)
	}

	// Calculate speed-based correctness rates
	totalFast := int(fastCorrect + fastIncorrect)
	if totalFast > 0 {
		userStats.FastCorrectRate = float64(fastCorrect) / float64(totalFast) * 100
	}

	totalAverage := int(averageCorrect + averageIncorrect)
	if totalAverage > 0 {
		userStats.AverageCorrectRate = float64(averageCorrect) / float64(totalAverage) * 100
	}

	totalSlow := int(slowCorrect + slowIncorrect)
	if totalSlow > 0 {
		userStats.SlowCorrectRate = float64(slowCorrect) / float64(totalSlow) * 100
	}

	// Calculate global accuracy rate
	if globalStatsResult.globalStats.TotalAttempts > 0 {
		totalQuestions := float64(len(questions))
		totalPossibleAnswers := float64(globalStatsResult.globalStats.TotalAttempts) * totalQuestions
		report.GlobalStats.AverageAccuracyRate = (1 - float64(globalStatsResult.globalStats.TotalErrors)/totalPossibleAnswers) * 100
	}

	// Finalize user stats
	if userStats.AttemptedQuestions > 0 {
		userStats.TimeSpent = float64(userStatsTimeSpent) / 1000000 // Adjust scale back
		userStats.AverageTimeSpent = userStats.TimeSpent / float64(userStats.AttemptedQuestions)
		userStats.CorrectAnswers = int(userStatsCorrectAnswers)
		userStats.AccuracyRate = float64(userStatsCorrectAnswers) / float64(userStats.AttemptedQuestions) * 100
	}

	// Set final report fields
	report.UserStats = userStats
	report.Questions = tempQuestions
	report.TimeDistribution = timeDistribution

	return report, nil
}

// Cached version of getTimeManagementReport
func getTimeManagementReport(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		sessionID := c.Param("sessionId")

		// Create cache key
		cacheKey := getSessionReportCacheKey(sessionID)

		// Try to get from cache first
		cachedData, err := redisClient.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return cached result
			c.Header("X-Cache", "HIT")
			c.Data(200, "application/json", []byte(cachedData))
			return
		}

		// Cache miss - generate report
		c.Header("X-Cache", "MISS")
		report, err := generateTimeManagementReport(dbx, sessionID)
		if err != nil {
			log.Printf("Error generating report: %v", err)
			c.JSON(500, gin.H{"error": "Failed to generate report"})
			return
		}

		// Serialize report to JSON
		reportJSON, err := json.Marshal(report)
		if err != nil {
			log.Printf("Error serializing report: %v", err)
			c.JSON(500, gin.H{"error": "Failed to serialize report"})
			return
		}

		// Store in cache
		err = redisClient.Set(ctx, cacheKey, reportJSON, cacheDuration).Err()
		if err != nil {
			log.Printf("Warning: Failed to cache report: %v", err)
			// Continue anyway, not critical
		}

		// Return response
		c.Data(200, "application/json", reportJSON)
	}
}

// Function to normalize time data - handles anomalies like unusually fast times
func normalizeTimeData(timeSpent, avgTime, expectedBaseTime float64) float64 {
	// Set a minimum reasonable time (e.g., 5% of expected base time)
	minReasonableTime := math.Max(5.0, expectedBaseTime*0.05)

	// If time is unreasonably low, adjust it
	if timeSpent < minReasonableTime {
		return minReasonableTime
	}

	// If avg time exists and is very low, normalize it too
	if avgTime > 0 && avgTime < minReasonableTime {
		return math.Max(timeSpent, minReasonableTime)
	}

	return timeSpent
}

// Optimized and cached version of getLatestTimeManagementReport
func getLatestTimeManagementReport(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userID := c.Param("userId")

		// Create cache key
		cacheKey := getLatestReportCacheKey(userID)

		// Try to get from cache first
		cachedData, err := redisClient.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return cached result
			c.Header("X-Cache", "HIT")
			c.Data(200, "application/json", []byte(cachedData))
			return
		}

		// Cache miss - query the database
		c.Header("X-Cache", "MISS")

		// Get the latest completed session for the user with all needed details in a single query
		var session struct {
			SessionID string    `db:"session_id"`
			ExamID    string    `db:"exam_id"`
			StartTime time.Time `db:"start_time"`
		}

		err = dbx.Get(&session, `
			SELECT session_id, exam_id, start_time
			FROM exam_sessions
			WHERE user_id = $1 AND status = 'COMPLETED'
			ORDER BY start_time DESC
			LIMIT 1
		`, userID)

		if err != nil {
			log.Printf("Error fetching latest session: %v", err)
			c.JSON(404, gin.H{"error": "No completed exam sessions found"})
			return
		}

		// Use the existing detailed report function for the latest session
		// We don't need to optimize this as much since it's for a single session
		report, err := generateTimeManagementReport(dbx, session.SessionID)
		if err != nil {
			log.Printf("Error generating report: %v", err)
			c.JSON(500, gin.H{"error": "Failed to generate report"})
			return
		}

		// Cache the report in Redis
		reportJSON, err := json.Marshal(report)
		if err != nil {
			log.Printf("Error serializing latest report: %v", err)
			// Continue even if serialization fails
		} else {
			// Store in cache with specified expiration
			err = redisClient.Set(ctx, cacheKey, reportJSON, cacheDuration).Err()
			if err != nil {
				log.Printf("Warning: Failed to cache latest report: %v", err)
				// Continue even if caching fails
			}
		}

		c.JSON(200, report)
	}
}

// New endpoint to invalidate cache for a session
func invalidateSessionCache(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		sessionID := c.Param("sessionId")

		// Define session cache key
		sessionCacheKey := getSessionReportCacheKey(sessionID)

		// Get the user ID for this session to invalidate user caches too
		var userID string
		err := dbx.Get(&userID, `
			SELECT user_id FROM exam_sessions WHERE session_id = $1
		`, sessionID)

		if err != nil {
			log.Printf("Warning: Failed to get user ID for cache invalidation: %v", err)
			// Continue anyway, not critical
		}

		// Create pipeline for batch deletion
		pipeline := redisClient.Pipeline()

		// Add session cache key to pipeline
		pipeline.Del(ctx, sessionCacheKey)

		// If we found a user ID, invalidate user caches too
		if userID != "" {
			userCacheKey := getUserReportCacheKey(userID)
			latestCacheKey := getLatestReportCacheKey(userID)
			pipeline.Del(ctx, userCacheKey)
			pipeline.Del(ctx, latestCacheKey)
		}

		// Execute pipeline
		_, err = pipeline.Exec(ctx)
		if err != nil {
			log.Printf("Error invalidating cache: %v", err)
			c.JSON(500, gin.H{"message": "Error invalidating cache", "error": err.Error()})
			return
		}

		c.JSON(200, gin.H{"message": "Cache invalidated successfully for session " + sessionID})
	}
}

// New endpoint to invalidate cache for a user
func invalidateUserCache(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userID := c.Param("userId")

		// Define cache keys to invalidate
		userCacheKey := getUserReportCacheKey(userID)
		latestCacheKey := getLatestReportCacheKey(userID)

		// Get the user's sessions to invalidate each session cache too
		var sessionIDs []string
		err := dbx.Select(&sessionIDs, `
			SELECT session_id FROM exam_sessions WHERE user_id = $1
		`, userID)

		if err != nil {
			log.Printf("Warning: Failed to get user sessions for cache invalidation: %v", err)
			// Continue anyway, this is not critical
		}

		// Create pipeline for batch deletion
		pipeline := redisClient.Pipeline()

		// Add user report keys to pipeline
		pipeline.Del(ctx, userCacheKey)
		pipeline.Del(ctx, latestCacheKey)

		// Add session keys to pipeline
		for _, sessionID := range sessionIDs {
			sessionCacheKey := getSessionReportCacheKey(sessionID)
			pipeline.Del(ctx, sessionCacheKey)
		}

		// Execute pipeline
		_, err = pipeline.Exec(ctx)
		if err != nil {
			log.Printf("Error invalidating cache: %v", err)
			c.JSON(500, gin.H{"message": "Error invalidating cache", "error": err.Error()})
			return
		}

		c.JSON(200, gin.H{"message": "Cache invalidated successfully for user " + userID})
	}
}

// Optimized and cached version of getTimeManagementReportAllSessions
func getTimeManagementReportAllSessions(dbx *sqlx.DB, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userID := c.Param("userId")

		// Create cache key
		cacheKey := getUserReportCacheKey(userID)

		// Try to get from cache first
		cachedData, err := redisClient.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return cached result
			c.Header("X-Cache", "HIT")
			c.Data(200, "application/json", []byte(cachedData))
			return
		}

		// Cache miss - query the database
		c.Header("X-Cache", "MISS")

		// Max number of sessions to process at once
		// This prevents memory issues with users who have 1000+ sessions
		// maxSessions := 250

		// Get sessions for the user
		var sessions []struct {
			SessionID string    `db:"session_id"`
			ExamID    string    `db:"exam_id"`
			StartTime time.Time `db:"start_time"`
		}

		err = dbx.Select(&sessions, `
			SELECT session_id, exam_id, start_time
			FROM exam_sessions
			WHERE user_id = $1 AND status = 'COMPLETED'
			ORDER BY start_time DESC
		`, userID,
		// maxSessions
		)

		if err != nil {
			log.Printf("Error fetching sessions: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch sessions"})
			return
		}

		if len(sessions) == 0 {
			c.JSON(404, gin.H{"error": "No completed exam sessions found"})
			return
		}

		// Extract session IDs for batch processing
		sessionIDs := make([]string, len(sessions))
		for i, s := range sessions {
			sessionIDs[i] = s.SessionID
		}

		// Get all unique exam IDs
		examIDMap := make(map[string]bool)
		for _, s := range sessions {
			examIDMap[s.ExamID] = true
		}

		examIDs := make([]string, 0, len(examIDMap))
		for examID := range examIDMap {
			examIDs = append(examIDs, examID)
		}

		// OPTIMIZATION 1: Batch fetch all session data
		var sessionsData []struct {
			SessionID string          `db:"session_id"`
			ExamID    string          `db:"exam_id"`
			Answers   json.RawMessage `db:"answers"`
		}

		err = dbx.Select(&sessionsData, `
			SELECT session_id, exam_id, answers
			FROM exam_sessions
			WHERE session_id = ANY($1)
		`, pq.Array(sessionIDs))

		if err != nil {
			log.Printf("Error fetching session data: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch session data"})
			return
		}

		// Create a map for fast lookup
		sessionDataMap := make(map[string]sessionQueryData)
		for _, s := range sessionsData {
			sessionDataMap[s.SessionID] = sessionQueryData{
				sessionID: s.SessionID,
				examID:    s.ExamID,
				userID:    userID,
				answers:   s.Answers,
			}
		}

		// OPTIMIZATION 2: Batch fetch all timing data
		var timesData []struct {
			SessionID     string          `db:"exam_session_id"`
			QuestionTimes json.RawMessage `db:"question_times"`
		}

		err = dbx.Select(&timesData, `
			SELECT exam_session_id, question_times
			FROM exam_gamification
			WHERE exam_session_id = ANY($1)
		`, pq.Array(sessionIDs))

		if err != nil {
			log.Printf("Error fetching timing data: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch timing data"})
			return
		}

		// Process all timing data concurrently
		timesWg := sync.WaitGroup{}
		timesChan := make(chan sessionTimesData, len(timesData))

		for _, t := range timesData {
			timesWg.Add(1)
			go func(sessionID string, raw json.RawMessage) {
				defer timesWg.Done()

				var times map[string]float64
				err := json.Unmarshal(raw, &times)
				if err != nil {
					log.Printf("Error parsing timing data for session %s: %v", sessionID, err)
					return
				}

				timesChan <- sessionTimesData{
					sessionID:     sessionID,
					questionTimes: times,
				}
			}(t.SessionID, t.QuestionTimes)
		}

		// Wait for all timing data to be processed
		timesWg.Wait()
		close(timesChan)

		// Create a map of session ID to timing data
		sessionTimesMap := make(map[string]map[string]float64)
		for t := range timesChan {
			sessionTimesMap[t.sessionID] = t.questionTimes
		}

		// OPTIMIZATION 3: Batch fetch all exam details
		var examsData []struct {
			ID        string           `db:"id"`
			Name      string           `db:"name"`
			Duration  PostgresDuration `db:"duration"`
			Questions int              `db:"question_count"`
		}

		err = dbx.Select(&examsData, `
			SELECT 
				ae.id, 
				ae.name, 
				ae.duration,
				COUNT(DISTINCT eq.id) as question_count
			FROM available_exams ae
			JOIN exam_questions eq ON ae.id = eq.exam_id
			WHERE ae.id = ANY($1)
			GROUP BY ae.id, ae.name, ae.duration
		`, pq.Array(examIDs))

		if err != nil {
			log.Printf("Error fetching exam details: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch exam details"})
			return
		}

		// Create a map of exam ID to exam details
		examDetailsMap := make(map[string]struct {
			Name      string
			Duration  PostgresDuration
			Questions int
		})

		for _, e := range examsData {
			examDetailsMap[e.ID] = struct {
				Name      string
				Duration  PostgresDuration
				Questions int
			}{
				Name:      e.Name,
				Duration:  e.Duration,
				Questions: e.Questions,
			}
		}

		// OPTIMIZATION 4: Batch fetch all questions with metadata
		type examQuestionForPreload struct {
			ID       string `db:"id"`
			ExamID   string `db:"exam_id"`
			Title    string `db:"title"`
			MetaName string `db:"meta_name"`
			MetaVal  string `db:"meta_val"`
			MetaLvl  int    `db:"meta_lvl"`
		}

		var questionsWithMetadata []examQuestionForPreload

		// Using a single query to join exam_questions and question_metadata
		questionsQuery := `
			SELECT 
				q.id, 
				q.exam_id, 
				q.title,
				qm.name AS meta_name,
				qm.value AS meta_val,
				qm.level AS meta_lvl
			FROM exam_questions q
			LEFT JOIN question_metadata qm ON q.id = qm.question_id AND q.exam_id = qm.exam_id
			WHERE q.exam_id = ANY($1)
		`
		err = dbx.Select(&questionsWithMetadata, questionsQuery, pq.Array(examIDs))
		if err != nil {
			log.Printf("Error fetching questions with metadata: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch question data"})
			return
		}

		// Fetch all correct answers in a single query
		type correctAnswerEntry struct {
			QuestionID   string `db:"question_id"`
			ExamID       string `db:"exam_id"`
			CorrectOptID string `db:"option_id"`
		}

		var correctAnswersData []correctAnswerEntry
		correctAnswersQuery := `
			SELECT 
				question_id, 
				exam_id, 
				id AS option_id
			FROM question_options
			WHERE exam_id = ANY($1) AND is_correct = true
		`
		err = dbx.Select(&correctAnswersData, correctAnswersQuery, pq.Array(examIDs))
		if err != nil {
			log.Printf("Error fetching correct answers: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch correct answers"})
			return
		}

		// Process questions and metadata
		examQuestionsMap := make(map[string]map[string]*ExamQuestion) // examID -> questionID -> ExamQuestion
		examCorrectAnswers := make(map[string]map[string]string)      // examID -> questionID -> correctOptionID

		// Initialize maps
		for _, examID := range examIDs {
			examQuestionsMap[examID] = make(map[string]*ExamQuestion)
			examCorrectAnswers[examID] = make(map[string]string)
		}

		// Process correct answers
		for _, ca := range correctAnswersData {
			examCorrectAnswers[ca.ExamID][ca.QuestionID] = ca.CorrectOptID
		}

		// Process questions and metadata
		for _, q := range questionsWithMetadata {
			if _, exists := examQuestionsMap[q.ExamID][q.ID]; !exists {
				examQuestionsMap[q.ExamID][q.ID] = &ExamQuestion{
					ID:       q.ID,
					ExamID:   q.ExamID,
					Title:    q.Title,
					Metadata: []QuestionMetadata{},
				}
			}

			// Only add metadata if it exists
			if q.MetaName != "" {
				examQuestionsMap[q.ExamID][q.ID].Metadata = append(
					examQuestionsMap[q.ExamID][q.ID].Metadata,
					QuestionMetadata{
						Name:  q.MetaName,
						Value: q.MetaVal,
						Level: q.MetaLvl,
					},
				)
			}
		}

		// OPTIMIZATION 5: Batch fetch global stats by exam
		var globalStatsData []struct {
			ExamID        string  `db:"exam_id"`
			AvgTime       float64 `db:"avg_time"`
			FastestTime   float64 `db:"fastest_time"`
			SlowestTime   float64 `db:"slowest_time"`
			TotalAttempts int     `db:"total_attempts"`
			TotalErrors   int     `db:"total_errors"`
			AccuracyRate  float64 `db:"accuracy_rate"`
		}

		globalStatsQuery := `
			WITH question_counts AS (
				SELECT 
					exam_id, 
					COUNT(*) as question_count
				FROM exam_questions
				WHERE exam_id = ANY($1)
				GROUP BY exam_id
			),
			session_stats AS (
				SELECT 
					es.exam_id,
					eg.exam_session_id,
					SUM(CAST(value AS DOUBLE PRECISION)) as total_time,
					COUNT(*) as questions_attempted,
					SUM(CASE WHEN es.answers->>key != qo.id 
						THEN 1 ELSE 0 END) as error_count
				FROM exam_gamification eg
				JOIN exam_sessions es ON eg.exam_session_id = es.session_id
				CROSS JOIN LATERAL jsonb_each_text(eg.question_times) times(key, value)
				JOIN exam_questions eq ON es.exam_id = eq.exam_id AND times.key = eq.id
				JOIN question_options qo ON eq.id = qo.question_id AND eq.exam_id = qo.exam_id 
                   AND qo.is_correct = true
				WHERE es.exam_id = ANY($1) AND es.status = 'COMPLETED'
				GROUP BY es.exam_id, eg.exam_session_id
			)
			SELECT 
				ss.exam_id,
				AVG(total_time/NULLIF(questions_attempted, 0))::numeric(10,3) as avg_time,
				MIN(total_time/NULLIF(questions_attempted, 0))::numeric(10,3) as fastest_time,
				MAX(total_time/NULLIF(questions_attempted, 0))::numeric(10,3) as slowest_time,
				COUNT(DISTINCT exam_session_id) as total_attempts,
				SUM(error_count) as total_errors,
				(1 - (SUM(error_count)::float / 
						(SUM(questions_attempted) + SUM((qc.question_count - questions_attempted)))))::numeric(10,2) * 100 as accuracy_rate
			FROM session_stats ss
			JOIN question_counts qc ON ss.exam_id = qc.exam_id
			GROUP BY ss.exam_id
		`

		err = dbx.Select(&globalStatsData, globalStatsQuery, pq.Array(examIDs))

		if err != nil {
			log.Printf("Error fetching global stats: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch global statistics"})
			return
		}

		// Create a map of exam ID to global stats
		globalStatsMap := make(map[string]struct {
			AvgTime       float64
			FastestTime   float64
			SlowestTime   float64
			TotalAttempts int
			TotalErrors   int
			AccuracyRate  float64
		})

		for _, g := range globalStatsData {
			globalStatsMap[g.ExamID] = struct {
				AvgTime       float64
				FastestTime   float64
				SlowestTime   float64
				TotalAttempts int
				TotalErrors   int
				AccuracyRate  float64
			}{
				AvgTime:       g.AvgTime,
				FastestTime:   g.FastestTime,
				SlowestTime:   g.SlowestTime,
				TotalAttempts: g.TotalAttempts,
				TotalErrors:   g.TotalErrors,
				AccuracyRate:  g.AccuracyRate,
			}
		}

		// OPTIMIZATION 6: Batch fetch subject stats
		var subjectStatsData []struct {
			ExamID             string  `db:"exam_id"`
			Subject            string  `db:"subject"`
			TotalQuestions     int     `db:"total_questions"`
			QuestionsAttempted int     `db:"questions_attempted"`
			TotalCorrect       int     `db:"total_correct"`
			TotalErrors        int     `db:"total_errors"`
			AccuracyRate       float64 `db:"accuracy_rate"`
		}

		subjectStatsQuery := `
			WITH questions_with_subject AS (
				SELECT 
					q.id as question_id,
					q.exam_id,
					qm.value as subject,
					qo.id as correct_answer_id
				FROM exam_questions q
				JOIN question_metadata qm ON q.id = qm.question_id AND q.exam_id = qm.exam_id
				JOIN question_options qo ON q.id = qo.question_id AND q.exam_id = qo.exam_id AND qo.is_correct = true
				WHERE q.exam_id = ANY($1) AND qm.name = 'subject'
			),
			answer_stats AS (
				SELECT 
					qs.exam_id,
					qs.subject,
					qs.question_id,
					COUNT(DISTINCT es.session_id) as attempt_count,
					SUM(CASE WHEN es.answers->qs.question_id IS NOT NULL THEN 1 ELSE 0 END) as is_attempted,
					SUM(CASE 
						WHEN es.answers->qs.question_id#>>'{}'= qs.correct_answer_id THEN 1 
						ELSE 0 
					END) as is_correct
				FROM questions_with_subject qs
				JOIN exam_sessions es ON 
					es.exam_id = qs.exam_id 
					AND es.status = 'COMPLETED'
				GROUP BY qs.exam_id, qs.subject, qs.question_id, qs.correct_answer_id
			)
			SELECT 
				exam_id,
				subject,
				COUNT(DISTINCT question_id) as total_questions,
				SUM(is_attempted) as questions_attempted,
				SUM(is_correct) as total_correct,
				SUM(is_attempted - is_correct) as total_errors,
				CASE 
					WHEN SUM(is_attempted) > 0 
					THEN ROUND((SUM(is_correct)::float / SUM(is_attempted) * 100)::numeric, 1)
					ELSE 0 
				END as accuracy_rate
			FROM answer_stats
			WHERE subject IS NOT NULL
			GROUP BY exam_id, subject
		`

		err = dbx.Select(&subjectStatsData, subjectStatsQuery, pq.Array(examIDs))

		if err != nil {
			log.Printf("Error fetching subject stats: %v", err)
			// Continue anyway as this is not critical
		}

		// Create a map of exam ID to subject stats
		examSubjectStatsMap := make(map[string]map[string]SubjectGlobalStats)
		for _, s := range subjectStatsData {
			if _, exists := examSubjectStatsMap[s.ExamID]; !exists {
				examSubjectStatsMap[s.ExamID] = make(map[string]SubjectGlobalStats)
			}

			examSubjectStatsMap[s.ExamID][s.Subject] = SubjectGlobalStats{
				Subject:                 s.Subject,
				TotalQuestions:          s.TotalQuestions,
				TotalQuestionsAttempted: s.QuestionsAttempted,
				TotalCorrect:            s.TotalCorrect,
				TotalErrors:             s.TotalErrors,
				AccuracyRate:            s.AccuracyRate,
			}
		}

		// OPTIMIZATION 7: Batch fetch average times for questions
		var avgTimesData []struct {
			ExamID      string  `db:"exam_id"`
			QuestionID  string  `db:"question_id"`
			AverageTime float64 `db:"avg_time"`
		}

		avgTimesQuery := `
			WITH question_times_expanded AS (
				SELECT 
					eg.exam_session_id,
					es.exam_id,
					(jsonb_each_text(eg.question_times)).*
				FROM exam_gamification eg
				JOIN exam_sessions es ON eg.exam_session_id = es.session_id
				WHERE es.exam_id = ANY($1)
				AND es.status = 'COMPLETED'
			)
			SELECT 
				exam_id,
				key as question_id,
				ROUND(AVG(CAST(value AS DOUBLE PRECISION))::numeric, 3) as avg_time
			FROM question_times_expanded
			GROUP BY exam_id, key
		`

		err = dbx.Select(&avgTimesData, avgTimesQuery, pq.Array(examIDs))

		if err != nil {
			log.Printf("Error fetching average times: %v", err)
			c.JSON(500, gin.H{"error": "Failed to fetch average times"})
			return
		}

		// Create a map of exam ID to question ID to average time
		examAvgTimesMap := make(map[string]map[string]float64)
		for _, a := range avgTimesData {
			if _, exists := examAvgTimesMap[a.ExamID]; !exists {
				examAvgTimesMap[a.ExamID] = make(map[string]float64)
			}

			examAvgTimesMap[a.ExamID][a.QuestionID] = a.AverageTime
		}

		// Initialize aggregate report
		aggregateReport := &TimeManagementReport{
			UserID:   userID,
			Sessions: make([]SessionReport, 0, len(sessions)),
		}

		// Track totals for user stats
		var totalTimeSpent float64
		var totalCorrectAnswers int
		var totalAttemptedQuestions int

		// Use a worker pool to process sessions in parallel
		workerCount := runtime.NumCPU()
		if workerCount > 8 {
			workerCount = 8 // Cap at 8 workers
		}

		// Create channels for work distribution and results collection
		workChan := make(chan int, len(sessions))
		resultChan := make(chan SessionReport, len(sessions))

		// Start worker goroutines
		var wg sync.WaitGroup
		for i := 0; i < workerCount; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()

				for idx := range workChan {
					session := sessions[idx]
					sessionID := session.SessionID
					examID := session.ExamID

					// Skip if we don't have all required data
					sessionData, hasSessionData := sessionDataMap[sessionID]
					if !hasSessionData {
						log.Printf("Missing session data for %s", sessionID)
						continue
					}

					sessionTimes, hasSessionTimes := sessionTimesMap[sessionID]
					if !hasSessionTimes {
						log.Printf("Missing timing data for %s", sessionID)
						continue
					}

					examDetails, hasExamDetails := examDetailsMap[examID]
					if !hasExamDetails {
						log.Printf("Missing exam details for %s", examID)
						continue
					}

					questions, hasQuestions := examQuestionsMap[examID]
					if !hasQuestions {
						log.Printf("Missing question data for %s", examID)
						continue
					}

					correctAnswers, hasCorrectAnswers := examCorrectAnswers[examID]
					if !hasCorrectAnswers {
						log.Printf("Missing correct answers for %s", examID)
						continue
					}

					// Parse user answers
					var userAnswers map[string]string
					err := json.Unmarshal(sessionData.answers, &userAnswers)
					if err != nil {
						log.Printf("Error parsing answers for session %s: %v", sessionID, err)
						continue
					}

					// Get question average times
					avgTimes, hasAvgTimes := examAvgTimesMap[examID]
					if !hasAvgTimes {
						log.Printf("Warning: No average times for exam %s", examID)
						avgTimes = make(map[string]float64)
					}

					// Create correctness and time counters
					questionTimeStatus := make(map[string]string)
					var correct, attempted int
					var timeSpent float64

					// Time distribution counters
					timeDistribution := TimeDistribution{
						NotAttempted: examDetails.Questions,
					}

					// Calculate expected base time
					expectedBaseTime := examDetails.Duration.Seconds() / float64(examDetails.Questions)

					// Initialize user stats with subject map
					userStats := UserStats{
						SubjectStats: make(map[string]SubjectUserStats),
					}

					// Create a map to track subjects and their stats
					subjectQuestionsCount := make(map[string]int)
					subjectAttemptsCount := make(map[string]int)
					subjectCorrectCount := make(map[string]int)

					// Process each question to get basic stats
					for questionID, question := range questions {
						// Get subject from metadata
						var subject string
						for _, meta := range question.Metadata {
							if meta.Name == "subject" {
								subject = meta.Value
								if subject != "" {
									subjectQuestionsCount[subject]++
								}
								break
							}
						}

						userAns, isAttempted := userAnswers[questionID]
						if !isAttempted {
							continue
						}

						timeSpentOnQ, hasTime := sessionTimes[questionID]
						if !hasTime {
							continue
						}

						correctAns := correctAnswers[questionID]
						isCorrect := userAns == correctAns
						avgTime := avgTimes[questionID]

						// Update counters
						attempted++
						timeSpent += timeSpentOnQ
						if isCorrect {
							correct++
						}

						// Track subject statistics
						if subject != "" {
							subjectAttemptsCount[subject]++
							if isCorrect {
								subjectCorrectCount[subject]++
							}
						}

						// Determine time status
						var timeStatus string
						if timeSpentOnQ < avgTime*0.8 {
							if isCorrect {
								timeStatus = TimeStatusFastCorrect
								timeDistribution.FastCorrect++
							} else {
								timeStatus = TimeStatusFastIncorrect
								timeDistribution.FastIncorrect++
							}
						} else if timeSpentOnQ > avgTime*1.2 {
							if isCorrect {
								timeStatus = TimeStatusSlowCorrect
								timeDistribution.SlowCorrect++
							} else {
								timeStatus = TimeStatusSlowIncorrect
								timeDistribution.SlowIncorrect++
							}
						} else {
							if isCorrect {
								timeStatus = TimeStatusAverageCorrect
								timeDistribution.AverageCorrect++
							} else {
								timeStatus = TimeStatusAverageIncorrect
								timeDistribution.AverageIncorrect++
							}
						}

						questionTimeStatus[questionID] = timeStatus
					}

					// Adjust not attempted count
					timeDistribution.NotAttempted = examDetails.Questions - attempted

					// Calculate user stats
					userStats.TimeSpent = timeSpent
					userStats.CorrectAnswers = correct
					userStats.AttemptedQuestions = attempted

					if attempted > 0 {
						userStats.AverageTimeSpent = timeSpent / float64(attempted)
						userStats.AccuracyRate = float64(correct) / float64(attempted) * 100
					}

					// Calculate speed-based correctness rates
					totalFast := timeDistribution.FastCorrect + timeDistribution.FastIncorrect
					if totalFast > 0 {
						userStats.FastCorrectRate = float64(timeDistribution.FastCorrect) / float64(totalFast) * 100
					}

					totalAverage := timeDistribution.AverageCorrect + timeDistribution.AverageIncorrect
					if totalAverage > 0 {
						userStats.AverageCorrectRate = float64(timeDistribution.AverageCorrect) / float64(totalAverage) * 100
					}

					totalSlow := timeDistribution.SlowCorrect + timeDistribution.SlowIncorrect
					if totalSlow > 0 {
						userStats.SlowCorrectRate = float64(timeDistribution.SlowCorrect) / float64(totalSlow) * 100
					}

					// Calculate subject stats
					for subject, count := range subjectQuestionsCount {
						attempts := subjectAttemptsCount[subject]
						correct := subjectCorrectCount[subject]
						accuracyRate := 0.0
						if attempts > 0 {
							accuracyRate = float64(correct) / float64(attempts) * 100
						}

						userStats.SubjectStats[subject] = SubjectUserStats{
							Subject:            subject,
							TotalQuestions:     count,
							QuestionsAttempted: attempts,
							CorrectAnswers:     correct,
							AccuracyRate:       accuracyRate,
						}
					}

					// Build global stats
					globalStats := GlobalStats{
						SubjectStats: make(map[string]SubjectGlobalStats),
					}

					// Get global stats for this exam
					examGlobalStats, hasGlobalStats := globalStatsMap[examID]
					if hasGlobalStats {
						globalStats.AverageTimeSpent = examGlobalStats.AvgTime
						globalStats.FastestTime = examGlobalStats.FastestTime
						globalStats.SlowestTime = examGlobalStats.SlowestTime
						globalStats.TotalAttempts = examGlobalStats.TotalAttempts
						globalStats.AverageAccuracyRate = examGlobalStats.AccuracyRate
					}

					// Get subject stats for this exam
					if examSubjectStats, hasSubjectStats := examSubjectStatsMap[examID]; hasSubjectStats {
						for subject, stats := range examSubjectStats {
							globalStats.SubjectStats[subject] = stats
						}
					}

					// Create minimal session report (no detailed questions)
					sessionReport := SessionReport{
						ExamID:           examID,
						ExamName:         examDetails.Name,
						SessionID:        sessionID,
						TotalQuestions:   examDetails.Questions,
						ExpectedBaseTime: expectedBaseTime,
						UserStats:        userStats,
						GlobalStats:      globalStats,
						TimeDistribution: timeDistribution,
						Questions:        []QuestionTimeManagement{}, // Empty for efficiency in bulk report
					}

					resultChan <- sessionReport
				}
			}()
		}

		// Send work to the worker pool
		for i := range sessions {
			workChan <- i
		}
		close(workChan)

		// Wait for all workers to finish
		wg.Wait()
		close(resultChan)

		// Collect results and process them
		for result := range resultChan {
			// Add session to report
			aggregateReport.Sessions = append(aggregateReport.Sessions, result)

			// Update aggregate stats
			totalTimeSpent += result.UserStats.TimeSpent
			totalCorrectAnswers += result.UserStats.CorrectAnswers
			totalAttemptedQuestions += result.UserStats.AttemptedQuestions
		}

		// Calculate overall user stats
		if totalAttemptedQuestions > 0 {
			aggregateReport.UserStats = UserStats{
				TimeSpent:          totalTimeSpent,
				AverageTimeSpent:   totalTimeSpent / float64(totalAttemptedQuestions),
				CorrectAnswers:     totalCorrectAnswers,
				AttemptedQuestions: totalAttemptedQuestions,
				AccuracyRate:       float64(totalCorrectAnswers) / float64(totalAttemptedQuestions) * 100,
				SubjectStats:       make(map[string]SubjectUserStats),
			}
		}

		// Cache the report in Redis
		reportJSON, err := json.Marshal(aggregateReport)
		if err != nil {
			log.Printf("Error serializing aggregate report: %v", err)
			// Continue even if serialization fails
		} else {
			// Store in cache with specified expiration
			err = redisClient.Set(ctx, cacheKey, reportJSON, cacheDuration).Err()
			if err != nil {
				log.Printf("Warning: Failed to cache user report: %v", err)
				// Continue even if caching fails
			}
		}

		c.JSON(200, aggregateReport)
	}
}

// Register all routes
func RegisterTimeManagementRoutes(r *gin.Engine, dbx *sqlx.DB, redisClient *redis.Client) {
	r.GET("/v0/reports/time-management/:sessionId", getTimeManagementReport(dbx, redisClient))
	r.GET("/v0/reports/time-management/user/:userId", getTimeManagementReportAllSessions(dbx, redisClient))
	r.GET("/v0/reports/time-management/user/:userId/latest", getLatestTimeManagementReport(dbx, redisClient))

	// Add new cache invalidation endpoints
	r.POST("/v0/reports/time-management/invalidate/user/:userId", invalidateUserCache(dbx, redisClient))
	r.POST("/v0/reports/time-management/invalidate/session/:sessionId", invalidateSessionCache(dbx, redisClient))
}
