services:
  # db:
  #   image: postgres:latest
  #   environment:
  #     POSTGRES_PASSWORD: Capcapcap123
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - pgdata:/var/lib/postgresql/data
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql
  #   networks:
  #     - my_network

  # pgbouncer1:
  #   image: pgbouncer/pgbouncer
  #   ports:
  #     - "6432:6432"
  #   environment:
  #     DATABASES_HOST: db
  #     DATABASES_PORT: 5432
  #     DATABASES_USER: gnomefin
  #     DATABASES_PASSWORD: Capcapcap123
  #     PGBOUNCER_AUTH_TYPE: md5
  #     PGBOUNCER_AUTH_FILE: /etc/pgbouncer/userlist.txt
  #     PGBOUNCER_DEFAULT_POOL_SIZE: 20000
  #     PGBOUNCER_MAX_CLIENT_CONN: 20000
  #     PGBOUNCER_POOL_MODE: transaction
  #   volumes:
  #     - ./pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt:ro
  #   networks:
  #     - my_network

  # pgbouncer2:
  #   image: pgbouncer/pgbouncer
  #   ports:
  #     - "6433:6432"
  #   environment:
  #     DATABASES_HOST: db
  #     DATABASES_PORT: 5432
  #     DATABASES_USER: gnomefin
  #     DATABASES_PASSWORD: Capcapcap123
  #     PGBOUNCER_AUTH_TYPE: md5
  #     PGBOUNCER_AUTH_FILE: /etc/pgbouncer/userlist.txt
  #     PGBOUNCER_DEFAULT_POOL_SIZE: 20000
  #     PGBOUNCER_MAX_CLIENT_CONN: 20000
  #     PGBOUNCER_POOL_MODE: transaction
  #   volumes:
  #     - ./pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt:ro
  #   networks:
  #     - my_network

  # pgbouncer3:
  #   image: pgbouncer/pgbouncer
  #   ports:
  #     - "6434:6432"
  #   environment:
  #     DATABASES_HOST: db
  #     DATABASES_PORT: 5432
  #     DATABASES_USER: gnomefin
  #     DATABASES_PASSWORD: Capcapcap123
  #     PGBOUNCER_AUTH_TYPE: md5
  #     PGBOUNCER_AUTH_FILE: /etc/pgbouncer/userlist.txt
  #     PGBOUNCER_DEFAULT_POOL_SIZE: 20000
  #     PGBOUNCER_MAX_CLIENT_CONN: 20000
  #     PGBOUNCER_POOL_MODE: transaction
  #   volumes:
  #     - ./pgbouncer/userlist.txt:/etc/pgbouncer/userlist.txt:ro
  #   networks:
  #     - my_network

  # haproxy:
  #   image: haproxy:latest
  #   ports:
  #     - "6435:6435"  # Expose HAProxy on port 6435
  #   volumes:
  #     - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
  #   networks:
  #     - my_network

  # pgadmin:
  #   image: dpage/pgadmin4:8.9
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: Capcapcap123
  #   ports:
  #     - "5050:80"
  #   volumes:
  #     - pgadmin:/var/lib/pgadmin
  #   networks:
  #     - my_network

  # redis:
  #   image: redis/redis-stack:latest
  #   environment:
  #     REDIS_ARGS: "--requirepass cassieinkl"
  #   ports:
  #     - "6379:6379"
  #     - "8001:8001"
  #   networks:
  #     - my_network
  redisinsight:
    image: redis/redisinsight:latest
    ports:
      - "5540:5540"
    networks:
      - my_network
    volumes:
      - redisinsight:/data

  redis:
    image: redis/redis-stack:latest
    environment:
      REDIS_ARGS: "--requirepass cassieinkl"
    ports:
      - "6379:6379"
      - "8001:8001"
    networks:
      - my_network

networks:
  my_network:
    driver: bridge

volumes:
  redisinsight:
    external: true
