-- Step 1: Add indexes for the new name columns
-- Ensure indexes are created only if they do not already exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'public' AND indexname = 'idx_user_demographics_province_name'
    ) THEN
        CREATE INDEX idx_user_demographics_province_name ON user_demographics(province_name);
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'public' AND indexname = 'idx_user_demographics_city_name'
    ) THEN
        CREATE INDEX idx_user_demographics_city_name ON user_demographics(city_name);
    END IF;
END $$;

-- Step 2: Create a backup table if it does not already exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'user_demographics_backup'
    ) THEN
        CREATE TABLE user_demographics_backup AS SELECT * FROM user_demographics;
        RAISE NOTICE 'Backup table created successfully.';
    ELSE
        RAISE NOTICE 'Backup table already exists. Skipping creation.';
    END IF;
END $$;

-- Step 3: Clean data by removing empty strings
-- This step ensures that empty strings are converted to NULL
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM user_demographics LIMIT 1) THEN
        UPDATE user_demographics SET province_id = NULL WHERE province_id = '';
        UPDATE user_demographics SET city_id = NULL WHERE city_id = '';
        UPDATE user_demographics SET district_id = NULL WHERE district_id = '';
        UPDATE user_demographics SET village_id = NULL WHERE village_id = '';
        UPDATE user_demographics SET education_level_id = NULL WHERE education_level_id = '';
        UPDATE user_demographics SET program_study_id = NULL WHERE program_study_id = '';
        RAISE NOTICE 'Empty strings cleaned and set to NULL.';
    ELSE
        RAISE NOTICE 'Table is empty. Skipping data cleaning.';
    END IF;
END $$;

-- Step 4: Convert columns to BIGINT using a safe, step-by-step approach
-- Each column conversion is wrapped in its own transaction to avoid failures

-- Function to safely convert a column to BIGINT
CREATE OR REPLACE FUNCTION convert_to_bigint(target_table_name TEXT, target_column_name TEXT) RETURNS VOID AS $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = target_table_name AND column_name = target_column_name
    ) THEN
        RAISE NOTICE 'Column % does not exist in table %. Skipping conversion.', target_column_name, target_table_name;
        RETURN;
    END IF;

    -- Check if the new column already exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = target_table_name AND column_name = target_column_name || '_new'
    ) THEN
        -- Add the new column
        EXECUTE format('ALTER TABLE %I ADD COLUMN %I BIGINT', target_table_name, target_column_name || '_new');
        -- Copy data from the old column to the new column
        EXECUTE format('UPDATE %I SET %I = %I::BIGINT WHERE %I IS NOT NULL', target_table_name, target_column_name || '_new', target_column_name, target_column_name);
        -- Drop the old column
        EXECUTE format('ALTER TABLE %I DROP COLUMN %I', target_table_name, target_column_name);
        -- Rename the new column to the original column name
        EXECUTE format('ALTER TABLE %I RENAME COLUMN %I TO %I', target_table_name, target_column_name || '_new', target_column_name);
        RAISE NOTICE 'Column % in table % converted to BIGINT.', target_column_name, target_table_name;
    ELSE
        RAISE NOTICE 'Column %_new already exists in table %. Skipping conversion.', target_column_name, target_table_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Convert each column to BIGINT
SELECT convert_to_bigint('user_demographics', 'province_id');
SELECT convert_to_bigint('user_demographics', 'city_id');
SELECT convert_to_bigint('user_demographics', 'district_id');
SELECT convert_to_bigint('user_demographics', 'village_id');
SELECT convert_to_bigint('user_demographics', 'education_level_id');
SELECT convert_to_bigint('user_demographics', 'program_study_id');

-- Final Commit
COMMIT;