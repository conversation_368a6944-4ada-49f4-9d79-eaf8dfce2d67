import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from 'redis';

// Initialize Redis client
const client = createClient({
  url: process.env.REDIS_URL
});

client.on('error', (err) => console.log('Redis Client Error', err));

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    await client.connect();

    const { orderId, token } = req.body;

    if (!orderId || !token) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Store the payment info in Redis
    // The key is the orderId, the value is the token
    // Set an expiration of 1 hour (3600 seconds)
    await client.set(`payment:${orderId}`, token, {
      EX: 3600
    });

    await client.quit();
    res.status(200).json({ message: 'Payment info stored successfully' });
  } catch (error) {
    console.error('Error storing payment info:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}