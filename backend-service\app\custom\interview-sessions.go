package custom

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

type InterviewSession struct {
	ID              int        `json:"id" db:"id"`
	SessionId       string     `json:"sessionId" db:"session_id"`
	UserId          string     `json:"userId" db:"user_id"`
	UserEmail       string     `json:"userEmail" db:"user_email"`
	UserName        string     `json:"userName" db:"user_name"`
	InterviewId     string     `json:"interviewId" db:"interview_id"`
	Category        string     `json:"category" db:"category"`
	Type            string     `json:"type" db:"type"`
	Status          string     `json:"status" db:"status"`
	RecordingURL    string     `json:"recordingUrl" db:"recording_url"`
	TranscriptURL   string     `json:"transcriptUrl" db:"transcript_url"`
	Duration        string     `json:"duration" db:"duration"`
	DurationSeconds int        `json:"durationSeconds" db:"duration_seconds"`
	StartTime       time.Time  `json:"startTime" db:"start_time"`
	EndTime         *time.Time `json:"endTime,omitempty" db:"end_time"`
	CreatedAt       time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt       time.Time  `json:"updatedAt" db:"updated_at"`
}

// RegisterInterviewSessionsRoutes registers the session-related routes
func RegisterInterviewSessionsRoutes(r *gin.Engine, dbx *sqlx.DB) {
	r.POST("/v0/ai-interview/sessions", handleCreateSession(dbx))
	r.GET("/v0/ai-interview/sessions/interview_:session_id", handleGetSession(dbx))
	r.PUT("/v0/ai-interview/sessions/interview_:session_id", handleUpdateSession(dbx))
	r.GET("/v0/ai-interview/users/:user_id/sessions", handleListUserSessions(dbx))
}

func handleCreateSession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var session InterviewSession
		if err := c.ShouldBindJSON(&session); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
			return
		}

		// Validate required fields
		if session.SessionId == "" || session.UserId == "" || session.UserEmail == "" ||
			session.UserName == "" || session.InterviewId == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields"})
			return
		}

		// Set defaults if not provided
		if session.Category == "" {
			session.Category = "interview"
		}
		if session.Type == "" {
			session.Type = "ai_interview"
		}
		if session.Status == "" {
			session.Status = "in_progress"
		}
		if session.RecordingURL == "" {
			session.RecordingURL = fmt.Sprintf("https://cdn-google.terang.ai/recordings/%s/recording.ogg", session.SessionId)
		}
		if session.TranscriptURL == "" {
			session.TranscriptURL = fmt.Sprintf("https://cdn-google.terang.ai/recordings/%s/transcript.json", session.SessionId)
		}

		query := `
INSERT INTO interview_sessions (
session_id, user_id, user_email, user_name, 
interview_id, category, type, status, 
recording_url, transcript_url, duration, duration_seconds, start_time
) VALUES (
$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, CURRENT_TIMESTAMP
) RETURNING id, created_at, updated_at, start_time`

		// Set default duration if not provided
		if session.Duration == "" {
			session.Duration = "01:00:00"
			session.DurationSeconds = 3600
		}

		err := dbx.QueryRowContext(
			c.Request.Context(),
			query,
			session.SessionId,
			session.UserId,
			session.UserEmail,
			session.UserName,
			session.InterviewId,
			session.Category,
			session.Type,
			session.Status,
			session.RecordingURL,
			session.TranscriptURL,
			session.Duration,
			session.DurationSeconds,
		).Scan(&session.ID, &session.CreatedAt, &session.UpdatedAt, &session.StartTime)

		if err != nil {
			log.Printf("Error creating session: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create session"})
			return
		}

		c.JSON(http.StatusCreated, session)
	}
}

func handleGetSession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := "interview_" + c.Param("session_id")
		var session InterviewSession

		query := `SELECT * FROM interview_sessions WHERE session_id = $1`
		err := dbx.GetContext(c.Request.Context(), &session, query, sessionId)

		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch session"})
			return
		}

		c.JSON(http.StatusOK, session)
	}
}

func handleUpdateSession(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionId := "interview_" + c.Param("session_id")
		var updates struct {
			Status          *string    `json:"status"`
			RecordingURL    *string    `json:"recordingUrl"`
			TranscriptURL   *string    `json:"transcriptUrl"`
			Duration        *string    `json:"duration"`
			DurationSeconds *int       `json:"durationSeconds"`
			EndTime         *time.Time `json:"endTime"`
		}

		if err := c.ShouldBindJSON(&updates); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
			return
		}

		// Build dynamic query based on provided fields
		query := `UPDATE interview_sessions SET updated_at = CURRENT_TIMESTAMP`
		values := []interface{}{sessionId}
		paramCount := 1

		if updates.Status != nil {
			paramCount++
			query += fmt.Sprintf(", status = $%d", paramCount)
			values = append(values, *updates.Status)
		}

		if updates.RecordingURL != nil {
			paramCount++
			query += fmt.Sprintf(", recording_url = $%d", paramCount)
			values = append(values, *updates.RecordingURL)
		}

		if updates.TranscriptURL != nil {
			paramCount++
			query += fmt.Sprintf(", transcript_url = $%d", paramCount)
			values = append(values, *updates.TranscriptURL)
		}

		if updates.EndTime != nil {
			paramCount++
			query += fmt.Sprintf(", end_time = $%d", paramCount)
			values = append(values, *updates.EndTime)
		}

		if updates.Duration != nil {
			paramCount++
			query += fmt.Sprintf(", duration = $%d", paramCount)
			values = append(values, *updates.Duration)
		}

		if updates.DurationSeconds != nil {
			paramCount++
			query += fmt.Sprintf(", duration_seconds = $%d", paramCount)
			values = append(values, *updates.DurationSeconds)
		}

		query += " WHERE session_id = $1 RETURNING *"

		var session InterviewSession
		err := dbx.GetContext(c.Request.Context(), &session, query, values...)

		if err != nil {
			if err == sql.ErrNoRows {
				c.JSON(http.StatusNotFound, gin.H{"error": "Session not found"})
				return
			}
			log.Printf("Error updating session: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update session"})
			return
		}

		c.JSON(http.StatusOK, session)
	}
}

func handleListUserSessions(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := c.Param("user_id")
		var sessions []InterviewSession

		query := `
			SELECT * FROM interview_sessions 
			WHERE user_id = $1 
			ORDER BY created_at DESC`

		err := dbx.SelectContext(c.Request.Context(), &sessions, query, userId)
		if err != nil {
			log.Printf("Error fetching user sessions: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sessions"})
			return
		}

		c.JSON(http.StatusOK, sessions)
	}
}
