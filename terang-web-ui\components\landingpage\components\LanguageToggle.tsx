'use client';

import React from "react";
import styled from "styled-components";
import { useLanguage } from "@/app/language-wrapper";

const ToggleContainer = styled.div`
  display: flex;
  align-items: center;
  margin-left: 1rem;
  position: relative;
`;

const ToggleButton = styled.button`
  background: transparent;
  border: 1px solid ${(props) => props.theme.text};
  border-radius: 20px;
  padding: 0.4rem 0.8rem;
  font-size: ${(props) => props.theme.fontsm};
  color: ${(props) => props.theme.text};
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${(props) => `rgba(${props.theme.textRgba}, 0.1)`};
  }
  
  @media (max-width: 64em) {
    font-size: ${(props) => props.theme.fontxs};
    padding: 0.3rem 0.6rem;
  }
`;

// Fix: Use $active instead of active to prevent it from being passed to DOM
const LanguageCode = styled.span.withConfig({
  shouldForwardProp: (prop) => prop !== '$active',
})<{ $active: boolean }>`
  padding: 0 0.25rem;
  opacity: ${(props) => (props.$active ? "1" : "0.5")};
  font-weight: ${(props) => (props.$active ? "600" : "400")};
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.8;
  }
`;

const Divider = styled.span`
  margin: 0 0.25rem;
  color: ${(props) => props.theme.text};
  opacity: 0.5;
`;

const FlagIcon = styled.span`
  font-size: 0.9em;
  line-height: 1;
`;

const LanguageToggle = () => {
  const { language, setLanguage } = useLanguage();
  
  return (
    <ToggleContainer>
      <ToggleButton>
        <LanguageCode 
          $active={language === "id"} 
          onClick={() => setLanguage("id")}
        >
          <FlagIcon>🇮🇩</FlagIcon>
          ID
        </LanguageCode>
        <Divider>|</Divider>
        <LanguageCode 
          $active={language === "en"} 
          onClick={() => setLanguage("en")}
        >
          <FlagIcon>🇺🇸</FlagIcon>
          EN
        </LanguageCode>
      </ToggleButton>
    </ToggleContainer>
  );
};

export default LanguageToggle;