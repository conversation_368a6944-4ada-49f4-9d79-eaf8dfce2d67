-- Create simplified promo_email_subscribers table
CREATE TABLE IF NOT EXISTS promo_email_subscribers (
    id character varying(26) NOT NULL,
    email character varying(255) NOT NULL,
    partner_id character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- Create index on email and partner_id
CREATE INDEX idx_promo_email_subscribers_email ON promo_email_subscribers USING btree (email);
CREATE INDEX idx_promo_email_subscribers_partner ON promo_email_subscribers USING btree (partner_id);

-- Create a unique constraint to prevent duplicate subscriptions
CREATE UNIQUE INDEX unique_email_partner ON promo_email_subscribers (email, partner_id);