package repository

import (
	"errors"
	"fmt"
	"log"
	"math"

	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"

	lib "github.com/terang-ai/backend-service/lib"
	model "github.com/terang-ai/backend-service/model/media"
)

type MediaRepository struct {
	Db *sqlx.DB
}

// MediaPaginationInfo represents pagination metadata.
type MediaPaginationInfo struct {
	TotalData   int `json:"total_data"`
	TotalPages  int `json:"total_pages"`
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
}

func NewMediaRepository(db *sqlx.DB) MediaRepositoryInterface {
	return &MediaRepository{Db: db}
}

func (m *MediaRepository) InsertMedia(url string, availableExamId string, post model.PostMedia) (*model.Media, error) {
	// Prepare the SQL statement for insertion
	log.Println(url, availableExamId)

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedUrl := p.Sanitize(url)
	sanitizedAVailableExamId := p.Sanitize(availableExamId)

	query := `
        INSERT INTO media(id, url, available_exam_id)
        VALUES ($1, $2, $3)
        RETURNING *
    `
	if m.Db == nil {
		log.Println("Database connection is nil")
		return nil, errors.New("database connection is nil")
	}
	// Generate ULID for ID
	ulidId := lib.GenerateULID()

	// Create a Media object to hold the returned data
	var media model.Media

	// Execute the statement with named parameters
	err := m.Db.QueryRowx(query, ulidId, sanitizedUrl, sanitizedAVailableExamId).StructScan(&media)
	if err != nil {
		log.Println("Error executing statement:", err)
		return nil, err
	}

	return &media, nil
}

func (m *MediaRepository) DeleteMedia(id string) (bool, error) {
	// Prepare statement to check if the entity exists
	existsQuery := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE id = $1)", "media")
	existsStmt, err := m.Db.Preparex(existsQuery)
	if err != nil {
		log.Printf("Error preparing %s existence check statement: %s\n", "media", err)
		return false, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.Get(&exists, id)
	if err != nil {
		log.Printf("Error checking if %s exists: %s\n", "media", err)
		return false, err
	}

	if !exists {
		return false, fmt.Errorf("%s with ID %s does not exist", "media", id)
	}

	// Prepare statement to delete the entity
	deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE id = $1", "media")
	deleteStmt, err := m.Db.Preparex(deleteQuery)
	if err != nil {
		log.Printf("Error preparing %s deletion statement: %s\n", "media", err)
		return false, err
	}
	defer deleteStmt.Close()

	_, err = deleteStmt.Exec(id)
	if err != nil {
		log.Printf("Error deleting %s: %s\n", "media", err)
		return false, err
	}

	return true, nil
}

func (m *MediaRepository) GetAllMedia(page int, pageSize int) ([]model.Media, MediaPaginationInfo, error) {
	if page < 1 || pageSize < 1 {
		return nil, MediaPaginationInfo{}, errors.New("invalid pagination parameters")
	}

	offset := (page - 1) * pageSize
	query := "SELECT * FROM media ORDER BY id LIMIT $1 OFFSET $2"

	var dataPool []model.Media
	err := m.Db.Select(&dataPool, query, pageSize, offset)
	if err != nil {
		log.Println("Error querying data:", err)
		return nil, MediaPaginationInfo{}, err
	}

	// Count total number of media
	totalDataQuery := "SELECT COUNT(*) FROM media"
	var totalData int
	err = m.Db.Get(&totalData, totalDataQuery)
	if err != nil {
		log.Println("Error retrieving total media count:", err)
		return nil, MediaPaginationInfo{}, err
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
	paginationInfo := MediaPaginationInfo{
		TotalData:   totalData,
		TotalPages:  totalPages,
		CurrentPage: page,
		PageSize:    pageSize,
	}

	return dataPool, paginationInfo, nil
}

func (m *MediaRepository) GetOneMedia(id string) (model.Media, error) {
	query := "SELECT * FROM media WHERE id = $1"

	var media model.Media
	err := m.Db.Get(&media, query, id)
	if err != nil {
		log.Println("Error fetching data:", err)
		return model.Media{}, err
	}

	return media, nil
}

func (m *MediaRepository) UpdateMedia(id string, post model.UpdateMedia) (*model.Media, error) {
	// Prepare statement to check if the entity exists
	existsQuery := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE id = $1)", "media")
	existsStmt, err := m.Db.Preparex(existsQuery)
	if err != nil {
		log.Printf("Error preparing %s existence check statement: %s\n", "media", err)
		return nil, err
	}
	defer existsStmt.Close()

	var exists bool
	err = existsStmt.Get(&exists, id)
	if err != nil {
		log.Printf("Error checking if %s exists: %s\n", "media", err)
		return nil, err
	}

	if !exists {
		return nil, fmt.Errorf("%s with ID %s does not exist", "media", id)
	}

	// Sanitize inputs
	p := bluemonday.UGCPolicy()
	sanitizedUrl := lib.SanitizeOptionalString(post.Url, p)
	sanitizedMimeType := lib.SanitizeOptionalString(post.MimeType, p)
	sanitizedSizeInBytes := post.SizeInBytes
	sanitizedWidth := post.Width
	sanitizedHeight := post.Height
	sanitizedModifiedAt := post.ModifiedAt

	// Prepare update query
	updateQuery := `
		UPDATE media 
		SET url = COALESCE($2, url),
			size_in_bytes = COALESCE($3, size_in_bytes),
			mime_type = COALESCE($4, mime_type),
			width = COALESCE($5, width),
			height = COALESCE($6, height),
			modified_at = COALESCE($7, modified_at)
		WHERE id = $1
		RETURNING *
	`

	// Create a Media object to hold the returned data
	var updatedMedia model.Media

	// Execute the update statement and scan the result into the Media model
	err = m.Db.QueryRowx(updateQuery, id, sanitizedUrl, sanitizedSizeInBytes, sanitizedMimeType, sanitizedWidth, sanitizedHeight, sanitizedModifiedAt).StructScan(&updatedMedia)
	if err != nil {
		log.Printf("Error updating %s: %s\n", "availableExam", err)
		return nil, err
	}

	return &updatedMedia, nil
}
