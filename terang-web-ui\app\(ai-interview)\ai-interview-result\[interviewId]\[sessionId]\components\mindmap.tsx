"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef, JSX } from "react";
import {
  ReactFlow,
  Node,
  Edge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  Handle,
  Position,
  NodeProps,
  EdgeProps,
  getBezierPath,
  MarkerType,
  useReactFlow,
  ReactFlowProvider,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { 
  Brain, 
  Target, 
  BookOpen, 
  Clock, 
  TrendingUp, 
  Search, 
  ExternalLink, 
  Youtube,
  RotateCw,
  Lightbulb,
  CheckCircle,
  ArrowRight,
  MapPin,
  Calendar,
  Award,
  Play,
  Pause,
  Loader,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize2,
  X
} from "lucide-react";
import * as d3 from 'd3';
import ReactMarkdown from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkGfm from 'remark-gfm';
import { Components } from "react-markdown";

// Types
interface CategoryScore {
  category: string;
  score: number;
  maxScore: number;
  feedback: string;
  strengths: string[];
  improvements: string[];
}

interface NodeData {
  title: string;
  description: string;
  type: 'weakness' | 'skill' | 'resource' | 'milestone';
  category: string;
  priority: 'High' | 'Medium' | 'Low';
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
  dependencies: string[];
  resources: {
    youtube: any[];
    articles: any[];
  };
  geminiGenerated?: boolean;
  depth?: number;
  onGeneratePath: (id: string) => void;
  isLoading?: boolean;
}

interface MindMapProps {
  categoryScores: CategoryScore[];
  improvementAreas: string[];
  transcript: any;
}

// Create markdown components similar to your Questions component
const createMarkdownComponents = (): Components => ({
  // Paragraphs with proper spacing
  p: ({ children }) => (
    <p className="whitespace-pre-wrap break-words mb-3 last:mb-0 text-gray-700 leading-relaxed">
      {children}
    </p>
  ),
  
  // Bullet lists with proper styling
  ul: ({ children }) => (
    <ul className="mb-4 ml-4 space-y-2 list-disc last:mb-0">
      {children}
    </ul>
  ),
  
  // List items
  li: ({ children }) => (
    <li className="ml-2 text-gray-700">
      {children}
    </li>
  ),
  
  // Bold text
  strong: ({ children }) => (
    <strong className="font-semibold text-gray-900">{children}</strong>
  ),
  
  // Italic text
  em: ({ children }) => (
    <em className="italic text-gray-700">{children}</em>
  ),
  
  // Section headers (for lines ending with colon)
  h4: ({ children }) => (
    <h4 className="font-semibold text-gray-900 mt-4 mb-2">
      {children}
    </h4>
  ),
  
  // Code/inline code for examples
  code: ({ children, className }) => (
    <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm">
      {children}
    </code>
  ),
  
  // Blockquotes for examples
  blockquote: ({ children }) => (
    <blockquote className="border-l-4 border-blue-200 pl-4 my-3 italic bg-blue-50 py-2 rounded-r">
      {children}
    </blockquote>
  )
});

// Updated formatDescription function using ReactMarkdown
const formatDescriptionWithMarkdown = (description: string): JSX.Element => {
  // Pre-process the description to convert \n to actual newlines
  // and enhance formatting for better markdown parsing
  const processedDescription = description
    .replace(/\\n\\n/g, '\n\n')  // Double newlines for paragraphs
    .replace(/\\n/g, '\n')       // Single newlines
    .replace(/^(Poin-poin utama:)/gm, '**$1**')  // Make section headers bold
    .replace(/^(Contoh perbaikan[^:]*:)/gm, '**$1**')  // Make example headers bold
    .replace(/^•\s*/gm, '- ')    // Convert • bullets to markdown bullets
    .trim();

  return (
    <div className="prose prose-sm max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkBreaks, remarkGfm]}
        components={createMarkdownComponents()}
      >
        {processedDescription}
      </ReactMarkdown>
    </div>
  );
};

// Alternative: Enhanced version with even better formatting
const formatDescriptionAdvanced = (description: string): JSX.Element => {
  // More sophisticated preprocessing
  const processedDescription = description
    .replace(/\\n\\n/g, '\n\n')
    .replace(/\\n/g, '\n')
    // Convert section patterns to proper markdown
    .replace(/^(Poin-poin utama:)$/gm, '\n#### $1\n')
    .replace(/^(Contoh perbaikan dari transkrip:)(.*)$/gm, '\n> **Contoh perbaikan:**$2')
    // Convert bullet points
    .replace(/^•\s*/gm, '- ')
    // Enhance quotes in examples
    .replace(/(['"])(.*?)\1/g, '`$2`')  // Convert quotes to code blocks
    .trim();

  return (
    <div className="prose prose-sm max-w-none [&_ul]:space-y-1 [&_li]:leading-relaxed">
      <ReactMarkdown
        remarkPlugins={[remarkBreaks, remarkGfm]}
        components={createMarkdownComponents()}
      >
        {processedDescription}
      </ReactMarkdown>
    </div>
  );
};

// Modal Component
const NodeDetailsModal: React.FC<{
  node: Node<NodeData> | null;
  isOpen: boolean;
  onClose: () => void;
  onGeneratePath: (nodeId: string) => void;
  isGenerating: boolean;
  processedNodes: Set<string>;
  maxDepth: number;
}> = ({ node, isOpen, onClose, onGeneratePath, isGenerating, processedNodes, maxDepth }) => {
  if (!isOpen || !node) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[85vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-semibold text-gray-900">{node.data.title}</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Formatted Description using Markdown */}
          <div className="mb-6">
            {formatDescriptionAdvanced(node.data.description)}
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-6">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              node.data.priority === 'High' ? 'bg-red-100 text-red-800' :
              node.data.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {node.data.priority} Priority
            </span>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              {node.data.difficulty}
            </span>
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
              {node.data.estimatedTime}
            </span>
          </div>

          {/* Details */}
          <div className="space-y-3 mb-6">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <MapPin size={16} />
              <span>Category: {node.data.category}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Target size={16} />
              <span>Type: {node.data.type.charAt(0).toUpperCase() + node.data.type.slice(1)}</span>
            </div>
            {node.data.depth !== undefined && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Award size={16} />
                <span>Level: {node.data.depth}</span>
              </div>
            )}
          </div>

          {/* AI Generation Status */}
          {node.data.geminiGenerated !== undefined && (
            <div className={`flex items-center gap-2 text-sm mb-6 ${
              node.data.geminiGenerated ? 'text-green-600' : 'text-orange-600'
            }`}>
              <Brain size={16} />
              <span>{node.data.geminiGenerated ? 'AI-Generated Path' : 'Fallback Content'}</span>
            </div>
          )}

          {/* Generate Sub-paths Button */}
          {!processedNodes.has(node.id) && node.data.depth !== undefined && node.data.depth < maxDepth && (
            <button
              onClick={() => onGeneratePath(node.id)}
              disabled={isGenerating}
              className="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isGenerating ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader className="animate-spin" size={16} />
                  Generating...
                </div>
              ) : (
                'Generate Sub-paths'
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Custom collision force for rectangular nodes
const customCollisionForce = () => {
  let nodes: any[] = [];
  
  const force = (alpha: number) => {
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const nodeA = nodes[i];
        const nodeB = nodes[j];
        
        const widthA = nodeA.data?.type === 'milestone' ? 120 : 80;
        const heightA = nodeA.data?.type === 'milestone' ? 120 : 80;
        const widthB = nodeB.data?.type === 'milestone' ? 120 : 80;
        const heightB = nodeB.data?.type === 'milestone' ? 120 : 80;
        
        const dx = nodeB.x - nodeA.x;
        const dy = nodeB.y - nodeA.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        const minDistance = Math.max(widthA, heightA, widthB, heightB) + 100; // 100px buffer
        
        if (distance < minDistance && distance > 0) {
          const strength = alpha * 0.7;
          const moveDistance = (minDistance - distance) / 2;
          const moveX = (dx / distance) * moveDistance * strength;
          const moveY = (dy / distance) * moveDistance * strength;
          
          nodeA.x -= moveX;
          nodeA.y -= moveY;
          nodeB.x += moveX;
          nodeB.y += moveY;
        }
      }
    }
  };
  
  force.initialize = (newNodes: any[]) => {
    nodes = newNodes;
  };
  
  return force;
};

// Custom Node Components
const LearningNode: React.FC<NodeProps<NodeData>> = ({ data, id, selected }) => {
  const getNodeColor = () => {
    switch (data.type) {
      case 'weakness': return '#ef4444';
      case 'skill': return '#3b82f6';
      case 'resource': return '#8b5cf6';
      case 'milestone': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getPriorityColor = () => {
    switch (data.priority) {
      case 'High': return '#dc2626';
      case 'Medium': return '#d97706';
      case 'Low': return '#059669';
      default: return '#6b7280';
    }
  };

  const getIcon = () => {
    switch (data.type) {
      case 'weakness': return <Target size={50} />;
      case 'skill': return <Lightbulb size={50} />;
      case 'resource': return <BookOpen size={50} />;
      case 'milestone': return <Award size={50} />;
      default: return <Brain size={20} />;
    }
  };

  const nodeSize = data.type === 'milestone' ? 'w-32 h-32' : 'w-24 h-24';
  const textSize = data.type === 'milestone' ? 'text-sm' : 'text-xs';

  return (
    <div className={`relative ${nodeSize} group`}>
      {/* Handles */}
      <Handle type="target" position={Position.Top} className="w-2 h-2" />
      <Handle type="source" position={Position.Bottom} className="w-2 h-2" />
      
      {/* Main Node */}
      <div 
        className={`w-full h-full rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-200 cursor-pointer ${
          selected ? 'ring-4 ring-yellow-400' : ''
        } hover:scale-105`}
        style={{ backgroundColor: getNodeColor() }}
      >
        {/* Loading Indicator */}
        {data.isLoading && (
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
            <Loader className="animate-spin text-purple-600" size={16} />
          </div>
        )}
        
        {/* Priority Indicator */}
        <div 
          className="absolute -top-2 -left-2 w-4 h-4 rounded-full shadow-md"
          style={{ backgroundColor: getPriorityColor() }}
        />
        
        {/* Node Icon */}
        {getIcon()}
        
        {/* AI/Fallback Indicator */}
        {data.geminiGenerated !== undefined && (
          <div 
            className={`absolute -bottom-2 -right-2 w-3 h-3 rounded-full ${
              data.geminiGenerated ? 'bg-green-500' : 'bg-orange-500'
            }`}
          />
        )}
      </div>
      
      {/* Node Label */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 text-center">
        <div className="bg-white px-2 py-1 rounded shadow-md border min-w-32 max-w-48">
          <div className={`font-semibold text-gray-800 ${textSize} leading-tight break-words`}>
            {data.title}
          </div>
          {data.depth !== undefined && data.depth > 0 && (
            <div className="text-xs text-gray-500">L{data.depth}</div>
          )}
        </div>
      </div>
    </div>
  );
};

// Custom Edge Component
const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
}) => {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const getEdgeStyle = () => {
    switch (data?.type) {
      case 'prerequisite':
        return { stroke: '#dc2626', strokeDasharray: '5,5' };
      case 'related':
        return { stroke: '#059669', strokeDasharray: '3,3' };
      case 'leads-to':
        return { stroke: '#3b82f6' };
      default:
        return { stroke: '#6b7280' };
    }
  };

  return (
    <path
      id={id}
      style={{ ...style, ...getEdgeStyle(), strokeWidth: 2 }}
      className="react-flow__edge-path"
      d={edgePath}
      markerEnd={MarkerType.ArrowClosed}
    />
  );
};

// Node and Edge Types
const nodeTypes = {
  learningNode: LearningNode,
};

const edgeTypes = {
  custom: CustomEdge,
};

// D3 Tree Layout Hook
const useD3TreeLayout = () => {
  const { getNodes, setNodes, getEdges, fitView } = useReactFlow();

  const applyTreeLayout = useCallback(() => {
    const nodes = getNodes();
    const edges = getEdges();
    
    if (nodes.length === 0) return;

    // Create hierarchy from nodes and edges
    const createHierarchy = () => {
      // Find root node (center node)
      const rootNode = nodes.find(node => node.id === 'center');
      if (!rootNode) return null;

      // Build tree structure recursively
      const buildTree = (nodeId: string, visited = new Set()): any => {
        if (visited.has(nodeId)) return null;
        visited.add(nodeId);

        const node = nodes.find(n => n.id === nodeId);
        if (!node) return null;

        // Find children
        const childEdges = edges.filter(edge => edge.source === nodeId);
        const children = childEdges
          .map(edge => buildTree(edge.target, visited))
          .filter(child => child !== null);

        return {
          ...node,
          children: children.length > 0 ? children : undefined
        };
      };

      return buildTree('center');
    };

    const hierarchyData = createHierarchy();
    if (!hierarchyData) return;

    // Create D3 tree layout with more spacing
    const treeLayout = d3.tree<any>()
      .size([1600, 800]) // Increased from [1200, 600] for more space
      .separation((a, b) => {
        if (a.parent === b.parent) {
          return 1.5; // Increased from 0.8 - more space between siblings
        } else {
          return 2.0; // Increased from 1.2 - more space between different branches
        }
      });

    // Apply layout
    const root = d3.hierarchy(hierarchyData);
    const treeData = treeLayout(root);

    // Update node positions
    const updatedNodes = nodes.map(node => {
      const treeNode = treeData.descendants().find(d => d.data.id === node.id);
      if (treeNode) {
        return {
          ...node,
          position: {
            x: treeNode.x - 800, // Adjusted for larger tree size
            y: treeNode.y - 100   // Increased vertical offset for better spacing
          }
        };
      }
      return node;
    });

    setNodes(updatedNodes);
    
    // Fit view after layout
    setTimeout(() => {
      fitView({ duration: 800, padding: 0.1 });
    }, 100);
  }, [getNodes, getEdges, setNodes, fitView]);

  return { applyTreeLayout };
};

// Main Component
const ReactFlowLearningMindMapInner: React.FC<MindMapProps> = ({ 
  categoryScores, 
  improvementAreas,
  transcript
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node<NodeData> | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [geminiLoading, setGeminiLoading] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);
  const [generationQueue, setGenerationQueue] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [processedNodes, setProcessedNodes] = useState<Set<string>>(new Set());

  const { applyTreeLayout } = useD3TreeLayout();
  const { fitView } = useReactFlow();

  // Add radial and cluster layouts
  const applyRadialLayout = useCallback(() => {
    if (nodes.length === 0) return;

    // Simple radial layout - arrange nodes in concentric circles
    const centerNode = nodes.find((n: Node) => n.id === 'center');
    if (!centerNode) return;

    const nodesByDepth: { [key: number]: Node[] } = {};
    nodes.forEach((node: Node) => {
      const depth = node.data.depth || 0;
      if (!nodesByDepth[depth]) nodesByDepth[depth] = [];
      nodesByDepth[depth].push(node);
    });

    const updatedNodes = nodes.map((node: Node) => {
      if (node.id === 'center') {
        return { ...node, position: { x: 0, y: 0 } };
      }

      const depth = node.data.depth || 1;
      const nodesAtDepth = nodesByDepth[depth];
      const nodeIndex = nodesAtDepth.indexOf(node);
      const totalAtDepth = nodesAtDepth.length;
      
      const radius = depth * 300; // Increased spacing
      const angle = (nodeIndex / totalAtDepth) * 2 * Math.PI;
      
      return {
        ...node,
        position: {
          x: Math.cos(angle) * radius,
          y: Math.sin(angle) * radius
        }
      };
    });

    setNodes(updatedNodes);
    setTimeout(() => fitView({ duration: 800, padding: 0.1 }), 100);
  }, [nodes, setNodes, fitView]);

  const applyForceLayout = useCallback(() => {
    if (nodes.length === 0) return;

    // Prepare nodes for D3 force simulation
    const d3Nodes = nodes.map((node: Node) => ({
      ...node,
      x: node.position.x,
      y: node.position.y,
    }));

    const d3Links = edges.map((edge: Edge) => ({
      source: edge.source,
      target: edge.target,
    }));

    // Create force simulation with increased spacing
    const simulation = d3.forceSimulation(d3Nodes)
      .force('link', d3.forceLink(d3Links)
        .id((d: any) => d.id)
        .distance(250) // Increased from 200
        .strength(0.3)
      )
      .force('charge', d3.forceManyBody()
        .strength(-1200) // Increased repulsion for more spacing
        .distanceMax(600)
      )
      .force('center', d3.forceCenter(0, 0))
      .force('x', d3.forceX(0).strength(0.05))
      .force('y', d3.forceY(0).strength(0.05))
      .alphaDecay(0.02)
      .velocityDecay(0.3);

    // Update positions
    simulation.on('tick', () => {
      const updatedNodes = nodes.map((node: Node) => {
        const d3Node = d3Nodes.find((d3n: any) => d3n.id === node.id);
        if (d3Node) {
          return {
            ...node,
            position: { x: d3Node.x || 0, y: d3Node.y || 0 },
          };
        }
        return node;
      });
      setNodes(updatedNodes);
    });

    simulation.on('end', () => {
      fitView({ duration: 800 });
    });
  }, [nodes, edges, setNodes, fitView]);

  // Constants
  const MAX_GENERATION_DEPTH = 4;

  // Initialize mindmap
  useEffect(() => {
    generateInitialMindMap();
  }, [categoryScores, improvementAreas]);

  // Auto-apply tree layout when nodes change
  useEffect(() => {
    if (nodes.length > 0) {
      const timer = setTimeout(() => {
        applyTreeLayout();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [nodes.length, applyTreeLayout]);

  // Auto-generate paths
  useEffect(() => {
    if (generationQueue.length > 0 && !isGenerating) {
      processGenerationQueue();
    }
  }, [generationQueue, isGenerating]);

  const generateInitialMindMap = () => {
    const initialNodes: Node[] = [];
    const initialEdges: Edge[] = [];

    // Center node
    const centerNode: Node = {
      id: 'center',
      type: 'learningNode',
      position: { x: 0, y: 0 },
      data: {
        title: 'LPDP Interview Mastery',
        description: 'Your comprehensive learning journey',
        type: 'milestone',
        category: 'Core',
        priority: 'High',
        difficulty: 'Advanced',
        estimatedTime: '4-6 weeks',
        dependencies: [],
        resources: { youtube: [], articles: [] },
        depth: 0,
        onGeneratePath: generateLearningPath,
      } as NodeData,
    };
    initialNodes.push(centerNode);

    // Weakness nodes
    const validCategories = categoryScores.filter(category => 
      (category.score / category.maxScore) * 100 < 75
    );

    const nodesToGenerate: string[] = [];

    validCategories.forEach((category, index) => {
      const x = (Math.random() - 0.5) * 200;
      const y = (Math.random() - 0.5) * 200;

      const weaknessNode: Node = {
        id: `weakness-${index}`,
        type: 'learningNode',
        position: { x, y },
        data: {
          title: category.category,
          description: `Current: ${category.score.toFixed(1)}/${category.maxScore} - Needs improvement`,
          type: 'weakness',
          category: category.category,
          priority: category.score < category.maxScore * 0.5 ? 'High' : 'Medium',
          difficulty: category.score < category.maxScore * 0.5 ? 'Advanced' : 'Intermediate',
          estimatedTime: category.score < category.maxScore * 0.5 ? '2-3 weeks' : '1-2 weeks',
          dependencies: ['center'],
          resources: { youtube: [], articles: [] },
          depth: 1,
          onGeneratePath: generateLearningPath,
        } as NodeData,
      };

      initialNodes.push(weaknessNode);
      initialEdges.push({
        id: `center-to-${weaknessNode.id}`,
        source: 'center',
        target: weaknessNode.id,
        type: 'custom',
        data: { type: 'leads-to' },
      });

      nodesToGenerate.push(weaknessNode.id);
    });

    setNodes(initialNodes);
    setEdges(initialEdges);
    setGenerationQueue(nodesToGenerate);
    setProcessedNodes(new Set());
  };

  const processGenerationQueue = async () => {
    if (isGenerating || generationQueue.length === 0) return;
    
    setIsGenerating(true);
    const currentNodeId = generationQueue[0];
    
    try {
      await generateLearningPath(currentNodeId, false);
      setGenerationQueue(prev => prev.slice(1));
      setProcessedNodes(prev => new Set([...prev, currentNodeId]));
    } catch (error) {
      console.error('Error in auto-generation:', error);
      setGenerationQueue(prev => prev.slice(1));
    }
    
    setIsGenerating(false);
  };

  const generateLearningPath = async (nodeId: string, isManual: boolean = true) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node || geminiLoading.has(nodeId) || processedNodes.has(nodeId)) return;
    
    if (node.data.depth && node.data.depth >= MAX_GENERATION_DEPTH) return;
    
    const hasChildren = edges.some(edge => edge.source === nodeId);
    if (hasChildren && !isManual) return;
    
    setGeminiLoading(prev => new Set([...prev, nodeId]));
    setError(null);

    // Update node to show loading state
    setNodes(prev => prev.map(n => 
      n.id === nodeId 
        ? { ...n, data: { ...n.data, isLoading: true } }
        : n
    ));
    
    try {
      // Create contextual prompt
      const contextualPrompt = createContextualPrompt(node.data, categoryScores, improvementAreas, transcript);
      
      let learningSteps;
      let isAIGenerated = false;
      
      // Try API first, fallback if needed
      try {
        const response = await fetch('/api/gemini-learning-path', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ prompt: contextualPrompt }),
        });

        if (response.ok) {
          const geminiResponse = await response.json();
          
          if (!geminiResponse.error && geminiResponse.data && geminiResponse.data.steps) {
            learningSteps = geminiResponse.data.steps;
            isAIGenerated = true;
          } else {
            throw new Error('Invalid response format');
          }
        } else {
          throw new Error(`API error: ${response.status}`);
        }
      } catch (apiError) {
        console.warn('API failed, using fallback:', apiError);
        learningSteps = generateContextualFallbackSteps(node.data, categoryScores);
        setError('Using fallback generation due to API error.');
      }
      
      // Create child nodes
      const newNodes: Node[] = [];
      const newEdges: Edge[] = [];
      const childNodesToGenerate: string[] = [];
      
      learningSteps.forEach((step: any, index: number) => {
        const x = (Math.random() - 0.5) * 300;
        const y = (Math.random() - 0.5) * 300;
        
        const stepNode: Node = {
          id: `${node.id}-step-${index}-${Date.now()}`,
          type: 'learningNode',
          position: { x, y },
          data: {
            title: step.title,
            description: step.description,
            type: 'skill',
            category: node.data.category,
            priority: index === 0 ? 'High' : step.difficulty === 'Advanced' ? 'High' : 'Medium',
            difficulty: step.difficulty,
            estimatedTime: step.estimatedTime,
            dependencies: [node.id],
            resources: { 
              youtube: [], 
              articles: step.resources ? step.resources.map((resource: string) => ({ title: resource, url: '#' })) : [] 
            },
            geminiGenerated: isAIGenerated,
            depth: (node.data.depth || 0) + 1,
            onGeneratePath: generateLearningPath,
          } as NodeData,
        };
        
        newNodes.push(stepNode);
        
        newEdges.push({
          id: `${node.id}-to-${stepNode.id}`,
          source: node.id,
          target: stepNode.id,
          type: 'custom',
          data: { type: 'leads-to' },
        });

        if (!isManual && stepNode.data.depth && stepNode.data.depth < MAX_GENERATION_DEPTH) {
          childNodesToGenerate.push(stepNode.id);
        }
      });
      
      // Add new nodes and edges
      setNodes(prev => [...prev, ...newNodes]);
      setEdges(prev => [...prev, ...newEdges]);
      
      // Apply tree layout
      setTimeout(() => {
        applyTreeLayout();
      }, 100);
      
      if (!isManual && childNodesToGenerate.length > 0) {
        setTimeout(() => {
          setGenerationQueue(prev => [...prev, ...childNodesToGenerate]);
        }, 2000);
      }
      
    } catch (error) {
      console.error('Error generating learning path:', error);
      setError('Error generating learning path.');
    } finally {
      setGeminiLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
      
      // Remove loading state
      setNodes(prev => prev.map(n => 
        n.id === nodeId 
          ? { ...n, data: { ...n.data, isLoading: false } }
          : n
      ));
    }
  };

  interface CategoryScore {
    category: string;
    score: number;
    maxScore: number;
    improvements: string[];
    strengths: string[];
  }

  interface TranscriptEntry {
    speaker: string;
    text: string;
    timestamp: string;
  }

  // Function to convert transcript JSON to readable text format
  const formatTranscript = (transcript: TranscriptEntry[]): string => {
    if (!transcript || !Array.isArray(transcript)) {
      return "No transcript available";
    }
    
    return transcript
      .map(entry => `${entry.speaker}: ${entry.text}`)
      .join('\n\n');
  };

  // Alternative format - more structured
  const formatTranscriptStructured = (transcript: TranscriptEntry[]): string => {
    if (!transcript || !Array.isArray(transcript)) {
      return "No transcript available";
    }
    
    let formatted = "=== INTERVIEW TRANSCRIPT ===\n\n";
    
    transcript.forEach((entry, index) => {
      formatted += `[${entry.timestamp}] ${entry.speaker}:\n`;
      formatted += `${entry.text}\n\n`;
    });
    
    return formatted;
  };

  // Extract only applicant responses for focused analysis
  const extractApplicantResponses = (transcript: TranscriptEntry[]): string => {
    if (!transcript || !Array.isArray(transcript)) {
      return "No applicant responses available";
    }
    
    const applicantResponses = transcript
      .filter(entry => entry.speaker === 'Applicant')
      .map((entry, index) => `Response ${index + 1}: ${entry.text}`)
      .join('\n\n');
      
    return `=== APPLICANT RESPONSES ===\n\n${applicantResponses}`;
  };

  // Main function with transcript processing
  const createContextualPrompt = (
    nodeData: NodeData, 
    categoryScores: CategoryScore[], 
    improvementAreas: string[], 
    transcript: any
  ) => {
    const relevantCategory = categoryScores.find(cat => cat.category === nodeData.category);
    const currentScore = relevantCategory ? relevantCategory.score : 0;
    const maxScore = relevantCategory ? relevantCategory.maxScore : 10;
    const improvements = relevantCategory ? relevantCategory.improvements : [];
    const strengths = relevantCategory ? relevantCategory.strengths : [];
    
    // Process transcript based on its format
    let formattedTranscript: string;
    
    if (typeof transcript === 'string') {
      formattedTranscript = transcript;
    } else if (Array.isArray(transcript)) {
      // Choose your preferred format:
      formattedTranscript = formatTranscriptStructured(transcript);
      // OR: formattedTranscript = extractApplicantResponses(transcript);
      // OR: formattedTranscript = formatTranscript(transcript);
    } else {
      formattedTranscript = "No valid transcript provided";
    }
    
    return `
  As an expert LPDP scholarship interview coach, create a detailed learning path for improving "${nodeData.title}" in the context of LPDP interviews.

  Current context:
  - Category: ${nodeData.category}
  - Current skill level: ${nodeData.difficulty}
  - Priority: ${nodeData.priority}
  - Description: ${nodeData.description}
  - Current score: ${currentScore}/${maxScore} (${Math.round((currentScore/maxScore) * 100)}%)
  - Depth level: ${nodeData.depth || 0} (generating level ${(nodeData.depth || 0) + 1})

  User data:
  - Improvement Areas: ${Array.isArray(improvementAreas) ? improvementAreas.join(', ') : improvementAreas}

  Interview Transcript:
  ${formattedTranscript}

  Specific areas needing improvement:
  ${improvements.length > 0 ? improvements.map(imp => `- ${imp}`).join('\n') : '- General skill development needed'}

  Current strengths to build upon:
  ${strengths.length > 0 ? strengths.map(str => `- ${str}`).join('\n') : '- Building from foundation level'}

  Please provide 3-4 specific, actionable learning steps that:
  1. Address the specific weaknesses identified above (improvement areas and interview transcript analysis)
  2. Are tailored for LPDP interview success
  3. Consider Indonesian cultural context and expectations
  4. Build progressively from current skill level
  5. Include practical exercises specific to LPDP interviews
  6. IMPORTANT: Highlight which specific parts of the applicant's responses could be improved and provide concrete examples of better responses.
  7. IMPORTANT: You need to give description language based on transcript language, e.g. EN or ID. The response should be EN or ID and follow the transcript language!

  Return response as valid JSON in this exact format:
  {
    "steps": [
      {
        "title": "Step title",
        "description": "Detailed description",
        "difficulty": "Beginner|Intermediate|Advanced",
        "estimatedTime": "X weeks",
        "resources": ["Resource 1", "Resource 2"]
      }
    ]
  }
    `;
  };

  const generateContextualFallbackSteps = (nodeData: NodeData, categoryScores: CategoryScore[]) => {
    const relevantCategory = categoryScores.find(cat => cat.category === nodeData.category);
    const improvements = relevantCategory ? relevantCategory.improvements : [];
    
    if (improvements.length > 0) {
      return improvements.slice(0, 3).map((improvement, index) => ({
        title: `${improvement} Mastery`,
        description: `Focus on improving ${improvement} specifically for ${nodeData.category} in LPDP context`,
        difficulty: index === 0 ? 'Beginner' : index === 1 ? 'Intermediate' : 'Advanced',
        estimatedTime: `${1 + index} week${index > 0 ? 's' : ''}`
      }));
    }

    return [
      { 
        title: `${nodeData.title} Fundamentals`, 
        description: `Master the core concepts of ${nodeData.title} for LPDP interview success`,
        difficulty: 'Beginner', 
        estimatedTime: '1 week' 
      },
      { 
        title: `${nodeData.title} Application`, 
        description: `Apply ${nodeData.title} skills in Indonesian academic and professional contexts`,
        difficulty: 'Intermediate', 
        estimatedTime: '2 weeks' 
      },
      { 
        title: `${nodeData.title} Leadership`, 
        description: `Demonstrate ${nodeData.title} expertise in LPDP interview scenarios`,
        difficulty: 'Advanced', 
        estimatedTime: '1 week' 
      }
    ];
  };

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedNode(null);
  };

  if (!categoryScores.length && !improvementAreas.length) {
    return (
      <div className="mb-4 bg-white rounded-lg border border-gray-200">
        <div className="p-6 text-center">
          <Brain className="mx-auto mb-3 text-gray-400" size={48} />
          <h3 className="text-lg font-semibold mb-2">Interactive Learning Mind Map</h3>
          <p className="text-gray-600">Great job! Your performance shows minimal areas for improvement.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4 bg-white rounded-lg border border-gray-200">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Brain className="text-purple-600" size={28} />
            <div>
              <h3 className="text-xl font-bold">Interactive Learning Mind Map</h3>
              <p className="text-gray-600 text-sm">
                Click on any node to view details • AI-enhanced with API
                {isGenerating && <span className="text-purple-600"> • Generating paths...</span>}
              </p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              <span className="font-medium">Notice:</span> {error}
            </p>
          </div>
        )}

        {/* Generation Status */}
        {(isGenerating || generationQueue.length > 0) && (
          <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Loader className="animate-spin text-purple-600" size={16} />
              <p className="text-purple-800 text-sm">
                Auto-generating AI learning paths... ({generationQueue.length} remaining)
                {geminiLoading.size > 0 && ` • ${geminiLoading.size} in progress`}
              </p>
            </div>
          </div>
        )}

        {/* React Flow Container */}
        <div className="w-full h-[1200px] bg-gray-50 rounded-lg border border-gray-200">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            fitViewOptions={{ padding: 0.15, minZoom: 0.05, maxZoom: 1.5 }}
            minZoom={0.02}
            maxZoom={3}
            defaultEdgeOptions={{
              type: 'custom',
              animated: true,
            }}
            nodesDraggable={true}
            nodesConnectable={false}
            elementsSelectable={true}
            proOptions={{ hideAttribution: true }}
          >
            <Controls />
            <Background variant={BackgroundVariant.Dots} gap={50} size={3} />
            <Panel position="top-right">
              <div className="flex flex-col gap-2">
                <div className="bg-white rounded-lg shadow-lg p-2 border">
                  <div className="text-xs font-medium text-gray-700 mb-2">Layout Options</div>
                  <div className="flex flex-col gap-1">
                    <button
                      onClick={applyTreeLayout}
                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200 transition-colors"
                    >
                      Tree Layout
                    </button>
                    <button
                      onClick={applyForceLayout}
                      className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200 transition-colors"
                    >
                      Force Layout
                    </button>
                  </div>
                </div>
                <button
                  onClick={() => {
                    if (confirm('Reset and regenerate the mind map?')) {
                      setNodes([]);
                      setEdges([]);
                      setProcessedNodes(new Set());
                      setGenerationQueue([]);
                      setGeminiLoading(new Set());
                      setSelectedNode(null);
                      setIsModalOpen(false);
                      generateInitialMindMap();
                    }
                  }}
                  className="px-3 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors text-sm"
                >
                  Reset Map
                </button>
              </div>
            </Panel>
          </ReactFlow>
        </div>

        {/* Node Details Modal */}
        <NodeDetailsModal
          node={selectedNode}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onGeneratePath={generateLearningPath}
          isGenerating={geminiLoading.has(selectedNode?.id || '')}
          processedNodes={processedNodes}
          maxDepth={MAX_GENERATION_DEPTH}
        />

        {/* Enhanced Legend */}
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Mind Map Guide</h4>
          
          {/* Node Types */}
          <div className="mb-4">
            <h5 className="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">Node Types & Colors</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
              <div className="flex items-center gap-3 p-2 bg-white rounded border">
                <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <Target size={12} className="text-white" />
                </div>
                <div>
                  <div className="font-medium">Weaknesses</div>
                  <div className="text-gray-600">Areas needing improvement based on your assessment</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-2 bg-white rounded border">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <Lightbulb size={12} className="text-white" />
                </div>
                <div>
                  <div className="font-medium">Skills</div>
                  <div className="text-gray-600">Specific learning steps and skill development</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-2 bg-white rounded border">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <BookOpen size={12} className="text-white" />
                </div>
                <div>
                  <div className="font-medium">Resources</div>
                  <div className="text-gray-600">Learning materials and reference content</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-2 bg-white rounded border">
                <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Award size={12} className="text-white" />
                </div>
                <div>
                  <div className="font-medium">Milestones</div>
                  <div className="text-gray-600">Major achievements and learning goals</div>
                </div>
              </div>
            </div>
          </div>

          {/* Indicators */}
          <div className="mb-4">
            <h5 className="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">Visual Indicators</h5>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs">
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <div className="w-3 h-3 bg-red-600 rounded-full"></div>
                <span><strong>Red dot:</strong> High priority</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span><strong>Green dot:</strong> AI-generated content</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-white rounded border">
                <Loader size={12} className="text-purple-600" />
                <span><strong>Spinner:</strong> Generating content</span>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mb-4">
            <h5 className="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">How to Use</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600">
              <div>• <strong>Click nodes</strong> to view detailed information</div>
              <div>• <strong>Drag nodes</strong> to rearrange manually</div>
              <div>• <strong>Use layout buttons</strong> to auto-organize</div>
              <div>• <strong>Zoom & pan</strong> to navigate large maps</div>
            </div>
          </div>

          {/* Statistics */}
          <div className="border-t pt-3">
            <div className="flex flex-wrap gap-4 text-xs text-gray-600">
              <span>✓ <strong>{nodes.length}</strong> learning nodes</span>
              <span>✓ <strong>{edges.length}</strong> connections</span>
              <span>✓ AI-powered path generation</span>
              <span>✓ Interactive tree layouts</span>
              <span>✓ Contextual learning recommendations</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Wrapper component with ReactFlowProvider
const ReactFlowLearningMindMap: React.FC<MindMapProps> = (props) => {
  return (
    <ReactFlowProvider>
      <ReactFlowLearningMindMapInner {...props} />
    </ReactFlowProvider>
  );
};

export default ReactFlowLearningMindMap;