// store/slices/purchasedBundlesSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Define the raw API response types
interface NullableString {
  String: string;
  Valid: boolean;
}

export interface NullableInt {
  Int32: number;
  Valid: boolean;
}

interface NullableTime {
  Time: string;
  Valid: boolean;
}

interface BundleCategory {
  id: string;
  name: string;
}

// API Bundle type
export interface Bundle {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPercentage: NullableInt;
  thumbnailUrl: NullableString;
  bannerUrl: NullableString;
  visibility: string;
  validFrom: NullableTime;
  validUntil: NullableTime;
  createdAt: string;
  modifiedAt: string;
  userId: string;
  category: BundleCategory;
  metadata?: string;
}

// Interface for purchased bundles from API
interface PurchasedBundle {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPercentage: NullableInt;
  thumbnailUrl: NullableString;
  bannerUrl: NullableString;
  visibility: string;
  validFrom: NullableTime;
  validUntil: NullableTime;
  createdAt: string;
  modifiedAt: string;
  userId: string;
  categoryId: string;
  categoryName: string;
  examsTotal?: number;
  examsCompleted?: number;
  examsCompletedRate?: number;
  purchaseDate?: string;
  orderStatus?: string;
  paymentStatus?: string;
  orderId?: string;
  remainingDays?: number | null;
  accessExpiresAt?: string | null;
  metadata?: string;
}

// API response type - updated to match what your API is returning
export interface BundleResponse {
  bundles: Bundle[];  // The array of bundles from your API
  pagination: {
    total_data: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}
  
// Update BundleUI interface to properly handle the discountPercentage type
export interface BundleUI {
  id: string;
  name: string;
  description: string;
  price: number;
  // Use a union type to handle both possible formats
  discountPercentage: number | null | NullableInt;
  thumbnailUrl: string | null;
  bannerUrl: string | null;
  visibility: string;
  validFrom: string | null;
  validUntil: string | null;
  createdAt: string;
  modifiedAt: string;
  categoryId: string;
  categoryName: string;
  // Keep the original category structure for backward compatibility
  category?: {
    id: string;
    name: string;
  };
  // Additional properties for purchased bundles
  examsTotal?: number;
  examsCompleted?: number;
  examsCompletedRate?: number;
  purchaseDate?: string;
  orderStatus?: string;
  paymentStatus?: string;
  orderId?: string;
  remainingDays?: number | null;
  accessExpiresAt?: string | null;
}

// Add bundle details type
export interface BundleDetails {
  bundle: Bundle;
  examCount: number;
}
  
// Add UI version of bundle details
export interface BundleDetailsUI {
  bundle: BundleUI;
  examCount: number;
}
  
// Update the state interface to include bundle details
interface BundlesState {
  items: BundleUI[] | null;
  filteredItems: BundleUI[] | null;
  loading: boolean;
  error: string | null;
  activeCategory: string | null;
  // Add bundle details to the state
  currentBundleDetails: BundleDetailsUI | null;
  bundleDetailsLoading: boolean;
  bundleDetailsError: string | null;
}
  
const initialState: BundlesState = {
  items: null,
  filteredItems: null,
  loading: false,
  error: null,
  activeCategory: null,
  // Initialize new state properties
  currentBundleDetails: null,
  bundleDetailsLoading: false,
  bundleDetailsError: null,
};

// Helper function to transform API bundle data to UI friendly format
const transformBundleData = (bundle: Bundle | PurchasedBundle): BundleUI => {
  // Check if we have a direct categoryId/categoryName (purchased bundle) or a category object (regular bundle)
  const hasCategoryObject = 'category' in bundle && bundle.category !== undefined;
  
  return {
    id: bundle.id,
    name: bundle.name,
    description: bundle.description,
    price: bundle.price,
    discountPercentage: bundle.discountPercentage?.Valid ? bundle.discountPercentage.Int32 : null,
    thumbnailUrl: bundle.thumbnailUrl?.Valid ? bundle.thumbnailUrl.String : null,
    bannerUrl: bundle.bannerUrl?.Valid ? bundle.bannerUrl.String : null,
    visibility: bundle.visibility || 'PRIVATE',
    validFrom: bundle.validFrom?.Valid ? bundle.validFrom.Time : null,
    validUntil: bundle.validUntil?.Valid ? bundle.validUntil.Time : null,
    createdAt: bundle.createdAt,
    modifiedAt: bundle.modifiedAt,
    // Get categoryId/name either from direct properties or from the category object
    categoryId: hasCategoryObject ? bundle.category.id : ('categoryId' in bundle ? bundle.categoryId : ""),
    categoryName: hasCategoryObject ? bundle.category.name : ('categoryName' in bundle ? bundle.categoryName : ""),
    // Provide category object for backward compatibility
    category: hasCategoryObject ? bundle.category : {
      id: 'categoryId' in bundle ? bundle.categoryId : "",
      name: 'categoryName' in bundle ? bundle.categoryName : ""
    },
    // Add additional properties from PurchasedBundle if they exist
    ...(('examsTotal' in bundle) && { examsTotal: bundle.examsTotal }),
    ...(('examsCompleted' in bundle) && { examsCompleted: bundle.examsCompleted }),
    ...(('examsCompletedRate' in bundle) && { examsCompletedRate: bundle.examsCompletedRate }),
    ...(('purchaseDate' in bundle) && { purchaseDate: bundle.purchaseDate }),
    ...(('orderStatus' in bundle) && { orderStatus: bundle.orderStatus }),
    ...(('paymentStatus' in bundle) && { paymentStatus: bundle.paymentStatus }),
    ...(('orderId' in bundle) && { orderId: bundle.orderId }),
    ...(('remainingDays' in bundle) && { remainingDays: bundle.remainingDays }),
    ...(('accessExpiresAt' in bundle) && { accessExpiresAt: bundle.accessExpiresAt })
  };
};

export const fetchPurchasedBundleDetails = createAsyncThunk(
  'purchasedBundles/fetchPurchasedBundleDetails',
  async (bundleId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/exam-bundles-purchased/${bundleId}`);
      
      if (response.status === 401) {
        return rejectWithValue('You need to be logged in to view bundle details');
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to fetch bundle details');
      }

      const data = await response.json();
      console.log('Bundle details response:', data);
      
      // Transform the bundle data to UI friendly format
      const transformedData: BundleDetailsUI = {
        bundle: transformBundleData(data.bundle),
        examCount: data.examCount
      };
      
      return transformedData;
    } catch (error: any) {
      console.error('Error in fetchPurchasedBundleDetails:', error);
      return rejectWithValue(error.message || 'An unknown error occurred');
    }
  }
);
  
// Create async thunk for fetching bundles
export const fetchPurchasedBundles = createAsyncThunk(
  'purchasedBundles/fetchPurchasedBundles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/exam-bundles-purchased');
      
      if (response.status === 401) {
        return rejectWithValue('You need to be logged in to view exam bundles');
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to fetch exam bundles');
      }

      // Get raw response data
      const rawData = await response.json();
      
      console.log('Raw API response:', rawData);
      
      // Extract the bundles array from the correct location in the response
      let bundlesArray = [];
      
      if (rawData.bundles && Array.isArray(rawData.bundles.purchasedBundles)) {
        // The actual structure from the API
        console.log('Structure: bundles.purchasedBundles array found');
        bundlesArray = rawData.bundles.purchasedBundles;
      } else if (Array.isArray(rawData.bundles)) {
        // Fallback for original expected structure
        console.log('Structure: bundles is directly an array');
        bundlesArray = rawData.bundles;
      } else if (typeof rawData.purchasedBundles === 'object' && Array.isArray(rawData.purchasedBundles)) {
        // Alternative structure possibility
        console.log('Structure: purchasedBundles array found at root level');
        bundlesArray = rawData.purchasedBundles;
      } else if (rawData.bundles && typeof rawData.bundles === 'object' && rawData.bundles.bundles && Array.isArray(rawData.bundles.bundles)) {
        // Deeper nested possibility
        console.log('Structure: bundles.bundles array found');
        bundlesArray = rawData.bundles.bundles;
      } else if (typeof rawData.bundles === 'object' && rawData.bundles !== null) {
        // Check if bundles is an object with bundle properties
        if (rawData.bundles.id && rawData.bundles.name) {
          console.log('Structure: single bundle object detected');
          bundlesArray = [rawData.bundles];
        }
      }
      
      // Handle empty response - CHANGE IS HERE
      if (!bundlesArray || bundlesArray.length === 0) {
        console.log('No bundles found in response. Using empty array.', rawData);
        // Return empty array instead of rejecting the promise
        return [];
      }
      
      console.log('Extracted bundles array:', bundlesArray);
      
      // Transform the data to a more UI-friendly format
      const transformedBundles = bundlesArray.map(transformBundleData);
      
      console.log('Transformed bundles:', transformedBundles);
      
      return transformedBundles;
    } catch (error: any) {
      console.error('Error in fetchPurchasedBundles:', error);
      return rejectWithValue(error.message || 'An unknown error occurred');
    }
  }
);

// Create async thunk for purchasing a bundle
export const purchaseBundle = createAsyncThunk(
  'purchasedBundles/purchaseBundle',
  async (bundleId: string, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/purchase-bundle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ bundleId }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        return rejectWithValue(errorText || 'Failed to purchase bundle');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'An unknown error occurred during purchase');
    }
  }
);

// Create the slice
const purchasedBundlesSlice = createSlice({
  name: 'purchasedBundles',
  initialState,
  reducers: {
    clearBundles: (state) => {
      state.items = null;
      state.filteredItems = null;
      state.error = null;
    },
    // Add reducer to clear bundle details
    clearBundleDetails: (state) => {
      state.currentBundleDetails = null;
      state.bundleDetailsError = null;
    },
    setActiveCategory: (state, action: PayloadAction<string | null>) => {
      state.activeCategory = action.payload;
      
      // Filter items by category if a category is selected
      if (state.items && action.payload) {
        // Use case-insensitive match
        const categoryLower = action.payload.toLowerCase();
        state.filteredItems = state.items.filter(bundle => 
          (bundle.categoryName?.toLowerCase() === categoryLower) || 
          (bundle.category?.name?.toLowerCase() === categoryLower)
        );
      } else if (state.items) {
        state.filteredItems = state.items;
      }
    },
    filterBundles: (state, action: PayloadAction<string>) => {
      const searchTerm = action.payload.toLowerCase();
      if (!state.items) return;
      
      if (searchTerm === '') {
        // If there's an active category, filter by that
        if (state.activeCategory) {
          const categoryLower = state.activeCategory.toLowerCase();
          state.filteredItems = state.items.filter(bundle => 
            (bundle.categoryName?.toLowerCase() === categoryLower) ||
            (bundle.category?.name?.toLowerCase() === categoryLower)
          );
        } else {
          state.filteredItems = state.items;
        }
      } else {
        // Filter by search term and possibly category
        let filtered = state.items.filter(bundle => 
          bundle.name.toLowerCase().includes(searchTerm) || 
          bundle.description.toLowerCase().includes(searchTerm)
        );
        
        // Apply category filter if needed
        if (state.activeCategory) {
          const categoryLower = state.activeCategory.toLowerCase();
          filtered = filtered.filter(bundle => 
            (bundle.categoryName?.toLowerCase() === categoryLower) ||
            (bundle.category?.name?.toLowerCase() === categoryLower)
          );
        }
        
        state.filteredItems = filtered;
      }
    },
    // Add reducer for initializing with server data
    initializeWithServerData: (state, action: PayloadAction<BundleUI[]>) => {
      state.items = action.payload;
      state.filteredItems = action.payload;
      state.loading = false;
      state.error = null;
    },
    // Add reducer for initializing bundle details with server data
    initializeBundleDetailsWithServerData: (state, action: PayloadAction<BundleDetailsUI>) => {
      state.currentBundleDetails = action.payload;
      state.bundleDetailsLoading = false;
      state.bundleDetailsError = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchPurchasedBundles pending state
      .addCase(fetchPurchasedBundles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      // Handle fetchPurchasedBundles fulfilled state
      .addCase(fetchPurchasedBundles.fulfilled, (state, action: PayloadAction<BundleUI[]>) => {
        state.items = action.payload;
        state.filteredItems = action.payload;
        state.loading = false;
        state.error = null;
      })
      // Handle fetchPurchasedBundles rejected state
      .addCase(fetchPurchasedBundles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch exam bundles';
      })
      .addCase(fetchPurchasedBundleDetails.pending, (state) => {
        state.bundleDetailsLoading = true;
        state.bundleDetailsError = null;
      })
      .addCase(fetchPurchasedBundleDetails.fulfilled, (state, action: PayloadAction<BundleDetailsUI>) => {
        state.currentBundleDetails = action.payload;
        state.bundleDetailsLoading = false;
        state.bundleDetailsError = null;
      })
      .addCase(fetchPurchasedBundleDetails.rejected, (state, action) => {
        state.bundleDetailsLoading = false;
        state.bundleDetailsError = action.payload as string || 'Failed to fetch bundle details';
        state.currentBundleDetails = null;
      });
  },
});

export const { 
  clearBundles, 
  setActiveCategory, 
  filterBundles, 
  initializeWithServerData,
  // Export new actions
  clearBundleDetails,
  initializeBundleDetailsWithServerData
} = purchasedBundlesSlice.actions;
  
export default purchasedBundlesSlice.reducer;