// components/VideoShowcase.tsx
import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, Pause, Brain, Mic, BarChart3, CheckCircle, ArrowRight, Sparkles, Video } from 'lucide-react';

interface VideoFeature {
  title: string;
  description: string;
  icon: React.ElementType;
  bgColor: string;
  videoSrc: string;
  posterSrc: string;
  pattern: string;
  key: string;
  features: string[];
  buttonText: string;
  gradient: string;
}

interface LazyVideoProps {
  src: string;
  poster: string;
  title: string;
}

const LazyVideo: React.FC<LazyVideoProps> = ({ src, poster, title }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState<boolean>(false);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  useEffect(() => {
    const options: IntersectionObserverInit = {
      root: null,
      rootMargin: '0px',
      threshold: 0.7
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsInView(true);
          if (videoRef.current && videoRef.current.paused) {
            videoRef.current.play()
              .then(() => setIsPlaying(true))
              .catch((error) => console.error('Error autoplaying video:', error));
          }
        } else {
          if (videoRef.current && !videoRef.current.paused) {
            videoRef.current.pause();
            setIsPlaying(false);
          }
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, options);

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  const handleVideoLoaded = (): void => {
    setIsLoaded(true);
    if (isInView && videoRef.current) {
      videoRef.current.play()
        .then(() => setIsPlaying(true))
        .catch((error) => console.error('Error autoplaying video:', error));
    }
  };

  const handlePlayPause = (): void => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play()
          .then(() => setIsPlaying(true))
          .catch((error) => console.error('Error playing video:', error));
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  return (
    <div 
      ref={containerRef} 
      className="aspect-video rounded-3xl overflow-hidden relative bg-gray-100 shadow-2xl border border-white/20 group backdrop-blur-sm"
    >
      {/* Poster Image */}
      <div 
        className={`absolute inset-0 transition-all duration-500 ${
          isLoaded ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
        }`}
      >
        <img 
          src={poster} 
          alt={`${title} thumbnail`}
          className="w-full h-full object-contain bg-gray-100"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-blue-900/20" />
      </div>

      {/* Video */}
      {isInView && (
        <video
          ref={videoRef}
          className={`w-full h-full object-contain transition-all duration-500 ${
            isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}
          playsInline
          muted
          loop
          onLoadedData={handleVideoLoaded}
          onEnded={() => setIsPlaying(false)}
          onPause={() => setIsPlaying(false)}
          onPlay={() => setIsPlaying(true)}
        >
          <source src={src} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      )}

      {/* Play/Pause Overlay */}
      <button 
        className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          isPlaying ? 'opacity-0 group-hover:opacity-100' : 'opacity-100'
        }`}
        onClick={handlePlayPause}
        aria-label={`${isPlaying ? 'Pause' : 'Play'} ${title} video`}
      >
        <div className="bg-white/95 backdrop-blur-md p-6 rounded-full shadow-2xl transform transition-all duration-300 group-hover:scale-110 hover:bg-white border border-white/30">
          {isPlaying ? (
            <Pause className="w-8 h-8 text-gray-900" />
          ) : (
            <Play className="w-8 h-8 text-gray-900 ml-1" />
          )}
        </div>
      </button>

      {/* Gradient overlay for better contrast */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent pointer-events-none" />
    </div>
  );
};

const getFeatures = (): VideoFeature[] => [
  {
    title: "LPDP Interview Simulation",
    description: "Simulasi wawancara LPDP yang komprehensif dengan AI Professor Terra. Dapatkan pengalaman wawancara yang realistis dengan feedback real-time dan analisis mendalam untuk mempersiapkan kamu menghadapi wawancara sesungguhnya.",
    icon: Brain,
    bgColor: "from-purple-50 to-pink-50",
    videoSrc: "https://cdn.terang.ai/videos/1-interview-lpdp.mp4",
    posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-1.webp",
    pattern: "squares",
    key: "lpdp_interview",
    features: [
      "🎯 Personal Background & Motivation",
      "🤖 AI Professor Terra Interactive",
      "📊 Real-time Feedback Analysis",
      "⏱️ 45-60 menit simulasi lengkap"
    ],
    buttonText: "Mulai LPDP Interview Gratis",
    gradient: "from-purple-600 to-pink-600"
  },
  {
    title: "Natural Voice Conversation",
    description: "Berbicara langsung dengan AI seperti pewawancara sesungguhnya. Sistem voice recognition canggih memahami bahasa Indonesia dengan sempurna dan memberikan respons yang natural dan kontekstual.",
    icon: Mic,
    bgColor: "from-emerald-50 to-teal-50",
    videoSrc: "https://cdn.terang.ai/videos/2-interview-lpdp.mp4",
    posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-2.webp",
    pattern: "bokeh",
    key: "voice_conversation",
    features: [
      "🎙️ Voice Recognition Indonesia",
      "🗣️ Natural Conversation Flow",
      "📈 Real-time Audio Analysis",
      "🔄 Interactive Response System"
    ],
    buttonText: "Coba Voice Interview",
    gradient: "from-emerald-600 to-teal-600"
  },
  {
    title: "Smart Performance Insights",
    description: "AI menganalisis jawaban dan memberikan feedback mendalam dengan skor performa yang komprehensif dan rekomendasi pembelajaran yang dipersonalisasi untuk meningkatkan kemampuan wawancaramu.",
    icon: BarChart3,
    bgColor: "from-blue-50 to-indigo-50",
    videoSrc: "https://cdn.terang.ai/videos/3-interview-lpdp.mp4",
    posterSrc: "https://cdn.terang.ai/images/landingpage/lpdp-3.webp",
    pattern: "crosshatch",
    key: "performance_insights",
    features: [
      "📊 Detailed Performance Score",
      "🧠 AI-Generated Learning Path",
      "📈 Improvement Recommendations",
      "📚 Curated Learning Resources"
    ],
    buttonText: "Mulai Sekarang",
    gradient: "from-blue-600 to-indigo-600"
  }
];

const VideoShowcase: React.FC = () => {
  // Use memoization to prevent unnecessary re-renders exactly like your original
  const memoizedFeatures = React.useMemo(() => 
    getFeatures().map(feature => ({
      ...feature,
      pattern: feature.pattern || 'squares' // Fallback to squares if no pattern specified
    })), 
  []); // No dependencies since we're not using translations

  return (
    <section id="interview-features" className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden pb-12">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-20 pt-20">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-full text-purple-800 text-sm font-medium mb-8 border border-purple-200">
            <Video className="w-5 h-5 mr-2" />
            Live Demo Experience
            <Sparkles className="w-5 h-5 ml-2" />
          </div>
          
            <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-8">Lihat Bagaimana <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600">LPDP AI Interview</span> Bekerja</h2>
          
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Saksikan simulasi wawancara LPDP dengan AI Professor Terra dan rasakan pengalaman yang realistis
          </p>
        </div>

        {/* Features Grid */}
        <div className="space-y-32">
          {memoizedFeatures.map((feature, index) => (
            <div 
              key={feature.key} 
              className={`flex flex-col lg:flex-row gap-16 items-center ${
                index % 2 === 0 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Video Side */}
              <div className="w-full lg:w-1/2">
                <LazyVideo
                  src={feature.videoSrc}
                  poster={feature.posterSrc}
                  title={feature.title}
                />
              </div>
              
              {/* Content Side */}
              <div className="w-full lg:w-1/2">
                <div className={`relative p-10 rounded-3xl bg-gradient-to-br ${feature.bgColor} border border-white/20 shadow-2xl backdrop-blur-sm overflow-hidden group hover:scale-105 transition-all duration-500`}>
                  {/* Pattern overlay */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent transform rotate-12 scale-150" />
                  </div>
                  
                  {/* Icon */}
                  <div className="relative z-10 mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-white/80 backdrop-blur-md shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <feature.icon className="w-8 h-8 text-gray-800" />
                    </div>
                  </div>
                  
                  {/* Title */}
                  <h3 className="relative z-10 text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                    {feature.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="relative z-10 text-gray-700 text-lg leading-relaxed mb-8">
                    {feature.description}
                  </p>
                  
                  {/* Feature List */}
                  <div className="relative z-10 space-y-4 mb-10">
                    {feature.features.map((featureItem, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-4">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        </div>
                        <span className="text-gray-800 font-medium">{featureItem}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <div className="relative z-10">
                    <button 
                      className={`group/btn inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r ${feature.gradient} text-white font-bold text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 transform`}
                      onClick={() => {
                        const interviewsSection = document.getElementById('interviews');
                        if (interviewsSection) {
                          interviewsSection.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                    >
                      {feature.buttonText}
                      <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default VideoShowcase;