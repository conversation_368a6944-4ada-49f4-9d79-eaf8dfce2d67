package model

import (
	"encoding/json"
	"time"
)

// TimezoneOffset represents the timezone offset from UTC.
type TimezoneOffset string

// TimezoneOffset constants
const (
	UTC                    TimezoneOffset = "+00:00"
	CET                    TimezoneOffset = "+01:00" // Central European Time
	EET                    TimezoneOffset = "+02:00" // Eastern European Time
	MST                    TimezoneOffset = "+03:00" // Moscow Time
	GST                    TimezoneOffset = "+04:00" // Gulf Standard Time
	PKT                    TimezoneOffset = "+05:00" // Pakistan Standard Time
	BST                    TimezoneOffset = "+06:00" // Bangladesh Standard Time
	AsiaJakarta            TimezoneOffset = "+07:00" // Asia/Jakarta
	CST                    TimezoneOffset = "+08:00" // China Standard Time
	JST                    TimezoneOffset = "+09:00" // Japan Standard Time
	AEST                   TimezoneOffset = "+10:00" // Australian Eastern Standard Time
	SBT                    TimezoneOffset = "+11:00" // Solomon Islands Time
	NZST                   TimezoneOffset = "+12:00" // New Zealand Standard Time
	AzoresTime             TimezoneOffset = "-01:00" // Azores Time
	SouthGeorgiaTime       TimezoneOffset = "-02:00" // South Georgia Time
	ArgentinaTime          TimezoneOffset = "-03:00" // Argentina Time
	AtlanticTime           TimezoneOffset = "-04:00" // Atlantic Time
	EasternStandardTime    TimezoneOffset = "-05:00" // Eastern Standard Time
	CentralStandardTime    TimezoneOffset = "-06:00" // Central Standard Time
	MountainStandardTime   TimezoneOffset = "-07:00" // Mountain Standard Time
	PacificStandardTime    TimezoneOffset = "-08:00" // Pacific Standard Time
	AlaskaStandardTime     TimezoneOffset = "-09:00" // Alaska Standard Time
	HawaiiAleutianStandard TimezoneOffset = "-10:00" // Hawaii-Aleutian Standard Time
	SamoaTime              TimezoneOffset = "-11:00" // Samoa Time
	BakerIslandTime        TimezoneOffset = "-12:00" // Baker Island Time
)

// User represents a user in the system
type User struct {
	Id               string           `json:"id"`
	Username         string           `json:"username"`
	Email            string           `json:"email"`
	Password         string           `json:"-"` // excluded from serialization
	FirstName        string           `json:"first_name"`
	LastName         string           `json:"last_name"`
	Picture          *string          `json:"picture"`
	Banner           *string          `json:"banner"`
	LastLogin        *time.Time       `json:"last_login"`
	Role             string           `json:"role"`
	Tier             string           `json:"tier"`
	PersonalWebsite  *string          `json:"personal_website"`
	PhoneNumber      *string          `json:"phone_number"`
	Address          *string          `json:"address"`
	DateOfBirth      *time.Time       `json:"date_of_birth"`
	Country          *string          `json:"country"`
	Bio              *string          `json:"bio"`
	Preferences      *json.RawMessage `json:"preferences"`
	SocialLinks      *json.RawMessage `json:"social_links"`
	IsVerified       bool             `json:"is_verified"`
	IsBanned         bool             `json:"is_banned"`
	Timezone         *TimezoneOffset  `json:"timezone"`
	ConsentMarketing bool             `json:"consent_marketing"`
	TwoFactorEnabled bool             `json:"two_factor_enabled"`
	TwoFactorSecret  *string          `json:"two_factor_secret"`
	LastModifiedBy   *string          `json:"last_modified_by"`
	CreatedAt        time.Time        `json:"created_at"`
	ModifiedAt       time.Time        `json:"modified_at"`
}

// PostUser is used for creating a new user. Fields can be added as necessary.
type PostUser struct {
	Id        string `json:"id"`
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password"`
	FirstName string `json:"first_name" binding:"required"`
	LastName  string `json:"last_name" binding:"required"`
	Picture   string `json:"picture" binding:"required"`
}

type UpdateUser struct {
	Username         *string          `json:"username,omitempty"`
	Email            *string          `json:"email,omitempty"`
	Password         *string          `json:"password,omitempty"`
	FirstName        *string          `json:"first_name,omitempty"`
	LastName         *string          `json:"last_name,omitempty"`
	Picture          *string          `json:"picture,omitempty"`
	Banner           *string          `json:"banner,omitempty"`
	LastLogin        *time.Time       `json:"last_login,omitempty"`
	Role             *string          `json:"role,omitempty"`
	Tier             *string          `json:"tier,omitempty"`
	PersonalWebsite  *string          `json:"personal_website,omitempty"`
	PhoneNumber      *string          `json:"phone_number,omitempty"`
	Address          *string          `json:"address,omitempty"`
	DateOfBirth      *time.Time       `json:"date_of_birth,omitempty"`
	Country          *string          `json:"country,omitempty"`
	Bio              *string          `json:"bio,omitempty"`
	Preferences      *json.RawMessage `json:"preferences,omitempty"`
	SocialLinks      *json.RawMessage `json:"social_links,omitempty"`
	IsVerified       *bool            `json:"is_verified,omitempty"`
	IsBanned         *bool            `json:"is_banned,omitempty"`
	Timezone         *TimezoneOffset  `json:"timezone,omitempty"`
	ConsentMarketing *bool            `json:"consent_marketing,omitempty"`
	TwoFactorEnabled *bool            `json:"two_factor_enabled,omitempty"`
	TwoFactorSecret  *string          `json:"two_factor_secret,omitempty"`
	LastModifiedBy   *string          `json:"last_modified_by,omitempty"`
}

type UserTokenStatus string

const (
	AVAILABLE UserTokenStatus = "AVAILABLE"
	EXPIRED   UserTokenStatus = "EXPIRED"
)

type UserVerificationRequest struct {
	Id        string          `json:"id"`
	Email     string          `json:"email"`
	Token     string          `json:"token"`
	Status    UserTokenStatus `json:"status"`
	Expires   string          `json:"expires"`
	CreatedAt time.Time       `json:"created_at"`
}

type PostUserVerificationRequest struct {
	Email string  `json:"email" binding:"required"`
	Token *string `json:"token"`
}

type UpdateUserVerificationRequest struct {
	Status *string `json:"status,omitempty"`
}

type UserUri struct {
	ID string `uri:"id" binding:"required"`
}

type UserEmailUri struct {
	EMAIL string `uri:"email" binding:"required"`
}

type UserTokenUri struct {
	TOKEN string `uri:"token" binding:"required"`
}
