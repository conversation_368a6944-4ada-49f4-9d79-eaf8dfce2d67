package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	model "github.com/terang-ai/backend-service/model/exams"
	repository "github.com/terang-ai/backend-service/repository/exams"
)

type ExamsController struct {
	Dbx   *sqlx.DB
	Redis *redis.Client
	Repo  repository.ExamsRepositoryInterface
}

func NewExamsController(dbx *sqlx.DB, redis *redis.Client) *ExamsController {
	return &ExamsController{
		Dbx:   dbx,
		Redis: redis,
		Repo:  repository.NewExamSessionsRepository(dbx, redis),
	}
}

// InsertExamSessions inserts a new exam session
func (c *ExamsController) InsertExamSessions(ctx *gin.Context) {
	var post model.PostExamSessions
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.InsertExamSessions(post)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert exam session failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "exam session inserted successfully"})
}

// GetAllExamSessionsByUserId retrieves all exam sessions for a given user
func (c *ExamsController) GetAllExamSessionsByUserId(ctx *gin.Context) {
	var uri model.ExamUserIdUri
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAllExamSessionsByUserId(uri.USERID, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exam sessions retrieved successfully", "_pagination": paginationInfo})
}

// GetLastExamSessionsByExamIdUserId retrieves all exam sessions for a given user
func (c *ExamsController) GetLastExamSessionsByExamIdUserId(ctx *gin.Context) {
	var uri model.ExamSessionExamUserIdUri
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	entities, err := c.Repo.GetLastExamSessionsByExamIdUserId(uri.EXAMID, uri.USERID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exam sessions retrieved successfully"})
}

// GetLastTrialSessionsByExamIdUserId retrieves all exam sessions for a given user
func (c *ExamsController) GetLastTrialSessionsByExamIdUserId(ctx *gin.Context) {
	var uri model.ExamSessionExamUserIdUri
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	var post model.PostGetExamSessionTrial
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	entities, err := c.Repo.GetLastTrialSessionsByExamIdUserId(uri.EXAMID, uri.USERID, post.Subject)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "exam sessions retrieved successfully"})
}

// UpdateExamSessions updates an existing exam session
func (c *ExamsController) UpdateExamSessions(ctx *gin.Context) {
	var uri model.ExamSessionIdUri
	var post model.UpdateExamSessions

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	updatedExam, err := c.Repo.UpdateExamSessions(uri.SESSIONID, post)
	if err != nil {
		if err.Error() == fmt.Sprintf("exam session with ID %s does not exist", uri.SESSIONID) {
			ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "exam session not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "update exam session failed"})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": updatedExam, "msg": "exam session updated successfully"})
}

// DeleteExamSessions deletes an exam session by ID
func (c *ExamsController) DeleteExamSessions(ctx *gin.Context) {
	var uri model.ExamUri

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	deleted, err := c.Repo.DeleteExamSessions(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete exam session failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "exam session not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "exam session deleted successfully"})
}

// InsertExamScores inserts a new exam score
func (c *ExamsController) InsertExamScores(ctx *gin.Context) {
	var post model.PostExamScores
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.InsertExamScores(post)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert exam score failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "exam score inserted successfully"})
}

// UpdateExamScores updates an existing exam score
func (c *ExamsController) UpdateExamScores(ctx *gin.Context) {
	var uri model.ExamUri
	var post model.UpdateExamScores

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	updatedScore, err := c.Repo.UpdateExamScores(uri.ID, post)
	if err != nil {
		if err.Error() == fmt.Sprintf("exam score with ID %s does not exist", uri.ID) {
			ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "exam score not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "update exam score failed"})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": updatedScore, "msg": "exam score updated successfully"})
}

// DeleteExamScores deletes an exam score by ID
func (c *ExamsController) DeleteExamScores(ctx *gin.Context) {
	var uri model.ExamUri

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	deleted, err := c.Repo.DeleteExamScores(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete exam score failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "exam score not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "exam score deleted successfully"})
}

// GetExamScoresBySessionId retrieves an exam score by session ID
func (c *ExamsController) GetExamScoresBySessionId(ctx *gin.Context) {
	var uri model.ExamSessionIdUri

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	score, err := c.Repo.GetExamScoresBySessionId(uri.SESSIONID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "retrieve exam score failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": score, "msg": "exam score retrieved successfully"})
}

// GetExamSessionBySessionId retrieves an exam score by session ID
func (c *ExamsController) GetExamSessionBySessionId(ctx *gin.Context) {
	var uri model.ExamSessionIdUri

	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	session, err := c.Repo.GetExamSessionBySessionId(uri.SESSIONID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "retrieve exam session failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": session, "msg": "exam session retrieved successfully"})
}
