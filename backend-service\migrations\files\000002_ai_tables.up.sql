-- CHAT AI
CREATE TABLE IF NOT EXISTS chat_ai_history (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    chat_id VARCHAR(26) NOT NULL,
    messages JSONB NOT NULL DEFAULT '[]'::<PERSON><PERSON><PERSON><PERSON>,
    from_page VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, chat_id)
);

CREATE TRIGGER update_chat_ai_history_modified_at
    BEFORE UPDATE ON chat_ai_history
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at();
    
-- Table for current AI token balance
CREATE TABLE IF NOT EXISTS user_ai_tokens (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL UNIQUE,
    token_balance BIGINT NOT NULL DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_ai_tokens_user
        FOREIGN KEY (user_id)
        REFERENCES users(id)
        ON DELETE CASCADE
);

-- Table for AI token usage history
CREATE TABLE IF NOT EXISTS user_ai_token_usage_history (
    id VARCHAR(26) NOT NULL PRIMARY KEY,
    user_id VARCHAR(26) NOT NULL,
    amount BIGINT NOT NULL,
    last_balance BIGINT NOT NULL,
    after_balance BIGINT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_ai_token_usage_history_user
        FOREIGN KEY (user_id)
        REFERENCES users(id)
        ON DELETE CASCADE
);

-- Chat AI related indexes
CREATE INDEX IF NOT EXISTS idx_chat_ai_history_user_chat ON chat_ai_history(user_id, chat_id);
CREATE INDEX IF NOT EXISTS idx_ai_token_usage_history_user_id ON user_ai_token_usage_history(user_id);