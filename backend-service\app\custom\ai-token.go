package custom

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/terang-ai/backend-service/lib"
)

// UserAITokens represents the user_ai_tokens table structure
type UserAITokens struct {
	ID           string    `db:"id" json:"id"`
	UserID       string    `db:"user_id" json:"userId"`
	TokenBalance int64     `db:"token_balance" json:"tokenBalance"`
	Metadata     string    `db:"metadata" json:"metadata"` // JSONB stored as string
	CreatedAt    time.Time `db:"created_at" json:"createdAt"`
	ModifiedAt   time.Time `db:"modified_at" json:"modifiedAt"`
}

// UserAITokenUsageHistory represents the user_ai_token_usage_history table structure
type UserAITokenUsageHistory struct {
	ID           string    `db:"id" json:"id"`
	UserID       string    `db:"user_id" json:"userId"`
	Amount       int64     `db:"amount" json:"amount"`
	LastBalance  int64     `db:"last_balance" json:"lastBalance"`
	AfterBalance int64     `db:"after_balance" json:"afterBalance"`
	Metadata     string    `db:"metadata" json:"metadata"` // JSONB stored as string
	CreatedAt    time.Time `db:"created_at" json:"createdAt"`
}

// TokenUpdateRequest represents the request body for token operations
type TokenUpdateRequest struct {
	Amount   int64  `json:"amount" binding:"required"`
	Metadata string `json:"metadata"`
}

// TokenBalanceResult holds the token balance query results
type TokenBalanceResult struct {
	Tokens UserAITokens
	Error  error
}

// TokenUpdateResult holds the results of token update operations
type TokenUpdateResult struct {
	NewBalance int64
	Error      error
}

// HistoryResult holds the token history query results
type HistoryResult struct {
	History []UserAITokenUsageHistory
	Error   error
}

// CacheEntry is used for token balance caching
type CacheEntry struct {
	Balance   int64
	Timestamp time.Time
}

// Global cache for token balances with TTL
var (
	tokenBalanceCache      = make(map[string]CacheEntry)
	tokenBalanceCacheMutex sync.RWMutex
	cacheTTL               = 30 * time.Second
)

// RegisterAITokenRoutes registers the AI token related routes
func RegisterAITokenRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.GET("/ai-tokens/:userId", getAITokenBalance(dbx))
		v0.POST("/ai-tokens/:userId/deduct", deductTokens(dbx))
		v0.POST("/ai-tokens/:userId/add", addTokens(dbx))
		v0.GET("/ai-tokens/:userId/history", getTokenUsageHistory(dbx))
	}

	// Start a background goroutine to clean the cache
	go cleanTokenBalanceCache()
}

// cleanTokenBalanceCache periodically cleans expired entries from the token balance cache
func cleanTokenBalanceCache() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		cleanCount := 0
		tokenBalanceCacheMutex.Lock()
		now := time.Now()
		for userID, entry := range tokenBalanceCache {
			if now.Sub(entry.Timestamp) > cacheTTL {
				delete(tokenBalanceCache, userID)
				cleanCount++
			}
		}
		tokenBalanceCacheMutex.Unlock()
		log.Printf("Cleaned %d expired entries from token balance cache", cleanCount)
	}
}

// getAITokenBalance retrieves the current token balance for a user with caching
func getAITokenBalance(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		// Check cache first
		tokenBalanceCacheMutex.RLock()
		if entry, found := tokenBalanceCache[userID]; found {
			if time.Since(entry.Timestamp) < cacheTTL {
				// Return cached balance if it's still valid
				tokenBalanceCacheMutex.RUnlock()
				c.JSON(http.StatusOK, gin.H{
					"tokens": UserAITokens{
						UserID:       userID,
						TokenBalance: entry.Balance,
					},
					"cached": true,
				})
				return
			}
		}
		tokenBalanceCacheMutex.RUnlock()

		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		resultCh := make(chan TokenBalanceResult, 1)

		// Start a goroutine to fetch token balance
		go func() {
			var tokens UserAITokens
			query := `
				SELECT id, user_id, token_balance, metadata, created_at, modified_at
				FROM user_ai_tokens
				WHERE user_id = $1
			`

			err := dbx.GetContext(ctx, &tokens, query, userID)
			if err != nil {
				if err == sql.ErrNoRows {
					// If no record exists, create one with default balance
					id := lib.GenerateULID()
					insertQuery := `
						INSERT INTO user_ai_tokens (id, user_id, token_balance, metadata)
						VALUES ($1, $2, 0, '{}')
						RETURNING id, user_id, token_balance, metadata, created_at, modified_at
					`
					err = dbx.GetContext(ctx, &tokens, insertQuery, id, userID)
					if err != nil {
						log.Printf("Error creating token balance for user %s: %v", userID, err)
						resultCh <- TokenBalanceResult{Error: fmt.Errorf("failed to create token balance: %w", err)}
						return
					}
				} else {
					log.Printf("Error fetching token balance for user %s: %v", userID, err)
					resultCh <- TokenBalanceResult{Error: fmt.Errorf("failed to fetch token balance: %w", err)}
					return
				}
			}

			resultCh <- TokenBalanceResult{Tokens: tokens}
		}()

		// Wait for result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			// Update cache
			tokenBalanceCacheMutex.Lock()
			tokenBalanceCache[userID] = CacheEntry{
				Balance:   result.Tokens.TokenBalance,
				Timestamp: time.Now(),
			}
			tokenBalanceCacheMutex.Unlock()

			c.JSON(http.StatusOK, gin.H{"tokens": result.Tokens})
		}
	}
}

// deductTokens handles token deduction and creates usage history
func deductTokens(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		var req TokenUpdateRequest
		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		if req.Amount <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Amount must be positive"})
			return
		}

		// Use context with timeout for database operations
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		// Create a goroutine to perform the token deduction
		resultCh := make(chan TokenUpdateResult, 1)

		go func() {
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to start transaction: %w", err)}
				return
			}
			defer tx.Rollback()

			// Get current balance
			var currentBalance int64
			err = tx.GetContext(ctx, &currentBalance, "SELECT token_balance FROM user_ai_tokens WHERE user_id = $1 FOR UPDATE", userID)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to get current balance: %w", err)}
				return
			}

			if currentBalance < req.Amount {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("insufficient token balance")}
				return
			}

			// Update balance
			newBalance := currentBalance - req.Amount
			_, err = tx.ExecContext(ctx, `
				UPDATE user_ai_tokens 
				SET token_balance = $1, modified_at = CURRENT_TIMESTAMP 
				WHERE user_id = $2
			`, newBalance, userID)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to update balance: %w", err)}
				return
			}

			// Record usage
			id := lib.GenerateULID()
			_, err = tx.ExecContext(ctx, `
				INSERT INTO user_ai_token_usage_history 
				(id, user_id, amount, last_balance, after_balance, metadata)
				VALUES ($1, $2, $3, $4, $5, $6)
			`, id, userID, -req.Amount, currentBalance, newBalance, req.Metadata)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to record usage: %w", err)}
				return
			}

			if err := tx.Commit(); err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to commit transaction: %w", err)}
				return
			}

			// Update cache after successful transaction
			tokenBalanceCacheMutex.Lock()
			tokenBalanceCache[userID] = CacheEntry{
				Balance:   newBalance,
				Timestamp: time.Now(),
			}
			tokenBalanceCacheMutex.Unlock()

			resultCh <- TokenUpdateResult{newBalance, nil}
		}()

		// Wait for the result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				if result.Error.Error() == "insufficient token balance" {
					c.JSON(http.StatusBadRequest, gin.H{"error": result.Error.Error()})
				} else {
					c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				}
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Tokens deducted successfully",
				"balance": result.NewBalance,
			})
		}
	}
}

// addTokens handles adding tokens to user balance
func addTokens(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")

		var req TokenUpdateRequest
		if err := c.BindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
			return
		}

		if req.Amount <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Amount must be positive"})
			return
		}

		// Use context with timeout for database operations
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		// Create a goroutine to perform the token addition
		resultCh := make(chan TokenUpdateResult, 1)

		go func() {
			tx, err := dbx.BeginTxx(ctx, nil)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to start transaction: %w", err)}
				return
			}
			defer tx.Rollback()

			// Get or create token balance record
			var currentBalance int64
			err = tx.GetContext(ctx, &currentBalance, `
				INSERT INTO user_ai_tokens (id, user_id, token_balance)
				VALUES ($1, $2, 0)
				ON CONFLICT (user_id) DO UPDATE
				SET token_balance = user_ai_tokens.token_balance
				RETURNING token_balance
			`, lib.GenerateULID(), userID)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to get/create balance record: %w", err)}
				return
			}

			newBalance := currentBalance + req.Amount

			// Update balance
			_, err = tx.ExecContext(ctx, `
				UPDATE user_ai_tokens 
				SET token_balance = $1, modified_at = CURRENT_TIMESTAMP 
				WHERE user_id = $2
			`, newBalance, userID)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to update balance: %w", err)}
				return
			}

			// Record usage
			id := lib.GenerateULID()
			_, err = tx.ExecContext(ctx, `
				INSERT INTO user_ai_token_usage_history 
				(id, user_id, amount, last_balance, after_balance, metadata)
				VALUES ($1, $2, $3, $4, $5, $6)
			`, id, userID, req.Amount, currentBalance, newBalance, req.Metadata)
			if err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to record usage: %w", err)}
				return
			}

			if err := tx.Commit(); err != nil {
				resultCh <- TokenUpdateResult{0, fmt.Errorf("failed to commit transaction: %w", err)}
				return
			}

			// Update cache after successful transaction
			tokenBalanceCacheMutex.Lock()
			tokenBalanceCache[userID] = CacheEntry{
				Balance:   newBalance,
				Timestamp: time.Now(),
			}
			tokenBalanceCacheMutex.Unlock()

			resultCh <- TokenUpdateResult{newBalance, nil}
		}()

		// Wait for the result or timeout
		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"message": "Tokens added successfully",
				"balance": result.NewBalance,
			})
		}
	}
}

// getTokenUsageHistory retrieves the token usage history for a user
func getTokenUsageHistory(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.Param("userId")
		limit := 100 // Default limit

		// Parse limit parameter if provided
		if limitStr := c.Query("limit"); limitStr != "" {
			if _, err := fmt.Sscanf(limitStr, "%d", &limit); err != nil || limit <= 0 {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
				return
			}
			if limit > 500 {
				limit = 500 // Cap the maximum limit
			}
		}

		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		resultCh := make(chan HistoryResult, 1)

		go func() {
			var history []UserAITokenUsageHistory
			query := `
				SELECT id, user_id, amount, last_balance, after_balance, metadata, created_at
				FROM user_ai_token_usage_history
				WHERE user_id = $1
				ORDER BY created_at DESC
				LIMIT $2
			`

			if err := dbx.SelectContext(ctx, &history, query, userID, limit); err != nil {
				log.Printf("Error fetching token usage history for user %s: %v", userID, err)
				resultCh <- HistoryResult{Error: fmt.Errorf("failed to fetch token usage history: %w", err)}
				return
			}

			resultCh <- HistoryResult{History: history}
		}()

		select {
		case <-ctx.Done():
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request timed out"})
			return
		case result := <-resultCh:
			if result.Error != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"history": result.History})
		}
	}
}
