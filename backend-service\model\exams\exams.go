package model

import (
	"time"
)

// ENUM TYPES
// ExamSessionStatus defines the possible statuses of an exam session
type ExamSessionStatus string

const (
	SessionActive    ExamSessionStatus = "ACTIVE"
	SessionCompleted ExamSessionStatus = "COMPLETED"
	SessionAbandoned ExamSessionStatus = "ABANDONED"
)

// ExamSessionType defines the possible types of an exam session
type ExamSessionType string

const (
	SessionPractice ExamSessionType = "PRACTICE"
	SessionExam     ExamSessionType = "EXAM"
)

// STRUCT MODELS
// ExamSession represents an individual exam session
type ExamSessions struct {
	Id               string            `json:"id" db:"id"`                               // ULID for the session ID
	SessionId        string            `json:"session_id" db:"session_id"`               // Additional ULID for tracking the session
	UserId           string            `json:"user_id" db:"user_id"`                     // User who takes the exam
	ExamId           string            `json:"exam_id" db:"exam_id"`                     // Foreign key to link the session to a specific exam
	Type             ExamSessionType   `json:"type" db:"type"`                           // Type of session: PRACTICE or EXAM
	Status           ExamSessionStatus `json:"status" db:"status"`                       // Status of the session
	StartTime        time.Time         `json:"start_time" db:"start_time"`               // When the session started
	EndTime          *time.Time        `json:"end_time,omitempty" db:"end_time"`         // When the session ended (nullable)
	Answers          string            `json:"answers" db:"answers"`                     // JSONB column to store the user's answers
	FlaggedQuestions *string           `json:"flagged_questions" db:"flagged_questions"` // JSONB column to store the user's flagged_questions
	CreatedAt        time.Time         `json:"created_at" db:"created_at"`               // When the session was created
	ModifiedAt       time.Time         `json:"modified_at" db:"modified_at"`             // When the session was last modified
	Subject          *string           `json:"subject" db:"subject"`
}
type ExamSessionsWithDuration struct {
	Id                string            `json:"id" db:"id"`                               // ULID for the session ID
	SessionId         string            `json:"session_id" db:"session_id"`               // Additional ULID for tracking the session
	UserId            string            `json:"user_id" db:"user_id"`                     // User who takes the exam
	ExamId            string            `json:"exam_id" db:"exam_id"`                     // Foreign key to link the session to a specific exam
	Type              ExamSessionType   `json:"type" db:"type"`                           // Type of session: PRACTICE or EXAM
	Status            ExamSessionStatus `json:"status" db:"status"`                       // Status of the session
	StartTime         time.Time         `json:"start_time" db:"start_time"`               // When the session started
	EndTime           *time.Time        `json:"end_time,omitempty" db:"end_time"`         // When the session ended (nullable)
	Answers           string            `json:"answers" db:"answers"`                     // JSONB column to store the user's answers
	FlaggedQuestions  *string           `json:"flagged_questions" db:"flagged_questions"` // JSONB column to store the user's flagged_questions
	CreatedAt         time.Time         `json:"created_at" db:"created_at"`               // When the session was created
	ModifiedAt        time.Time         `json:"modified_at" db:"modified_at"`             // When the session was last modified
	ExamDuration      string            `json:"exam_duration" db:"exam_duration"`
	ElapsedDuration   string            `json:"elapsed_duration" db:"elapsed_duration"`
	RemainingDuration string            `json:"remaining_duration" db:"remaining_duration"`
	Subject           *string           `json:"subject" db:"subject"`
}

// PostExamSession is used for creating a new exam session
type PostExamSessions struct {
	UserId           string            `json:"user_id" binding:"required"`    // User who takes the exam
	ExamId           string            `json:"exam_id" binding:"required"`    // Foreign key to link to a specific exam
	Type             ExamSessionType   `json:"type" binding:"required"`       // Type of session: PRACTICE or EXAM
	Status           ExamSessionStatus `json:"status" binding:"required"`     // Status of the session
	StartTime        time.Time         `json:"start_time" binding:"required"` // When the session started
	EndTime          *time.Time        `json:"end_time,omitempty"`            // When the session ended (nullable)
	Answers          string            `json:"answers,omitempty"`             // JSONB object to store user's answers
	FlaggedQuestions string            `json:"flagged_questions,omitempty"`   // JSONB object to store user's flagged_questions
	Subject          string            `json:"subject,omitempty"`
}

type PostGetExamSessionTrial struct {
	Subject string `json:"subject,omitempty"`
}

// UpdateExamSession is used for updating an existing exam session
type UpdateExamSessions struct {
	Status           *ExamSessionStatus `json:"status,omitempty"`            // Status of the session (optional update)
	EndTime          *time.Time         `json:"end_time,omitempty"`          // When the session ended (optional update)
	Answers          *string            `json:"answers,omitempty"`           // JSONB object to store updated user's answers (optional update)
	FlaggedQuestions *string            `json:"flagged_questions,omitempty"` // JSONB object to store updated user's flagged_questions (optional update)
}

// ExamScore represents the scores for each exam session
type ExamScores struct {
	Id             string    `json:"id" db:"id"`                           // ULID for the score entry
	SessionId      string    `json:"session_id" db:"session_id"`           // Foreign key to link the score to an exam session
	TotalQuestions int       `json:"total_questions" db:"total_questions"` // Total number of questions in the exam
	CorrectAnswers int       `json:"correct_answers" db:"correct_answers"` // Number of questions answered correctly
	Score          float64   `json:"score" db:"score"`                     // Total score for the exam
	Accuracy       float64   `json:"accuracy" db:"accuracy"`               // Accuracy percentage
	MetadataScores string    `json:"metadata_scores" db:"metadata_scores"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`   // When the score was created
	ModifiedAt     time.Time `json:"modified_at" db:"modified_at"` // When the score was last modified
}

// PostExamScore is used for creating a new exam score
type PostExamScores struct {
	SessionId      string   `json:"session_id" binding:"required"` // Foreign key to link to an exam session
	TotalQuestions *int     `json:"total_questions"`               // Total number of questions in the exam
	CorrectAnswers *int     `json:"correct_answers"`               // Number of questions answered correctly
	Score          *float64 `json:"score"`                         // Total score for the exam
	MetadataScores string   `json:"metadata_scores" db:"metadata_scores"`
}

// UpdateExamScore is used for updating an existing exam score
type UpdateExamScores struct {
	CorrectAnswers *int     `json:"correct_answers,omitempty"` // Number of questions answered correctly (optional update)
	Score          *float64 `json:"score,omitempty"`           // Updated total score for the exam (optional update)
}

// ExamUri is used for binding URI parameters for available exam endpoints
type ExamUri struct {
	ID string `uri:"id" binding:"required"`
}
type ExamUserIdUri struct {
	USERID string `uri:"user_id" binding:"required"`
}

type ExamSessionIdUri struct {
	SESSIONID string `uri:"session_id" binding:"required"`
}

type ExamSessionExamUserIdUri struct {
	EXAMID string `uri:"exam_id" binding:"required"`
	USERID string `uri:"user_id" binding:"required"`
}
