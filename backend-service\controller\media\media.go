package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	model "github.com/terang-ai/backend-service/model/media"
	repository "github.com/terang-ai/backend-service/repository/media"
)

type MediaController struct {
	Dbx  *sqlx.DB
	Repo repository.MediaRepositoryInterface
}

func NewMediaController(dbx *sqlx.DB) *MediaController {
	return &MediaController{
		Dbx:  dbx,
		Repo: repository.NewMediaRepository(dbx),
	}
}

func (c *MediaController) DeleteMedia(ctx *gin.Context) {
	var uri model.MediaUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.<PERSON>(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to delete Media
	deleted, err := c.Repo.DeleteMedia(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "delete Media failed"})
		return
	}

	if !deleted {
		ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Media not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "msg": "delete Media successfully"})
}

func (c *MediaController) GetAllMedia(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))

	entities, paginationInfo, err := c.Repo.GetAllMedia(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "An error occurred"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": entities, "msg": "medias retrieved successfully", "_pagination": paginationInfo})
}

func (c *MediaController) GetOneMedia(ctx *gin.Context) {
	var uri model.MediaUri

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Call repository method to retrieve one Media by ID
	media, err := c.Repo.GetOneMedia(uri.ID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "Media not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": media, "msg": "get Media successfully"})
}

func (c *MediaController) InsertMedia(ctx *gin.Context) {
	var post model.PostMedia
	// var entity model.Media
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	inserted, err := c.Repo.InsertMedia(post.Url, post.AvailableExamId, post)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "insert Media failed"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": inserted, "msg": "insert Media successfully"})
}

func (c *MediaController) UpdateMedia(ctx *gin.Context) {
	var uri model.MediaUri
	var post model.UpdateMedia

	// Bind URI parameters to struct
	if err := ctx.ShouldBindUri(&uri); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "error": err.Error(), "msg": "invalid URI parameters"})
		return
	}

	// Bind JSON body to struct
	if err := ctx.ShouldBindJSON(&post); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "failed", "msg": err.Error()})
		return
	}

	// Call repository method to update Media
	updatedExam, err := c.Repo.UpdateMedia(uri.ID, post)
	if err != nil {
		if err.Error() == fmt.Sprintf("availableExam with ID %s does not exist", uri.ID) {
			ctx.JSON(http.StatusNotFound, gin.H{"status": "failed", "msg": "Media not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"status": "failed", "error": err.Error(), "msg": "update Media failed"})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": updatedExam, "msg": "update Media successfully"})
}
