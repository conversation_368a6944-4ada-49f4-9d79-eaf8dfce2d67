package model

import (
	"time"
)

// PaymentStatus represents the visibility status of the comsheet
type PaymentStatus string

const (
	PENDING    PaymentStatus = "PENDING"
	PROCESSING PaymentStatus = "PROCESSING"
	PAID       PaymentStatus = "PAID"
	REFUNDED   PaymentStatus = "REFUNDED"
	WITHDRAWN  PaymentStatus = "WITHDRAWN"
	FAILED     PaymentStatus = "FAILED"
)

// Payment represents a Payment in the system
type Payment struct {
	Id               string        `json:"id"`
	InvoiceId        string        `json:"invoice_id"`
	PaymentMethod    string        `json:"payment_method"`
	Amount           float64       `json:"amount"`
	Status           PaymentStatus `json:"status"`
	PaymentStartDate *time.Time    `json:"payment_start_date"`
	PaymentEndDate   *time.Time    `json:"payment_end_date"`
	TransactionId    *string       `json:"transaction_id"`
	PaymentLink      *string       `json:"payment_link"`
	CreatedAt        time.Time     `json:"created_at"`
	ModifiedAt       time.Time     `json:"modified_at"`
	ClientEmail      *string       `json:"client_email"`
}

// PostPayment is used for creating a new Payment. Fields can be added as necessary.
type PostPayment struct {
	Id               string        `json:"id"`
	InvoiceId        string        `json:"invoice_id" binding:"required"`
	PaymentMethod    *string       `json:"payment_method" binding:"required"`
	Amount           float64       `json:"amount" binding:"required"`
	Status           PaymentStatus `json:"status" binding:"required"`
	PaymentStartDate *string       `json:"payment_start_date"`
	PaymentEndDate   *string       `json:"payment_end_date"`
	TransactionId    *string       `json:"transaction_id"`
	PaymentLink      *string       `json:"payment_link"`
	ClientEmail      *string       `json:"client_email"`
}

type UpdatePayment struct {
	InvoiceId        *string        `json:"invoice_id,omitempty"`
	PaymentMethod    *string        `json:"payment_method,omitempty"`
	Amount           *float64       `json:"amount,omitempty"`
	Status           *PaymentStatus `json:"status,omitempty"`
	PaymentStartDate *string        `json:"payment_start_date,omitempty"`
	PaymentEndDate   *string        `json:"payment_end_date,omitempty"`
	TransactionId    *string        `json:"transaction_id,omitempty"`
	PaymentLink      *string        `json:"payment_link,omitempty"`
	ClientEmail      *string        `json:"client_email,omitempty"`
}

type PaymentUri struct {
	ID string `uri:"id" binding:"required"`
}
