CREATE OR REPLACE FUNCTION identify_exam_type(metadata_scores JSONB)
RETURNS TEXT AS $$
BEGIN
    IF 
        metadata_scores->>'twk_score' IS NOT NULL OR 
        metadata_scores->>'tiu_score' IS NOT NULL OR 
        metadata_scores->>'tkp_score' IS NOT NULL 
    THEN
        RETURN 'CPNS';
    ELSIF 
        metadata_scores->>'kepribadian_score' IS NOT NULL OR 
        metadata_scores->>'pemecahan_masalah_score' IS NOT NULL 
    THEN
        RETURN 'LPDP';
    ELSIF 
        metadata_scores->>'kompetensi_teknis_score' IS NOT NULL OR 
        metadata_scores->>'manajerial_score' IS NOT NULL 
    THEN
        RETURN 'PPPK';
    ELSE
        RETURN 'UNKNOWN';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Update existing exam scores to add exam_type field
UPDATE exam_scores
SET metadata_scores = jsonb_set(
    metadata_scores, 
    '{exam_type}', 
    to_jsonb(identify_exam_type(metadata_scores))
)
WHERE metadata_scores IS NOT NULL 
AND metadata_scores->>'exam_type' IS NULL;

-- Create index for better performance on queries that filter by exam_type
CREATE INDEX IF NOT EXISTS idx_metadata_exam_type 
ON exam_scores USING gin ((metadata_scores->'exam_type'));

-- Create index for better performance on queries that select specific subject scores
CREATE INDEX IF NOT EXISTS idx_metadata_scores_subject 
ON exam_scores USING gin (metadata_scores);

-- Drop the temporary function
DROP FUNCTION identify_exam_type(JSONB);