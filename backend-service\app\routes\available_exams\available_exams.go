package available_exams

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	available_exams "github.com/terang-ai/backend-service/controller/available_exams"
	"github.com/terang-ai/backend-service/lib"
	model "github.com/terang-ai/backend-service/model/available_exams"
	repository "github.com/terang-ai/backend-service/repository/available_exams"
)

func RegisterRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	availableExamsCtrl := available_exams.NewAvailableExamsController(dbx, redis)

	availableExamsBaseGroup := r.Group("/v1")
	{
		availableExamsBaseGroup.POST("/available-exams", availableExamsCtrl.InsertAvailableExam)
		availableExamsBaseGroup.GET("/available-exams", availableExamsCtrl.GetAllAvailableExams)
		availableExamsBaseGroup.GET("/available-exams/:id", availableExamsCtrl.GetOneAvailableExam)
		availableExamsBaseGroup.PUT("/available-exams/:id", availableExamsCtrl.UpdateAvailableExam)
		availableExamsBaseGroup.DELETE("/available-exams/:id", availableExamsCtrl.DeleteAvailableExam)
	}

	availableExamsBaseGroupV2 := r.Group("/v2")
	{
		availableExamsBaseGroupV2.GET("/available-exams", availableExamsCtrl.GetAllAvailableExamsWithCache)
		availableExamsBaseGroupV2.GET("/available-exams/:user_email", availableExamsCtrl.GetAllAvailableExamsV2)
		availableExamsBaseGroupV2.GET("/available-exams/public", availableExamsCtrl.GetPublicAvailableExamsV2)
		availableExamsBaseGroupV2.GET("/available-exams/purchased/:user_email", availableExamsCtrl.GetAllPurchasedExamsByEmail)
		availableExamsBaseGroupV2.GET("/available-exams/taken/:user_email", availableExamsCtrl.GetAllExamsTaken)
	}

	// Exam Question Hints Routes
	examQuestionHintsGroup := r.Group("/v1/exam-question-hints")
	{
		examQuestionHintsGroup.POST("", availableExamsCtrl.InsertExamQuestionAnswerHint)
		examQuestionHintsGroup.GET("/:exam_id", availableExamsCtrl.GetExamQuestionAnswerHints)
		examQuestionHintsGroup.GET("/sessions/:session_id", availableExamsCtrl.GetExamQuestionAnswerHintsBySessionId)
		examQuestionHintsGroup.POST("/sessions/:session_id/subject", availableExamsCtrl.GetExamQuestionAnswerHintsBySessionIdBySubject)
	}

	v0 := r.Group("/v0")
	{
		v0.POST("/available-exams-with-media", insertExamWithMediaAndHints(dbx))
		v0.GET("/available-exams/practice", GetAvailablePracticeExams(dbx, redis))
	}
}

// Add this function to your RegisterRoutes function
func RegisterDaerah3TRoutes(r *gin.Engine, dbx *sqlx.DB, redis *redis.Client) {
	v1 := r.Group("/v1")
	{
		v1.GET("/available-exams/daerah3t/:user_email", GetAvailableExamsDaerah3T(dbx, redis))
		v1.GET("/available-exams/daerah3t/:user_email/:id", GetOneDaerah3TExam(dbx, redis))
	}
}

func GetAvailableExamsDaerah3T(dbx *sqlx.DB, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userEmail := c.Param("user_email")

		// Parse pagination parameters
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		pageSize, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
		examType := c.DefaultQuery("type", "")

		// Ensure minimum values
		if page < 1 {
			page = 1
		}
		if pageSize < 1 {
			pageSize = 10
		}

		// Create a cache key based on the request parameters
		cacheKey := fmt.Sprintf("daerah3t-exams:%s:%d:%d:%s", userEmail, page, pageSize, examType)

		// Try to get data from cache first
		cachedData, err := redis.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return the cached response
			c.Header("X-Cache", "HIT")
			c.Data(http.StatusOK, "application/json", []byte(cachedData))
			return
		}

		// Cache miss - proceed with database query
		c.Header("X-Cache", "MISS")

		// Get categories that the user has free access to via daerah 3T
		var freeCategoryIDs []string
		categoryQuery := `
			SELECT category_id
			FROM free_daerah_3t
			WHERE user_email = $1 AND is_active = true
		`

		err = dbx.Select(&freeCategoryIDs, categoryQuery, userEmail)
		if err != nil {
			log.Printf("Error fetching free categories for user: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve free categories"})
			return
		}

		// If user has no free categories, return empty result
		if len(freeCategoryIDs) == 0 {
			emptyResponse := struct {
				Data       []model.AvailableExamV2                `json:"data"`
				Pagination repository.AvailableExamPaginationInfo `json:"pagination"`
				FreeAccess bool                                   `json:"freeAccess"`
			}{
				Data: []model.AvailableExamV2{},
				Pagination: repository.AvailableExamPaginationInfo{
					TotalData:   0,
					TotalPages:  0,
					CurrentPage: page,
					PageSize:    pageSize,
				},
				FreeAccess: false,
			}

			jsonData, _ := json.Marshal(emptyResponse)

			// Store in cache with 5-minute expiration
			redis.Set(ctx, cacheKey, jsonData, 5*time.Minute)

			c.Data(http.StatusOK, "application/json", jsonData)
			return
		}

		// Calculate offset for pagination
		offset := (page - 1) * pageSize

		// Prepare query arguments with category IDs
		args := make([]interface{}, 0, len(freeCategoryIDs)+3) // +3 for pageSize, offset, and possibly examType
		placeholders := make([]string, len(freeCategoryIDs))

		for i, categoryID := range freeCategoryIDs {
			args = append(args, categoryID)
			placeholders[i] = fmt.Sprintf("$%d", i+1)
		}

		categoryPlaceholders := "(" + lib.JoinWithComma(placeholders) + ")"

		// Base query to get exams with free categories
		baseWhereClause := fmt.Sprintf(`
			WHERE EXISTS (
				SELECT 1 
				FROM exam_categories ec 
				WHERE ec.exam_id = e.id 
				AND ec.category_id IN %s
			)`, categoryPlaceholders)

		// Add exam type filter if provided
		if examType != "" {
			baseWhereClause += fmt.Sprintf(" AND e.type = $%d", len(args)+1)
			args = append(args, examType)
		}

		// Add pagination parameters
		args = append(args, pageSize, offset)

		// Query to get exams with pagination - using 0 as baseline_price for all exams
		dataQuery := fmt.Sprintf(`
			SELECT 
				e.id,
				e.name,
				e.subname,
				e.description,
				0 AS baseline_price,  -- Set baseline_price to 0 for all free exams
				e.visibility,
				e.duration,
				e.type,
				e.subject,
				e.created_at,
				e.modified_at,
				COALESCE(m.url, '') AS media_url,
				c.name AS category_name,
				c.id AS category_id,
				c.image_url AS category_image_url,
				TRUE AS is_free_access,
				TRUE AS is_purchased
			FROM 
				available_exams e
			LEFT JOIN
				media m ON e.id = m.available_exam_id
			LEFT JOIN 
				exam_categories ec ON e.id = ec.exam_id
			LEFT JOIN 
				categories c ON ec.category_id = c.id
			%s
			ORDER BY e.id
			LIMIT $%d OFFSET $%d
		`, baseWhereClause, len(args)-1, len(args))

		// Count total exams for pagination
		countQuery := fmt.Sprintf(`
			SELECT COUNT(DISTINCT e.id)
			FROM available_exams e
			%s
		`, baseWhereClause)

		var totalData int
		err = dbx.Get(&totalData, countQuery, args[:len(args)-2]...)
		if err != nil {
			log.Printf("Error counting daerah 3T exams: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count exams"})
			return
		}

		// Get the exams data
		var exams []model.AvailableExamV2
		err = dbx.Select(&exams, dataQuery, args...)
		if err != nil {
			log.Printf("Error querying daerah 3T exams: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve exams"})
			return
		}

		// Calculate pagination info
		totalPages := int(math.Ceil(float64(totalData) / float64(pageSize)))
		paginationInfo := repository.AvailableExamPaginationInfo{
			TotalData:   totalData,
			TotalPages:  totalPages,
			CurrentPage: page,
			PageSize:    pageSize,
		}

		// Create the response
		response := struct {
			Data       []model.AvailableExamV2                `json:"data"`
			Pagination repository.AvailableExamPaginationInfo `json:"pagination"`
			FreeAccess bool                                   `json:"freeAccess"`
		}{
			Data:       exams,
			Pagination: paginationInfo,
			FreeAccess: true,
		}

		// Marshal response to JSON
		jsonData, err := json.Marshal(response)
		if err != nil {
			log.Printf("Error marshaling JSON response: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create response"})
			return
		}

		// Store in cache with 5-minute expiration
		err = redis.Set(ctx, cacheKey, jsonData, 5*time.Minute).Err()
		if err != nil {
			log.Printf("Error setting cache: %v", err)
			// Continue even if caching fails
		}

		// Return the response
		c.Data(http.StatusOK, "application/json", jsonData)
	}
}

func GetOneDaerah3TExam(dbx *sqlx.DB, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		examId := c.Param("id")
		userEmail := c.Param("user_email")

		// Validate inputs
		if examId == "" || userEmail == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Exam ID and user email are required"})
			return
		}

		// Create cache key
		cacheKey := fmt.Sprintf("daerah3t-exam:%s:%s", examId, userEmail)

		// Try to get from cache first
		cachedData, err := redis.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit
			c.Header("X-Cache", "HIT")
			c.Data(http.StatusOK, "application/json", []byte(cachedData))
			return
		}

		// Cache miss
		c.Header("X-Cache", "MISS")

		// Create repository
		repo := repository.NewAvailableExamRepository(dbx, redis)

		// Get the exam
		exam, hasAccess, err := repo.GetOneAvailableExamDaerah3T(examId, userEmail)
		if err != nil {
			log.Printf("Error retrieving exam: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve exam"})
			return
		}

		// If user doesn't have access, return 403 Forbidden
		if !hasAccess {
			response := gin.H{
				"error":      "User does not have free access to this exam",
				"freeAccess": false,
			}
			jsonData, _ := json.Marshal(response)

			// Cache the negative result too
			redis.Set(ctx, cacheKey, jsonData, 5*time.Minute)

			c.Data(http.StatusForbidden, "application/json", jsonData)
			return
		}

		// Create the response
		response := struct {
			Data       model.AvailableExamV2 `json:"data"`
			FreeAccess bool                  `json:"freeAccess"`
		}{
			Data:       exam,
			FreeAccess: true,
		}

		// Marshal to JSON
		jsonData, err := json.Marshal(response)
		if err != nil {
			log.Printf("Error marshaling JSON response: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create response"})
			return
		}

		// Store in cache with 5-minute expiration
		redis.Set(ctx, cacheKey, jsonData, 5*time.Minute)

		// Return the response
		c.Data(http.StatusOK, "application/json", jsonData)
	}
}

type MediaInput struct {
	URL       string `json:"url"`
	SizeBytes *int   `json:"sizeBytes,omitempty"`
	MimeType  string `json:"mimeType,omitempty"`
	Width     *int   `json:"width,omitempty"`
	Height    *int   `json:"height,omitempty"`
}

type PostExamQuestionAnswerHint struct {
	ExamId       string  `json:"exam_id,omitempty"`
	Data         string  `json:"data" binding:"required"`
	PassingGrade *string `json:"passing_grade" binding:"required"`
}

type CategoryInput struct {
	ID string `json:"categoryId" binding:"required"`
}

type ExamWithMediaAndHintsInput struct {
	Name          string                     `json:"name" binding:"required"`
	Subname       string                     `json:"subname" binding:"required"`
	Description   string                     `json:"description" binding:"required"`
	BaselinePrice float64                    `json:"baselinePrice"`
	Visibility    string                     `json:"visibility" binding:"required"`
	Duration      string                     `json:"duration"`
	UserID        string                     `json:"userId" binding:"required"`
	Type          string                     `json:"type" binding:"required,oneof=PRACTICE EXAM"`
	Subject       *string                    `json:"subject"` // New field
	Media         []MediaInput               `json:"media"`
	Hints         PostExamQuestionAnswerHint `json:"hints"`
	Categories    []CategoryInput            `json:"categories" binding:"required"`
}

func insertExamWithMediaAndHints(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		var input ExamWithMediaAndHintsInput

		if err := c.ShouldBindJSON(&input); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Start transaction
		tx, err := dbx.Beginx()
		if err != nil {
			log.Printf("Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
			return
		}

		// Generate IDs
		examID := lib.GenerateULID()

		// 1. Insert exam
		examQuery := `
            INSERT INTO available_exams (
                id, name, subname, description, baseline_price,
                visibility, duration, user_id, type, subject, created_at, modified_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            ) RETURNING id`

		_, err = tx.Exec(
			examQuery,
			examID,
			input.Name,
			input.Subname,
			input.Description,
			input.BaselinePrice,
			input.Visibility,
			input.Duration,
			input.UserID,
			input.Type,
			input.Subject, // Added subject field
		)

		if err != nil {
			tx.Rollback()
			log.Printf("Error inserting exam: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert exam"})
			return
		}

		// 2. Insert exam categories
		categoryIDs := make([]string, 0)
		for _, category := range input.Categories {
			categoryLinkID := lib.GenerateULID()
			categoryQuery := `
                INSERT INTO exam_categories (
                    id, exam_id, category_id
                ) VALUES ($1, $2, $3)
                RETURNING id`

			var result sql.Result
			result, err = tx.Exec(
				categoryQuery,
				categoryLinkID,
				examID,
				category.ID,
			)

			if err != nil {
				tx.Rollback()
				log.Printf("Error inserting category link: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to link category"})
				return
			}

			rows, _ := result.RowsAffected()
			if rows > 0 {
				categoryIDs = append(categoryIDs, categoryLinkID)
			}
		}

		// 3. Insert media
		mediaIDs := make([]string, 0)
		for _, media := range input.Media {
			mediaID := lib.GenerateULID()
			mediaQuery := `
                INSERT INTO media (
                    id, url, available_exam_id, size_in_bytes,
                    mime_type, width, height, uploaded_at, modified_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                ) RETURNING id`

			var result sql.Result
			result, err = tx.Exec(
				mediaQuery,
				mediaID,
				media.URL,
				examID,
				media.SizeBytes,
				media.MimeType,
				media.Width,
				media.Height,
			)

			if err != nil {
				tx.Rollback()
				log.Printf("Error inserting media: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert media"})
				return
			}

			rows, _ := result.RowsAffected()
			if rows > 0 {
				mediaIDs = append(mediaIDs, mediaID)
			}
		}

		// 4. Insert exam hints
		hintsID := lib.GenerateULID()
		hintsQuery := `
            INSERT INTO exam_question_answer_hints (
                id, exam_id, data, passing_grade
            ) VALUES (
                $1, $2, $3, $4
            ) RETURNING id`

		_, err = tx.Exec(
			hintsQuery,
			hintsID,
			examID,
			input.Hints.Data,
			input.Hints.PassingGrade,
		)

		if err != nil {
			tx.Rollback()
			log.Printf("Error inserting hints: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert hints"})
			return
		}

		// Explicitly trigger data processing after insertion
		_, err = tx.Exec("SELECT process_exam_data_in_batches()")
		if err != nil {
			// Just log the error but don't fail the transaction
			log.Printf("Warning: Failed to trigger exam data processing: %v", err)
		}

		// Commit transaction
		if err = tx.Commit(); err != nil {
			tx.Rollback()
			log.Printf("Error committing transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message":     "Exam, categories, media, and hints created successfully",
			"examId":      examID,
			"categoryIds": categoryIDs,
			"mediaIds":    mediaIDs,
			"hintsId":     hintsID,
		})
	}
}

type Exam struct {
	ID            string  `json:"id" db:"id"`
	Name          string  `json:"name" db:"name"`
	Subname       string  `json:"subname" db:"subname"`
	Description   string  `json:"description" db:"description"`
	BaselinePrice float64 `json:"baselinePrice" db:"baseline_price"`
	Visibility    string  `json:"visibility" db:"visibility"`
	Duration      string  `json:"duration" db:"duration"`
	Type          string  `json:"type" db:"type"`
	Subject       *string `json:"subject,omitempty" db:"subject"`
}

// Backend: Golang implementation

type PaginatedResponse struct {
	Data        []Exam `json:"data"`
	Total       int    `json:"total"`
	CurrentPage int    `json:"currentPage"`
	TotalPages  int    `json:"totalPages"`
}

func GetAvailablePracticeExams(dbx *sqlx.DB, redis *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		const examType = "PRACTICE"
		ctx := c.Request.Context()

		// Parse pagination parameters
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		perSubjectLimit, _ := strconv.Atoi(c.DefaultQuery("limit", "5"))
		subject := c.Query("subject")
		categoryID := c.Query("category_id")

		// Ensure minimum values
		if page < 1 {
			page = 1
		}
		if perSubjectLimit < 1 {
			perSubjectLimit = 5
		}

		// Create a cache key based on the request parameters
		cacheKey := fmt.Sprintf("practice-exams:%s:%s:%d:%d",
			subject, categoryID, page, perSubjectLimit)

		// Try to get data from cache first
		cachedData, err := redis.Get(ctx, cacheKey).Result()
		if err == nil {
			// Cache hit - return the cached response
			c.Header("X-Cache", "HIT")
			c.Data(http.StatusOK, "application/json", []byte(cachedData))
			return
		}

		// Cache miss - proceed with database query
		c.Header("X-Cache", "MISS")

		// Base where clause for all queries
		var whereClause string
		var args []interface{}
		args = append(args, examType)

		if subject != "" && categoryID != "" {
			whereClause = "WHERE e.type = $1 AND e.subject = $2 AND EXISTS (SELECT 1 FROM exam_categories ec WHERE ec.exam_id = e.id AND ec.category_id = $3)"
			args = append(args, subject, categoryID)
		} else if subject != "" {
			whereClause = "WHERE e.type = $1 AND e.subject = $2"
			args = append(args, subject)
		} else if categoryID != "" {
			whereClause = "WHERE e.type = $1 AND EXISTS (SELECT 1 FROM exam_categories ec WHERE ec.exam_id = e.id AND ec.category_id = $2)"
			args = append(args, categoryID)
		} else {
			whereClause = "WHERE e.type = $1"
		}

		// Get exams with categories
		dataQuery := fmt.Sprintf(`
			WITH SubjectGroups AS (
				SELECT DISTINCT subject
				FROM available_exams e
				%s
			),
			PaginatedExams AS (
				SELECT 
					e.*,
					ROW_NUMBER() OVER (PARTITION BY e.subject ORDER BY e.id) as row_num
				FROM available_exams e
				%s
			),
			ExamWithCategories AS (
				SELECT 
					p.id, p.name, p.subname, p.description, p.baseline_price, 
					p.visibility, p.duration, p.type, p.subject, p.row_num,
					COALESCE(
						json_agg(
							json_build_object(
								'id', c.id, 
								'name', c.name
							)
						) FILTER (WHERE c.id IS NOT NULL), 
						'[]'::json
					) as categories
				FROM PaginatedExams p
				LEFT JOIN exam_categories ec ON p.id = ec.exam_id
				LEFT JOIN categories c ON ec.category_id = c.id
				GROUP BY p.id, p.name, p.subname, p.description, p.baseline_price, 
						p.visibility, p.duration, p.type, p.subject, p.row_num
			)
			SELECT 
				id, name, subname, description, baseline_price, 
				visibility, duration, type, subject, categories
			FROM ExamWithCategories
			WHERE row_num > $%d AND row_num <= $%d
			ORDER BY subject, id`,
			whereClause,
			whereClause,
			len(args)+1,
			len(args)+2,
		)

		// Calculate offset and limit for each subject
		perSubjectOffset := (page - 1) * perSubjectLimit
		perSubjectEnd := page * perSubjectLimit

		// Add pagination parameters
		args = append(args, perSubjectOffset, perSubjectEnd)

		// Count total records per subject with category filter
		countQuery := fmt.Sprintf(`
			SELECT 
				subject,
				COUNT(*) as count
			FROM available_exams e
			%s
			GROUP BY subject`,
			whereClause,
		)

		// Get counts per subject
		type SubjectCount struct {
			Subject string `db:"subject"`
			Count   int    `db:"count"`
		}
		var subjectCounts []SubjectCount
		err = dbx.Select(&subjectCounts, countQuery, args[:len(args)-2]...)
		if err != nil {
			log.Printf("Error counting practice exams per subject: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count exams"})
			return
		}

		// Calculate maximum pages needed across all subjects
		maxPages := 0
		totalExams := 0
		for _, sc := range subjectCounts {
			pages := int(math.Ceil(float64(sc.Count) / float64(perSubjectLimit)))
			if pages > maxPages {
				maxPages = pages
			}
			totalExams += sc.Count
		}

		// Define the exam struct with categories
		type ExamWithCategories struct {
			ID            string          `json:"id" db:"id"`
			Name          string          `json:"name" db:"name"`
			Subname       string          `json:"subname" db:"subname"`
			Description   string          `json:"description" db:"description"`
			BaselinePrice float64         `json:"baselinePrice" db:"baseline_price"`
			Visibility    string          `json:"visibility" db:"visibility"`
			Duration      string          `json:"duration" db:"duration"`
			Type          string          `json:"type" db:"type"`
			Subject       *string         `json:"subject,omitempty" db:"subject"`
			Categories    json.RawMessage `json:"categories" db:"categories"`
		}

		// Get paginated data
		var exams []ExamWithCategories
		err = dbx.Select(&exams, dataQuery, args...)
		if err != nil {
			log.Printf("Error querying practice exams: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve exams"})
			return
		}

		// Group exams by subject for clearer response structure
		examsBySubject := make(map[string][]ExamWithCategories)
		for _, exam := range exams {
			if exam.Subject != nil {
				subjectKey := *exam.Subject
				examsBySubject[subjectKey] = append(examsBySubject[subjectKey], exam)
			}
		}

		// Create the response
		response := struct {
			Data       map[string][]ExamWithCategories `json:"data"`
			Pagination struct {
				CurrentPage     int            `json:"currentPage"`
				TotalPages      int            `json:"totalPages"`
				PerSubjectLimit int            `json:"perSubjectLimit"`
				SubjectCounts   []SubjectCount `json:"subjectCounts"`
				TotalExams      int            `json:"totalExams"`
				CategoryID      string         `json:"categoryId,omitempty"`
			} `json:"pagination"`
		}{
			Data: examsBySubject,
			Pagination: struct {
				CurrentPage     int            `json:"currentPage"`
				TotalPages      int            `json:"totalPages"`
				PerSubjectLimit int            `json:"perSubjectLimit"`
				SubjectCounts   []SubjectCount `json:"subjectCounts"`
				TotalExams      int            `json:"totalExams"`
				CategoryID      string         `json:"categoryId,omitempty"`
			}{
				CurrentPage:     page,
				TotalPages:      maxPages,
				PerSubjectLimit: perSubjectLimit,
				SubjectCounts:   subjectCounts,
				TotalExams:      totalExams,
				CategoryID:      categoryID,
			},
		}

		// Marshal response to JSON
		jsonData, err := json.Marshal(response)
		if err != nil {
			log.Printf("Error marshaling JSON response: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create response"})
			return
		}

		// Store in cache with 5-minute expiration
		err = redis.Set(ctx, cacheKey, jsonData, 5*time.Minute).Err()
		if err != nil {
			log.Printf("Error setting cache: %v", err)
			// Continue even if caching fails
		}

		// Return the response
		c.Data(http.StatusOK, "application/json", jsonData)
	}
}
