# Stage 1: Build the Go application
FROM golang:1.23 AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy the Go module files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the Go application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o myapp .

# Stage 2: Create a lightweight image for the application
FROM alpine:3.18

# Install necessary runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy the compiled binary from the builder stage
COPY --from=builder /app/myapp .

# Create migrations directory structure and copy files
RUN mkdir -p migrations/files
COPY --from=builder /app/migrations/files/* ./migrations/files/

# Expose the port on which the app will run
EXPOSE 8080
ENV GCS_BUCKET_NAME=terang-ai-assets

# Run the Go application
CMD ["./myapp"]